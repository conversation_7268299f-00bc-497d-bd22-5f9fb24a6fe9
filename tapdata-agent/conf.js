const os = require("os");
const execP = require('child_process');
const YAML = require('yamljs');
const fs = require("fs");
const path = require('path');
const basic = require("./basic");
const CryptoJS = require("crypto-js");
const _platform = os.platform();
const _linux = (_platform === 'linux');
const _windows = (_platform === 'win32');
const _macos = (_platform === 'darwin');
const crypto = require('crypto');
let CERT_DIR = 'cert';////base
let CA_FILENAME = 'ca.pem';////mongodb
let CERTKEY_FILENAME = 'certificateKey.pem';////mongodb
let TAPDATA_HOME = process.env['DEBUG_TAPDATA_HOME'] || path.dirname(process.execPath);//process.cwd();//process.argv[1]
if(TAPDATA_HOME === "/")TAPDATA_HOME = "";
module.exports = function (yamlO, arguments) {
    let isNewSystem = false;//新装系统标记
    let WORK_DIR = '';
    if(_windows){
        checkBat(TAPDATA_HOME);
    }
    for (let i = 0; i < arguments.length; i++) {
        if (arguments[i] === '--workDir') {
            WORK_DIR = arguments[i + 1];
            if(WORK_DIR.substring(WORK_DIR.length-1,WORK_DIR.length) === "/" || WORK_DIR.substring(WORK_DIR.length-1,WORK_DIR.length) === "\\"){
                WORK_DIR = WORK_DIR.substring(0,WORK_DIR.length-1);
            }
            try {
                if (fs.existsSync(path.join(TAPDATA_HOME, '.workDir'))) {
                    fs.unlinkSync(path.join(TAPDATA_HOME, '.workDir'));
                }
                fs.writeFileSync(path.join(TAPDATA_HOME, '.workDir'), WORK_DIR, 'utf8');
            }catch(e){
                console.error('Write file error,please check your permitted.')
            }
            break;
        }
    }
    if (!WORK_DIR || WORK_DIR === '') {
        if(fs.existsSync(path.join(TAPDATA_HOME,'.workDir'))){
            WORK_DIR = fs.readFileSync(path.join(TAPDATA_HOME,'.workDir'),'utf8').toString();
        }
    }
    if (!WORK_DIR || WORK_DIR === '') {
        WORK_DIR = path.dirname(process.execPath); //os.homedir();
        /*
        if (_linux)
            WORK_DIR += "/.tapdata";
        if (_windows)
            WORK_DIR += "\\.tapdata";
         */
    }
    console.info("WORK DIR:"+WORK_DIR);
    let SCRIPT_DIR = TAPDATA_HOME + "/etc";
//文件是否存在

    let yamlObj;
    try {
        let fileName = WORK_DIR;
        if (_linux || _macos)
            fileName += "/application.yml";
        if (_windows)
            fileName += "\\application.yml";
        fs.accessSync(fileName, fs.constants.F_OK);
        if (!yamlO) {
            const data = fs.readFileSync(WORK_DIR + '/application.yml');
            yamlObj = YAML.parse(data.toString());
            if(yamlObj === null)yamlObj = {};
        } else {
            yamlObj = yamlO;
        }
    } catch (e) {
        try {
            fs.accessSync(WORK_DIR, fs.constants.F_OK);
        } catch (e) {
            fs.mkdirSync(WORK_DIR);
            let cert = WORK_DIR + "/" + CERT_DIR + "/";
            cert = basic.getExecStr({"_windows": _windows}, cert);
            fs.mkdirSync(cert);
        }
        let to = WORK_DIR + "";
        let from = SCRIPT_DIR;
        //if (_linux) {
        to += "/application.yml";
        from += '/application.yml';
        if (_windows) {
            to = to.replace(/\//g, "\\");
            from = from.replace(/\//g, "\\");
        }
        try {
            fs.copyFileSync(from, to);
            const data = fs.readFileSync(WORK_DIR + '/application.yml');
            yamlObj = YAML.parse(data.toString());
            isNewSystem = true;
        } catch (e) {
            try {
                fs.appendFileSync(to, '');
                yamlObj = {};
            } catch (err) {
                console.error(e);
            }
        }
    }
//文件夹是否存在
    let filename = WORK_DIR;
    if (_linux || _macos) {
        filename += "/logs";
    }
    if (_windows) {
        filename += "\\logs";
    }
    try {
        fs.accessSync(filename, fs.constants.F_OK);
    } catch (e) {
        fs.mkdirSync(filename);
    }
    //检验后台文件
    let GodExe = '';
    path.join(TAPDATA_HOME, 'tapdata-agent');
    if (_windows) {
        GodExe += path.join(TAPDATA_HOME, 'tapdata-agent.exe');
    } else if (_linux || _macos) {
        GodExe += path.join(TAPDATA_HOME, 'tapdata-agent');
    }
    let baseExe = '';
    if (_windows) {
        baseExe += path.join(TAPDATA_HOME, 'tapdata.exe');
    } else if (_linux || _macos) {
        baseExe += path.join(TAPDATA_HOME, 'tapdata');
    }
    try {
        fs.accessSync(GodExe, fs.constants.F_OK);
    } catch (e) {
        let from = process.execPath;//path.join(TAPDATA_HOME, 'tapdata');
        /*
        if (_windows) {
            from += '.exe';
        }
         */
        try {
            fs.accessSync(baseExe, fs.constants.F_OK);
        } catch (e) {
            try {
                fs.copyFileSync(from, baseExe);
            } catch (e) {
                console.info(e);
                return;
            }
        }
        try {
            fs.copyFileSync(from, GodExe);
            let stdio = ['ignore', 'ignore', 'ignore'];
            if (_windows) {
                let subProcess1 = require('child_process').spawn('attrib', ['+h', GodExe], {
                    'stdio': stdio,
                    'detached': true,
                    'windowsHide': true
                });
                subProcess1.unref();
            }
        } catch (e) {
            console.info(e);
            return;
        }
    }
    if(yamlObj['tapdata'] && yamlObj['tapdata']['cloud'] && yamlObj['tapdata']['cloud']['token'] && yamlObj['tapdata']['cloud']['token'] !== '' && isEmpty(yamlObj['tapdata']['conf'])){
        yamlObj['tapdata']['conf'] = {};
        yamlObj['tapdata']['conf']['tapdataPort'] = '3030';
        yamlObj['tapdata']['conf']['backendUrl'] = 'https://cloud.tapdata.net/console/v3/tm/api/';
        yamlObj['tapdata']['conf']['apiServerPort'] = '';
        yamlObj['tapdata']['conf']['reportInterval'] = 20000;
        yamlObj['tapdata']['conf']['tapdataJavaOpts'] = '';
        yamlObj['spring'] = {data:{mongodb:{}}};
        yamlObj['spring']['data']['mongodb']['username'] = '';
        yamlObj['spring']['data']['mongodb']['password'] = '';
        yamlObj['spring']['data']['mongodb']['mongoConnectionString'] = '';
        yamlObj['spring']['data']['mongodb']['uri'] = '';
        yamlObj['spring']['data']['mongodb']['ssl'] = '';
        yamlObj['spring']['data']['mongodb']['sslCA'] = '';
        yamlObj['spring']['data']['mongodb']['sslCertKey'] = '';
        yamlObj['spring']['data']['mongodb']['sslPEMKeyFilePassword'] = '';
        yamlObj['spring']['data']['mongodb']['authenticationDatabase'] = '';
    }
    if (isEmpty(yamlObj['tapdata']) ||
        isEmpty(yamlObj['tapdata']['conf']) ||
        isEmpty(yamlObj['tapdata']['conf']['tapdataPort']) ||
        isEmpty(yamlObj['tapdata']['conf']['backendUrl'])) {
        //let initObj = require("./init/init");
        if(isEmpty(yamlObj['tapdata']) ){
            yamlObj['tapdata']={};
            yamlObj['spring'] = {data:{mongodb:{}}};
            yamlObj['tapdata']['cloud']={accessCode:'',retryTime:'3',baseURLs:''};
        }
        yamlObj['tapdata']['conf'] = {};
        yamlObj['tapdata']['conf']['tapdataPort'] = '';
        yamlObj['tapdata']['conf']['backendUrl'] = '';
        yamlObj['tapdata']['conf']['apiServerPort'] = '';
        yamlObj['spring']['data']['mongodb']['username'] = '';
        yamlObj['spring']['data']['mongodb']['password'] = '';
        yamlObj['spring']['data']['mongodb']['mongoConnectionString'] = '';
        yamlObj['spring']['data']['mongodb']['uri'] = '';
        yamlObj['spring']['data']['mongodb']['ssl'] = '';
        yamlObj['spring']['data']['mongodb']['sslCA'] = '';
        yamlObj['spring']['data']['mongodb']['sslCertKey'] = '';
        yamlObj['spring']['data']['mongodb']['sslPEMKeyFilePassword'] = '';
        yamlObj['spring']['data']['mongodb']['authenticationDatabase'] = '';
        /*
        let yamlStr = YAML.stringify(yamlObj,5);
        fs.writeFileSync(WORK_DIR+'/application.yml',yamlStr);
        console.info("系统初始化完成");
         */
    }
    //初始化默认报送间隔和uuid
    if(!basic.isEmpty(yamlObj['tapdata']['conf']['backendUrl']) && (basic.isEmpty(yamlObj['tapdata']['conf']['reportInterval']) || basic.isEmpty(yamlObj['tapdata']['conf']['uuid']))){
        if(basic.isEmpty(yamlObj['tapdata']['conf']['reportInterval']))yamlObj['tapdata']['conf']['reportInterval'] = 20000;
        if(basic.isEmpty(yamlObj['tapdata']['conf']['uuid']))yamlObj['tapdata']['conf']['uuid'] = require('uuid/v4')();
        let yamlStr = YAML.stringify(yamlObj,5);
        fs.writeFileSync(WORK_DIR+'/application.yml',yamlStr);
    }
    if( yamlObj['tapdata']['cloud']['token'] &&
        yamlObj['tapdata']['cloud']['token'] !== '' &&
        (
            !yamlObj['tapdata']['conf']['tapdataJavaOpts'] ||
            (
                yamlObj['tapdata']['conf']['tapdataJavaOpts'] &&
                !yamlObj['tapdata']['conf']['tapdataJavaOpts'].includes("-Xmx")
            )
        )){
        yamlObj['tapdata']['conf']['tapdataJavaOpts'] = yamlObj['tapdata']['conf']['tapdataJavaOpts'] || "";
        let mem = (os.totalmem() / 1024 / 1024 / 5 * 3).toFixed(0);
        let util = 'M';
        if(mem * 1 > 8192){
            mem = '8192'
        }
        yamlObj['tapdata']['conf']['tapdataJavaOpts'] = (yamlObj['tapdata']['conf']['tapdataJavaOpts'] + " -Xmx" + mem + util).trim();
        let yamlStr = YAML.stringify(yamlObj,5);
        fs.writeFileSync(WORK_DIR+'/application.yml',yamlStr);
    }

    if(!basic.isEmpty(yamlObj['tapdata']['conf']['backendUrl']) && basic.isEmpty(yamlObj['tapdata']['conf']['Decimal128ToNumber'])){
        yamlObj['tapdata']['conf']['Decimal128ToNumber'] = 'false';
        let yamlStr = YAML.stringify(yamlObj,5);
        fs.writeFileSync(WORK_DIR+'/application.yml',yamlStr);
    }

    if (!yamlObj['tapdata']['conf']['tapdataTMJavaOpts'] ||
            (
                yamlObj['tapdata']['conf']['tapdataTMJavaOpts'] &&
                !yamlObj['tapdata']['conf']['tapdataTMJavaOpts'].includes("-Xmx")
            )
        ) {
        yamlObj['tapdata']['conf']['tapdataTMJavaOpts'] = yamlObj['tapdata']['conf']['tapdataTMJavaOpts'] || "";
        yamlObj['tapdata']['conf']['tapdataTMJavaOpts'] = yamlObj['tapdata']['conf']['tapdataTMJavaOpts'] + " -Xmx4G";
        if(!yamlObj['tapdata']['conf']['tapdataTMJavaOpts'].includes("-Xms")){
            yamlObj['tapdata']['conf']['tapdataTMJavaOpts'] = yamlObj['tapdata']['conf']['tapdataTMJavaOpts'] + " -Xms4G";
        }
        yamlObj['tapdata']['conf']['tapdataTMJavaOpts'] = yamlObj['tapdata']['conf']['tapdataTMJavaOpts'].trim();
        let yamlStr = YAML.stringify(yamlObj,5);
        if(yamlObj['spring']['data']['mongodb']['mongoConnectionString'] &&
            yamlObj['spring']['data']['mongodb']['mongoConnectionString'] !== "") {
            fs.writeFileSync(WORK_DIR + '/application.yml', yamlStr);
        }
    }

    if (!basic.isEmpty(yamlObj['spring']['data']['mongodb']['password'])) {
        yamlObj['spring']['data']['mongodb']['password'] = CryptoJS.RC4.decrypt(yamlObj['spring']['data']['mongodb']['password'], basic.getKey()).toString(CryptoJS.enc.Utf8);
    }
    if (!basic.isEmpty(yamlObj['spring']['data']['mongodb']['sslPEMKeyFilePassword'])) {
        yamlObj['spring']['data']['mongodb']['sslPEMKeyFilePassword'] = CryptoJS.RC4.decrypt(yamlObj['spring']['data']['mongodb']['sslPEMKeyFilePassword'], basic.getKey()).toString(CryptoJS.enc.Utf8);
    }

    let FRONTEND_DIR_NAME = 'tapdata-management';////base
    let NODE_HOME = path.join(TAPDATA_HOME,"lib","NDK","node");
    let nodeFileName = _windows ? 'node.exe' : 'node';
    if (!fs.existsSync(path.join(NODE_HOME, nodeFileName))){
        if (fs.existsSync(path.join(NODE_HOME, 'bin', nodeFileName))) {
            NODE_HOME = path.join(NODE_HOME, 'bin');
        }
    }
    let JAVA_BIN = path.join(TAPDATA_HOME,"lib","jdk","bin");
    let _NODE_EXEC = NODE_HOME + "/node";
    let COMPONENTS_DIR = path.join(TAPDATA_HOME , "components");////base
    let FRONTEND_HOME = COMPONENTS_DIR + "/" + FRONTEND_DIR_NAME;
    //if (isEmpty(yamlObj['tapdata']['conf']['AGENT_JAR_FILE'])) yamlObj['tapdata']['conf']['AGENT_JAR_FILE'] = 'tapdata-agent-v1.5.30-0-g0a320db.jar';
    //let AGENT_JAR_FILE = yamlObj['tapdata']['conf']['AGENT_JAR_FILE'];////base
    let AGENT_JAR_FILE = getAgentJar(COMPONENTS_DIR);
    let CONF_SH_FILE = WORK_DIR + '/tapdata.conf';////base
//java heap size

// MaxTenuringThreshold default 15
//TAPDATA_JAVA_OPTS=TAPDATA_JAVA_OPTS + " -XX:MaxTenuringThreshold=2";
// ResizePLAB,default:true
//TAPDATA_JAVA_OPTS=TAPDATA_JAVA_OPTS + " -XX:-ResizePLAB";

    let userLanguage;
//获取操作系统语言
    /*
    getLangues = {
        '_windows':{"code":'reg query "hklm\\system\\controlset001\\control\\nls\\language" /v Installlanguage',"msg":"000000"},
        '_linux':{"code":'echo $LANG',"msg":"000000"}
    };
     */
    let str = "";
    if (_linux || _macos)
        str = "echo $LANG";
    else if (_windows)
        str = 'reg query "hklm\\system\\controlset001\\control\\nls\\language" /v Installlanguage';
    let lan = execSync(str).toString();
    if (_windows) {
        let languageCode = lan.replace(/\\s*/g, "").split("REG_SZ")[1];
        if (languageCode === '0804') {
            userLanguage = 0;
        } else if (languageCode === '0409') {
            userLanguage = 1;
        }
    } else if (_linux) {
        let languageCode = lan.replace(/\\s*/g, "").split(".")[0];
        if (languageCode === 'zh_CN') {
            userLanguage = 0;
        } else if (languageCode === 'en_US') {
            userLanguage = 1;
        }
    }
    /*
    process.env['TAPDATA_PORT'] = yamlObj['tapdata']['conf']['tapdataPort'];
    process.env['TAPDATA_HOST'] = yamlObj['tapdata']['conf']['backendUrl'];
    process.env['TAPDATA_WORK_DIR'] = WORK_DIR;
    process.env['TAPDATA_ORIGIN'] = yamlObj['tapdata']['conf']['backendUrl'].split(',')[0].replace("/api/","").replace("/api","");
     */
    let MONGO_SSL_CA = '';
    let MONGO_SSL_CERT_KEY = '';
    if("true" === yamlObj['spring']['data']['mongodb']['ssl'] && !basic.isEmpty(yamlObj['spring']['data']['mongodb']['sslCA'])){
        try {
            MONGO_SSL_CA = fs.readFileSync(path.join(WORK_DIR + "/" + CERT_DIR + "/" + CA_FILENAME));
            MONGO_SSL_CERT_KEY = fs.readFileSync(path.join(WORK_DIR + "/" + CERT_DIR + "/" + CERTKEY_FILENAME));
        }catch (e) {
            console.info(e);
            return;
        }
    }
    let FRONTEND_WORKER_COUNT = process.env['FRONTEND_WORKER_COUNT'];
    let frontend_worker_count = FRONTEND_WORKER_COUNT ? FRONTEND_WORKER_COUNT : "";
    if (yamlObj['tapdata']['conf']["frontendWorkerCount"] && yamlObj['tapdata']['conf']["frontendWorkerCount"] !== "") {
        try {
            frontend_worker_count = parseInt(yamlObj['tapdata']['conf']["frontendWorkerCount"]);
        } catch (error) {
            console.error(error);
        }
    }
    let API_WORKER_COUNT = process.env['API_WORKER_COUNT'];
    let api_worker_count = API_WORKER_COUNT ? API_WORKER_COUNT : "";
    if (yamlObj['tapdata']['conf']["apiWorkerCount"] && yamlObj['tapdata']['conf']["apiWorkerCount"] !== "") {
        try {
            api_worker_count = parseInt(yamlObj['tapdata']['conf']["apiWorkerCount"]);
        } catch (error) {
            console.error(error);
        }
    }
    let appType = 'DAAS';
    if(yamlObj['tapdata']['cloud']['token'] && yamlObj['tapdata']['cloud']['token'] !== ''){
        let key = '5fa25b06ee34581d';
        let iv = '5fa25b06ee34581d';
        let token = decrypt(key, iv, yamlObj['tapdata']['cloud']['token']);
        token = JSON.parse(token);
        yamlObj['tapdata']['cloud']['process_id'] = token.process_id;
        yamlObj['tapdata']['cloud']['accessCode'] = token.cloud_accessCode;
        yamlObj['tapdata']['cloud']['isCloud'] = token.isCloud;
        yamlObj['tapdata']['cloud']['username'] = token.user_id;
        yamlObj['tapdata']['conf']['backendUrl'] = token.backend_url;
        yamlObj['tapdata']['cloud']['jobTags'] = token.jobTags;
        yamlObj['tapdata']['cloud']['version'] = token.version;
        yamlObj['tapdata']['cloud']['accessKey'] = token.accessKey || '';
        yamlObj['tapdata']['cloud']['secretKey'] = token.secretKey || '';
        if(token.spec && token.spec.memory ){
            let javaOptsArrNew = [];
            const javaOptsArr = yamlObj['tapdata']['conf']['tapdataJavaOpts'].split(' ');
            for(let x in javaOptsArr){
                if(!javaOptsArr[x].includes('-Xmx')){
                    javaOptsArrNew.push(javaOptsArr[x])
                }
            }
            let mem = (os.totalmem() / 1024 / 1024 / 5 * 4).toFixed(0);
            let util = 'G';
            if(mem * 1 < token.spec.memory * 1024){
                token.spec.memory = mem;
                util = 'M';
            }
            yamlObj['tapdata']['conf']['tapdataJavaOpts'] = javaOptsArrNew.join(' ');
            yamlObj['tapdata']['conf']['tapdataJavaOpts'] = (yamlObj['tapdata']['conf']['tapdataJavaOpts'] + " -Xmx" + token.spec.memory + util).trim();
        }
        appType = "DFS"
    }
    if (isEmpty(yamlObj['tapdata']['conf']['tapdataJavaOpts'])) yamlObj['tapdata']['conf']['tapdataJavaOpts'] = '';
    if (isEmpty(yamlObj['tapdata']['conf']['tapdataTMJavaOpts'])) yamlObj['tapdata']['conf']['tapdataTMJavaOpts'] = '';
    let TAPDATA_JAVA_OPTS = yamlObj['tapdata']['conf']['tapdataJavaOpts'];//""-Xmx8g -Xms8g" ////java
    let TAPDATA_TM_JAVA_OPTS = yamlObj['tapdata']['conf']['tapdataTMJavaOpts'];
    process.env['TAPDATA_PORT'] = yamlObj['tapdata']['conf']['tapdataPort'];
    process.env['TAPDATA_HOST'] = yamlObj['tapdata']['conf']['backendUrl'];
    process.env['TAPDATA_WORK_DIR'] = WORK_DIR;
    process.env['TAPDATA_ORIGIN'] = yamlObj['tapdata']['conf']['backendUrl'].split(',')[0].replace("/api/","").replace("/api","");
    let _docker = false;
    if(_linux || _macos){
        _docker = isDocker();
    }
    if (fs.existsSync(JAVA_BIN)) {
        process.env.PATH = JAVA_BIN + (_windows ? ';' : ':') + process.env.PATH;
    }
    return {
        'FRONTEND_DIR_NAME': FRONTEND_DIR_NAME,
        'TAPDATA_HOME': TAPDATA_HOME,
        'NODE_HOME': NODE_HOME,
        '_NODE_EXEC': _NODE_EXEC,
        'FRONTEND_HOME': FRONTEND_HOME,
        'COMPONENTS_DIR': COMPONENTS_DIR,
        'AGENT_JAR_FILE': AGENT_JAR_FILE,
        'SCRIPT_DIR': SCRIPT_DIR,
        'WORK_DIR': WORK_DIR,
        'CONF_SH_FILE': CONF_SH_FILE,
        'TAPDATA_JAVA_OPTS': TAPDATA_JAVA_OPTS,
        'TAPDATA_TM_JAVA_OPTS': TAPDATA_TM_JAVA_OPTS,
        'CERT_DIR': CERT_DIR,
        'CA_FILENAME': CA_FILENAME,
        'CERTKEY_FILENAME': CERTKEY_FILENAME,
        '_linux': _linux,
        '_windows': _windows,
        '_macos': _macos,
        '_docker': _docker,
        'userLanguage': userLanguage,
        'tapdata_port': yamlObj['tapdata']['conf']['tapdataPort'],
        'backend_url': yamlObj['tapdata']['conf']['backendUrl'],
        'api_server_port': yamlObj['tapdata']['conf']['apiServerPort'],
        'mongodbDeployType': yamlObj['tapdata']['conf']['mongodbDeployType'],
        'mongodbPort': yamlObj['tapdata']['conf']['mongodbPort'],
        'mongo_username': yamlObj['spring']['data']['mongodb']['username'],
        'mongo_password': yamlObj['spring']['data']['mongodb']['password'],
        'mongoConnectionString': yamlObj['spring']['data']['mongodb']['mongoConnectionString'],
        'uri': constructMetaDBURI(yamlObj),//yamlObj['spring']['data']['mongodb']['uri'],
        'ssl': yamlObj['spring']['data']['mongodb']['ssl'].toString(),
        'mongo_sslCAFile': yamlObj['spring']['data']['mongodb']['sslCA'],
        'mongo_sslPEMKeyFile': yamlObj['spring']['data']['mongodb']['sslCertKey'],
        'mongo_sslPEMKeyFilePassword': yamlObj['spring']['data']['mongodb']['sslPEMKeyFilePassword'],
        'mongo_authenticationDatabase': yamlObj['spring']['data']['mongodb']['authenticationDatabase'],
        'mongo_conn': constructMetaDBSSLParams(constructMetaDBURI(yamlObj), yamlObj),
        'cloud_accessCode': yamlObj['tapdata']['cloud']['accessCode'],
        'cloud_retryTime': yamlObj['tapdata']['cloud']['retryTime'],
        'cloud_baseURLs': yamlObj['tapdata']['cloud']['baseURLs'],
        'cloud_username': yamlObj['tapdata']['cloud']['username']?yamlObj['tapdata']['cloud']['username']:'',
        'cloud_token': yamlObj['tapdata']['cloud']['token'],
        'mode': yamlObj['tapdata']['mode'],
        'isNewSystem':isNewSystem,
        'uuid':yamlObj['tapdata']['conf']['uuid'],
        'reportInterval':yamlObj['tapdata']['conf']['reportInterval'],
        'MONGO_SSL_CA':MONGO_SSL_CA,
        'MONGO_SSL_CERT_KEY':MONGO_SSL_CERT_KEY,
        'frontend_worker_count':frontend_worker_count,
        'api_worker_count':api_worker_count,
        'process_id':yamlObj['tapdata']['cloud']['process_id'] || "",
        'isCloud':yamlObj['tapdata']['cloud']['isCloud'],
        'jobTags':yamlObj['tapdata']['cloud']['jobTags'],
        'version':yamlObj['tapdata']['cloud']['version'],
        'appType':appType,
        'accessKey':yamlObj['tapdata']['cloud']['accessKey'],
        'secretKey':yamlObj['tapdata']['cloud']['secretKey'],
        'apiServerOptions': yamlObj['tapdata']['conf']['apiServerOptions'],
        'Decimal128ToNumber': yamlObj['tapdata']['conf']['Decimal128ToNumber'],
        'apiServerErrorCode': yamlObj['tapdata']['conf']['apiServerErrorCode'],
        'sslCAPath':path.join(WORK_DIR,CERT_DIR,CA_FILENAME),
        'sslCertKeyPath':path.join(WORK_DIR,CERT_DIR,CERTKEY_FILENAME)
    };
};

function hasDockerEnv() {
    try {
        fs.statSync('/.dockerenv');
        return true;
    } catch (_) {
        return false;
    }
}
function hasDockerCGroup() {
    try {
        return fs.readFileSync('/proc/self/cgroup', 'utf8').includes('docker');
    } catch (_) {
        return false;
    }
}
function isDocker(){
    return hasDockerEnv() || hasDockerCGroup()
}

function decrypt(key, iv, crypted) {
    crypted = new Buffer.from(crypted, 'base64').toString('binary');
    var decipher = crypto.createDecipheriv('aes-128-cbc', key, iv);
    var decoded = decipher.update(crypted, 'binary', 'utf8');
    decoded += decipher.final('utf8');
    let rs = new Buffer.from(decoded,'base64').toString()
    return rs;
};

function execSync(execString) {
    try {
        return execP.execSync(execString).toString()
    } catch (e) {
        console.error(e);
        return '';
    }
}

function constructMetaDBURI(yamlObj) {
    let uri = yamlObj['spring']['data']['mongodb']['mongoConnectionString'];
    if(basic.isEmpty(uri))return '';
    if (uri.indexOf('mongodb://') < 0) {
        uri = 'mongodb://' + uri;
    }
    if (!isEmpty(yamlObj['spring']['data']['mongodb']['username']) &&
        !isEmpty(yamlObj['spring']['data']['mongodb']['password']) &&
        !isEmpty(yamlObj['spring']['data']['mongodb']['authenticationDatabase'])) {
        let encodeUsername = encodeURIComponent(yamlObj['spring']['data']['mongodb']['username']);
        let encodePassword = encodeURIComponent(yamlObj['spring']['data']['mongodb']['password']);
        uri = uri.replace("mongodb://", "mongodb://" + encodeUsername + ":" + encodePassword + '@');
        if (uri.indexOf('?') > 0) {
            uri += '&authSource=' + yamlObj['spring']['data']['mongodb']['authenticationDatabase']
        } else {
            uri += '?authSource=' + yamlObj['spring']['data']['mongodb']['authenticationDatabase']
        }
    }
    return uri;
}

function isEmpty(obj) {
    return typeof obj === "undefined" || obj === null || obj === "";
}

function constructMetaDBSSLParams(uri, obj) {
    let mongo_conn = uri;//obj['spring']['data']['mongodb']['uri'];
    if (!isEmpty(mongo_conn)) {
        if (!isEmpty(obj['spring']['data']['mongodb']['sslCertKey'])) {
            mongo_conn += " --ssl --sslAllowInvalidHostnames --sslAllowInvalidCertificates --sslPEMKeyFile " + obj['spring']['data']['mongodb']['sslCertKey'];
        }
        if (!isEmpty(obj['spring']['data']['mongodb']['sslCertKey']) && !isEmpty(obj['spring']['data']['mongodb']['sslPEMKeyFilePassword'])) {
            mongo_conn += " --sslPEMKeyPassword " + obj['spring']['data']['mongodb']['sslPEMKeyFilePassword'];
        }
        if (!isEmpty(obj['spring']['data']['mongodb']['sslCA'])) {
            mongo_conn += " --sslCAFile " + obj['spring']['data']['mongodb']['sslCA'];
        }
    }
    return mongo_conn;
}

function getAgentJar(COMPONENTS_DIR) {
    try {
        let arr = fs.readdirSync(COMPONENTS_DIR);
        for (let x in arr) {
            if (/tapdata-agent+[\s\S]*?\.jar$/.test(arr[x])) {
                return arr[x];
            }
        }
    }catch(e){
        return '';
    }
}
/*
function getUuid(tapdata_work_dir){
    const fs = require('fs');
    const path = require('path');
    const uuidv4 = require('uuid/v4');
    const uuidjs = path.join(tapdata_work_dir,'uuid.js');
    let uuid;
    if (fs.existsSync(uuidjs)) {
        // 1. 从缓存文件加载
        uuid = require(uuidjs);
    } else {
        // 2. 没有缓存的，生成缓存，并赋值
        uuid = uuidv4();
        let uuid2f = 'module.exports = "'+uuid+'";\n';
        fs.writeFileSync(uuidjs, uuid2f);//`${appConfig.cfg_dir}/uuid.js`
    }
    return uuid;
}
 */

function checkBat(TAPDATA_HOME){
    if (!fs.existsSync(path.join(TAPDATA_HOME, 'start.bat'))) {
        fs.writeFileSync(path.join(TAPDATA_HOME, 'start.bat'), 'tapdata start backend\npause', 'utf8');
    }
    if (!fs.existsSync(path.join(TAPDATA_HOME, 'stop.bat'))) {
        fs.writeFileSync(path.join(TAPDATA_HOME, 'stop.bat'), 'tapdata stop -f\npause', 'utf8');
    }
    if (!fs.existsSync(path.join(TAPDATA_HOME, 'status.bat'))) {
        fs.writeFileSync(path.join(TAPDATA_HOME, 'status.bat'), 'tapdata status\npause', 'utf8');
    }
}

function getJdkPath() {

}