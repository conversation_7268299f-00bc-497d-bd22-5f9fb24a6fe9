spring:
    data:
        mongodb:
            uri: ""
            ssl: 'false'
            sslCA: ""
            sslCertKey: ""
            conn_params: ""
            username: ""
            password: ""
            mongoConnectionString: '127.0.0.1:27017/tapdata'
            sslPEMKeyFilePassword: U2FsdGVkX1+mndt/qnglPQ==
            authenticationDatabase: admin
tapdata:
    cloud:
        accessCode: '<ACCESS_CODE>'
        retryTime: 3
        baseURLs: 'http://127.0.0.1:3030/api/'
    mode: cluster
    conf:
        tapdataPort: '3030'
        backendUrl: 'http://127.0.0.1:3030/api/'
        apiServerPort: '3080'
        tapdataJavaOpts: ""
        SCRIPT_DIR: etc
        reportInterval: 5000
        uuid: 568c765b-9bc0-4950-ba24-46fd48884980
