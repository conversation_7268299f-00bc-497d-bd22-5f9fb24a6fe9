Configutation:
  name: Default

  #  Properties:
  #    Property:
  #      - name: log-path
  #        value: "logs"
  #      - name: level
  #        value: INFO

  Appenders:

    Console:
      name: Console_Appender
      target: SYSTEM_OUT
      PatternLayout:
        pattern: "[%-5level] %d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %c{1} - %msg%n"

#    RollingFile:
#      - name: RollingFile_Appender
#        fileName: tapdata-server.log
#        filePattern: tapdata-server-$${date:yyyyMMdd}.log
#        PatternLayout:
#          pattern: "[%-5level] %d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %c{1} - %msg%n"
#        Policies:
#          OnStartupTriggeringPolicy:
#            minSize: 10 MB
#          SizeBasedTriggeringPolicy:
#            size: 150 MB
#        DefaultRollOverStrategy:
#          max: 30

#  Loggers:
#
#      Root:
#        level: ${level}
#        AppenderRef:
#          - ref: Console_Appender
#          - ref: RollingFile_Appender
#
#      Logger:
#        - name: com
#          level: ${level}
#          AppenderRef:
##              ref: RollingFile_Appender
#              level: debug
