const checkEnv = require('./checkEnv');
const basic = require('../basic');
const tapdataManagement = require('./tapdataManagement');
//const backend = require('./backend');
const apiserver = require('./apiserver');
const OsMonitor = require('./OsMonitor');
const prompts = require('prompts');
const getProcesses = require("getprocesses");
const msg = require('../sendMessage');
const { MONGODB_PROCESS_NAME } = require('./mongodb');
let arg;
let thisConf = "";
module.exports = function (arguments, conf, isGod) {//conf
    arg = arguments;
    thisConf = conf;
    if (arguments[0] === 'start') {//启动

        //校验环境
        let EventEmitter = require('events').EventEmitter;
        let life = new EventEmitter();
        life.on("checked", function () {
            (async () => {
                console.log('Do start application')
                startFun(arguments, conf, isGod);
            })();
        });
        checkEnv(life,arguments,conf);
    } else if (arguments[0] === 'stop') {//停止
        return stopFun(arguments, conf);
    } else if (arguments[0] === 'status') {//状态显示
        statusFun(arguments, conf);
    } else if (arguments[0] === 'restart') {
        stopFun(arguments, conf, true);//开始重新启动
    } else if (arguments[0] === 'init') {//系统初始化
        const init = require("../init/init").init;
        init(arguments,conf);
    } else if (arguments[0] === 'resetPassword' || arguments[0] === 'resetpassword') {
        const initPassword = require("../init/initPassword");
        initPassword(arguments,conf);
    } else if (arguments[0] === 'help' || arguments[0] === '?' || arguments[0] === "h") {//帮助信息
        basic.printHelp();
    } else if (arguments[0] === 'register') {//停止
        registerConn(arguments, conf);
    } else {
        //打印帮助信息
        basic.printHelp();
    }
};

function registerConn(arguments, conf) {//isRestart
    arguments = arg;
    conf = thisConf;
    msg(JSON.stringify(arguments), conf);
}

function startFun(arguments, conf) {//isGod
    arguments = arg;
    conf = thisConf;
    if (arguments[1] === 'frontend') {//启动前台系统
        (async () => {
            const initMongo = require("../init/mongoInit");
            if (!await initMongo(null, thisConf, arguments)) {
                return;
            }
            let list;
            if (conf._windows) {
                list = await require("../ps_win").getProcessesWindows();
            }else {
                list = await getProcesses.getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
            }
            //let list = await getProcesses.getProcesses();// snapshot('pid', 'name', 'path', 'cmdline');
            let havePid = false;
            for (let i = 0; i < list.length; i++) {
                //let checkStr = conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME;// conf.TAPDATA_HOME + "/node_modules/pm2";//
                let checkStr = conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME + "/server/index.js";
                if (conf._windows) {
                    //checkStr = conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME + "/node_modules/pm2/lib/ProcessContainer.js";
                    checkStr = checkStr.replace(/\//g, '\\');
                }
                if (list[i].arguments.join(' ').indexOf(checkStr) >= 0) {
                    havePid = true;
                    if (basic.isDebug(arguments)) {
                        console.info("frontend is started.We are not need start frontend.");
                    }
                    break;
                }
            }
            if (!havePid) {//basic.isEmpty(status.tapdataManagement(conf))
                await tapdataManagement.doStartFrontend(conf, arguments);
                msg(JSON.stringify(arguments), conf);
            }
        })();
    } else if (arguments[1] === 'backend' || thisConf.backend_url.indexOf('https://cloud.tapdata.net')>-1 || conf.appType === 'DFS') {//启动后台系统
        (async () => {
            if(arguments[1] !== 'backend')arguments.splice(1, 0, "backend");
            let list;
            if (conf._windows) {
                list = await require("../ps_win").getProcessesWindows();
            }else {
                list = await getProcesses.getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
            }
            //let list = await getProcesses.getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
            let havePid = false;
            let checkBackStr = conf.COMPONENTS_DIR + "/tapdata-agent";
            if (conf._windows) {
                checkBackStr = checkBackStr.replace(/\//g, '\\');
            }
            for (let i = 0; i < list.length; i++) {
                if (list[i].arguments.join(' ').indexOf(checkBackStr) >= 0) {
                    havePid = true;
                    if (conf.showBackendCheck !== false) {
                        console.info("backend is started.We are not need start backend.");
                    }
                }
            }
            if (!havePid) {
                //if (basic.isEmpty(status.backendProcess(conf))) {
                //backend.doStartBackend(conf, arguments);
                msg(JSON.stringify(arguments), conf);
            }else{
                if(conf._windows && conf.showBackendCheck !== false) {
                    basic.exit().then();
                }
            }
        })();
    } else if (arguments[1] === 'apiserver') {//启动API系统
        (async () => {
            //let list = await getProcesses.getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
            let list;
            if (conf._windows) {
                list = await require("../ps_win").getProcessesWindows();
            }else {
                list = await getProcesses.getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
            }
            let havePid = false;
            let checkMApiSrt = conf.COMPONENTS_DIR + "/apiserver/index";
            if (conf._windows) {
                checkMApiSrt = checkMApiSrt.replace(/\//g, '\\');
            }
            for (let i = 0; i < list.length; i++) {
                if (list[i].arguments.join(' ').indexOf(checkMApiSrt) >= 0) {
                    havePid = true;
                    if (basic.isDebug(arguments)) {
                        console.info("apiserver is started.We are not need start apiserver.");
                    }
                }
            }
            if (!havePid) {
                //if (basic.isEmpty(status.apiserverProcess(conf))) {
                await apiserver.doStartAPI(conf, arguments);
                msg(JSON.stringify(arguments), conf);
            }
        })();
    } else if (arguments[1] === 'os-monitor') {//启动os-monitor系统
        (async () => {
            let list = await getProcesses.getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
            let havePid = false;
            let checkOsSrt = conf.COMPONENTS_DIR + "/os-monitor";
            if (conf._windows) {
                checkOsSrt = checkOsSrt.replace(/\//g, '\\');
            }
            for (let i = 0; i < list.length; i++) {
                if (list[i].arguments.join(' ').indexOf(checkOsSrt) >= 0) {
                    havePid = true;
                    if (basic.isDebug(arguments)) {
                        console.info("os-monitor is started.We are not need start os-monitor.");
                    }
                }
            }
            if (!havePid) {
                //if (basic.isEmpty(status.osmonitorProcess(conf))) {
                OsMonitor.startOsMonitor(conf, arguments);
            }
        })();
    } else if (arguments[1] === MONGODB_PROCESS_NAME) { // 启动MongoDB
        msg(JSON.stringify(arguments), conf);
    } else if (arguments[1] === 'deployConnector') {
        msg(JSON.stringify(arguments), conf);
    } else if (basic.isEmpty(arguments[1]) || arguments[1] === '--workDir' || arguments[1] === '-debug' ) {//启动前台、后台、API系统
        (async () => {
            const initMongo = require("../init/mongoInit");
            if (!await initMongo(null, thisConf, arguments)) {
                return;
            }
            let list = await getProcesses.getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
            let frontHavePid = false;
            let backendHavePid = false;
            let apiHavePid = false;

            //let checkFrontStr = conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME;
            let checkFrontStr = conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME + "/server/index.js";
            let checkBackStr = conf.COMPONENTS_DIR + "/tapdata-agent";
            let checkMApiSrt = conf.COMPONENTS_DIR + "/apiserver/index";
            //let checkApiSrt = conf.COMPONENTS_DIR + "/apiserver/app";
            //let checkOsSrt = conf.COMPONENTS_DIR + "/os-monitor";
            if (conf._windows) {
                //checkFrontStr = conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME + "/node_modules/pm2/lib/ProcessContainer.js";
                checkFrontStr = checkFrontStr.replace(/\//g, '\\');
                checkBackStr = checkBackStr.replace(/\//g, '\\');
                checkMApiSrt = checkMApiSrt.replace(/\//g, '\\');
                //checkApiSrt.replace(/\//g,'\\');
                //checkOsSrt.replace(/\//g,'\\');
            }
            for (let i = 0; i < list.length; i++) {
                if (list[i].arguments.join(' ').indexOf(checkFrontStr) >= 0) {
                    frontHavePid = true;
                    if (basic.isDebug(arguments)) {
                        console.info("frontend is started.We are not need start frontend.");
                    }
                }
                if (list[i].arguments.join(' ').indexOf(checkBackStr) >= 0) {
                    backendHavePid = true;
                    if (basic.isDebug(arguments)) {
                        console.info("backend is started.We are not need start backend.");
                    }
                }
                if (list[i].arguments.join(' ').indexOf(checkMApiSrt) >= 0) {
                    apiHavePid = true;
                    if (basic.isDebug(arguments)) {
                        console.info("apiserver is started.We are not need start apiserver.");
                    }
                }
            }
            try {
                if (!frontHavePid) {
                    await tapdataManagement.doStartFrontend(conf, arguments);
                }
            } catch (e) {
                console.error('Pre-start Frontend failed: ' + e.toString());
            }

            try {
                if (!apiHavePid) {
                    await apiserver.doStartAPI(conf, arguments);
                }
            } catch (e) {
                console.error('Pre-start APIService failed: ' + e.toString());
            }
            msg(JSON.stringify(arguments), conf);
        })().catch((err) => {
            console.error(err);
        });
    } else {
        basic.printHelp();
    }
}

function stopFun(arguments, conf) {//isRestart
    arguments = arg;
    conf = thisConf;
    if ('-f' === arguments[1]) {
        if(arguments.length > 2){
            basic.printHelp();
            return;
        }
        msg(JSON.stringify(arguments), conf);
        /*
        (async () => {
            await apiserver.stopAPI(conf, isRestart);
            await backend.stopBackend(conf, isRestart);
            await tapdataManagement.stopFrontend(conf, isRestart);
        })();
         */
    } else if ('frontend' === arguments[1]) {//停止前台
        msg(JSON.stringify(arguments), conf);
        /*
        (async () => {
            await tapdataManagement.doStopFrontend(conf);
        })();
         */
    } else if ('backend' === arguments[1]) {//停止后台
        if('-f' === arguments[2]){
            msg(JSON.stringify(arguments), conf);
            return;
        }
        const questions = [{
            type: 'text',
            name: 'res',
            message: 'Please check if running task?(f[Force Close Service]/e[Exit]):'
        }];
        (async () => {
            const response = await prompts(questions);
            if (response['res'] === 'f') {
                msg(JSON.stringify(arguments), conf);
                /*
                (async () => {
                    await backend.stopBackend(conf);
                })();
                 */
            }
        })();
    } else if ('apiserver' === arguments[1]) {//停止API
        msg(JSON.stringify(arguments), conf);
        /*
        (async () => {
            await apiserver.stopAPI(conf);
        })();
         */
    } else if ('os-monitor' === arguments[1]) {//停止os-monitor
        (async () => {
            await OsMonitor.stopOsMonitor(conf);
        })();
    } else if (basic.isEmpty(arguments[1]) || arguments[1] === '--workDir') {
        const questions = [{
            type: 'text',
            name: 'res',
            message: 'Please check if running task?(f[Force Close Service]/e[Exit]):'
        }];
        (async () => {
            const response = await prompts(questions);
            if (response['res'] === 'f') {
                msg(JSON.stringify(arguments), conf);
                /*
                return;
                let msg = '';
                msg += await apiserver.stopAPI(conf, isRestart);
                msg += await backend.stopBackend(conf, isRestart);
                msg += await tapdataManagement.doStopFrontend(conf, isRestart);
                return msg;

                 */
            }
        })();
    } else if('agent' === arguments[1]){
        msg(JSON.stringify(arguments), conf);
    }else if (MONGODB_PROCESS_NAME === arguments[1]) {
        msg(JSON.stringify(arguments), conf);
    } else if ('deployConnector' === arguments[1]) {
        msg(JSON.stringify(arguments), conf);
    }else {
        //打印帮助信息
        basic.printHelp();
    }
}

function statusFun(args, conf) {//查看系统运行状态
    (async () => {
        /*
        let list = await getProcesses.getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
        let frontMPid = "";
        let frontPid = "";
        let backendPid = "";
        let apiMPid = "";
        let apiPid = "";
        let osiPid = "";
        let checkFrontStr = conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME + "/server/server.js";// conf.TAPDATA_HOME + "/node_modules/pm2"; //conf.COMPONENTS_DIR +
        let checkMFrontStr = conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME + "/server/index.js";
        let checkBackStr = conf.COMPONENTS_DIR + "/tapdata-agent-";
        let checkMApiSrt = conf.COMPONENTS_DIR + "/apiserver/index";
        let checkApiSrt = conf.COMPONENTS_DIR + "/apiserver";
        let checkOsSrt = conf.COMPONENTS_DIR + "/os-monitor";
        if (conf._windows) {
            //checkFrontStr = conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME + "/node_modules/pm2/lib/ProcessContainer.js";
            checkFrontStr = checkFrontStr.replace(/\//g, '\\');
            checkMFrontStr = checkMFrontStr.replace(/\//g, '\\');
            checkBackStr = checkBackStr.replace(/\//g, '\\');
            checkMApiSrt = checkMApiSrt.replace(/\//g, '\\');
            checkApiSrt = checkApiSrt.replace(/\//g, '\\');
            checkOsSrt = checkOsSrt.replace(/\//g, '\\');
        }
        for (let i = 0; i < list.length; i++) {
            if (list[i].arguments.join(' ').indexOf(checkMFrontStr) >= 0) {
                frontMPid += " " + list[i].pid;
            }
            if (list[i].arguments.join(' ').indexOf(checkFrontStr) >= 0) {
                frontPid += " " + list[i].pid;
            }
            if (list[i].arguments.join(' ').indexOf(checkBackStr) >= 0) {
                backendPid += " " + list[i].pid;
            }
            if (list[i].arguments.join(' ').indexOf(checkMApiSrt) >= 0) {
                apiMPid += " " + list[i].pid;
            }
            if (list[i].arguments.join(' ').indexOf(checkApiSrt) >= 0 &&
                list[i].arguments.join(' ').indexOf("app") >= 0) {
                apiPid += " " + list[i].pid;
            }
            if (list[i].arguments.join(' ').indexOf(checkOsSrt) >= 0) {
                osiPid += " " + list[i].pid;
            }
        }
         */
        conf = thisConf;
        const status = await basic.getStatus(conf);
        if(!basic.isTMJava(conf)) {
            if ((status.backendPid !== "" && status.frontPid !== "") || (conf.appType === 'DFS' && status.backendPid !== "")) {//if (status.backendProcess(conf) && status.tapdataManagement(conf)) {
                console.info("Tapdata was started.");
            } else {
                console.info("Tapdata was stopped.");
            }
        }else{
            if ((status.backendPid !== "" && status.frontMPid !== "") || (conf.appType === 'DFS' && status.backendPid !== "")) {//if (status.backendProcess(conf) && status.tapdataManagement(conf)) {
                console.info("Tapdata was started.");
            } else {
                console.info("Tapdata was stopped.");
            }
        }
        console.info("Tapdata Engine PID:" + status.backendPid);//status.backendProcess(conf));
        if(conf.appType !== 'DFS') {
            if(!basic.isTMJava(conf)) {
                console.info("Tapdata Management Controller PID:" + status.frontMPid);//status.tapdataManagement(conf));
                console.info("Tapdata Management Instances PID:" + status.frontPid);//status.tapdataManagement(conf));
            } else {
                console.info("Tapdata Management PID:" + status.frontMPid);
            }
            console.info("API Server Controller PID:" + status.apiMPid);//status.apiserverManageProcess(conf));
            console.info("API Server Instances PID:" + status.apiPid);//status.apiserverProcess(conf));
            //console.info("OS Monitor Daemon PID:" + osiPid);//status.osmonitorProcess(conf));
            console.info("MongoDB PID:" + status.mongodbPid);
        }

        msg(JSON.stringify(args), conf);
    })();
}
