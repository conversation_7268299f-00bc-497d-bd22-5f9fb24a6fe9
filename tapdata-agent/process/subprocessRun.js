const logger = require('../log/log')
module.exports =async function() {
    logger.info('begin run backend');
    let env = process.env;
    let stdio = ['ignore', 'ignore', 'ignore'];
    let args = env.TAPDATA_BACKEND_ARGS;
    args = JSON.parse(args);
    logger.info('begin run backend java path:',env.TAPDATA_JAVA_PATH);
    let subProcess = require('child_process').spawn(env.TAPDATA_JAVA_PATH, args, {
        'env': env,
        'cwd': env.TAPDATA_HOME,
        'stdio': stdio,
        'windowsHide': true,
        'detached': true
    });
    subProcess.unref();
    let start = (new Date()).getTime();
    while((new Date()).getTime() - start < 2000) {
    }
}