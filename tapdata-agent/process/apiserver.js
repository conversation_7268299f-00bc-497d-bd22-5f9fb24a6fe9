const basic = require('../basic');
//const getProcesses = require("getprocesses");
const fs = require('fs');
const path = require('path');
//const msg = require('../sendMessage');
let reportInfo = {'server':'apiServer','level':'INFO'};
const connectionSendMsg = require("../connection").sandMsg;
module.exports = {
    doStartAPI:async function (conf,arguments) {//启动API服务
        if(basic.isDebug(arguments)){
            console.info("ApiServer is starting...");
        }
        let needTar = false;
        if(basic.isDebug(arguments)){
            console.info("check ApiServer gz file");
        }
        let arr = fs.readdirSync(conf.COMPONENTS_DIR);
        for (let x in arr) {
            if (/apiserver+[\s\S]*?\.tar\.gz$/.test(arr[x])) {
                needTar = true;
                try {
                    fs.accessSync(path.join(conf.COMPONENTS_DIR, "apiserver"), fs.constants.F_OK);
                    fs.rmdirSync(path.join(conf.COMPONENTS_DIR, "apiserver"), {'recursive': true});
                } catch (e) {
                }
                const tar = require('tar');
                console.info("Unpack the APIServer files...");
                try {
                    await tar.x(
                        {
                            file: path.join(conf.COMPONENTS_DIR, arr[x]),
                            C: conf.COMPONENTS_DIR,
                            filter: (path, entry)=>{
                                return !(conf._windows && (entry.type === 'SymbolicLink' || entry.type === 'Link'));
                                //return true;
                            },
                            strict: true,
                            onwarn: (code, message, data) => {
                                //console.error(code, message);
                            }
                        }
                    );
                }catch (e) {
                    console.info("Unpack the APIServer files error:" + e);
                    return;
                }
                let arr1 = fs.readdirSync(conf.COMPONENTS_DIR);
                for (let y in arr1) {
                    if (/apig-+[\s\S]*?$/.test(arr1[y])) {
                        try {
                            fs.accessSync(path.join(conf.COMPONENTS_DIR, arr1[y]), fs.constants.F_OK);
                            fs.renameSync(path.join(conf.COMPONENTS_DIR, arr1[y]), path.join(conf.COMPONENTS_DIR, "apiserver"));
                            if(basic.isDebug(arguments)){
                                console.info("rename apiserver dir");
                            }
                        } catch (e) {
                            console.error('Rename APIServer failed: ' + e.toString())
                        }
                    }
                }
                try {
                    fs.accessSync(path.join(conf.COMPONENTS_DIR, arr[x]), fs.constants.F_OK);
                    fs.unlinkSync(path.join(conf.COMPONENTS_DIR, arr[x]), {'recursive': true});
                    if(basic.isDebug(arguments)){
                        console.info("remove apiserver gz file:" + arr[x]);
                    }
                } catch (e) {
                    console.info(e);
                }
                try {
                    let apiDirFiles = fs.readdirSync(path.join(conf.COMPONENTS_DIR, "apiserver"));
                    for(let z in apiDirFiles){
                        if(apiDirFiles[z] === 'post-install.js'){
                            let env = process.env;
                            env.PATH = conf.NODE_HOME +  '/:' + env.PATH;
                            let stdio = ['ignore','ignore','ignore'];//outLog
                            let cwd = basic.getExecStr(conf,conf.COMPONENTS_DIR + "/apiserver/");
                             let result = require('child_process').spawnSync(conf._NODE_EXEC, ['post-install.js'], {
                                'env': env,
                                'cwd': cwd,
                                'stdio': stdio,
                                'windowsHide': true,
                                'detached': true
                            });
                            if (result.error) {
                                console.info('post-install error:', result.error.message);
                            } else if (result.status !== 0) {
                                // 执行了但返回非0状态码
                                console.info('post-install exit code:', result.status);
                                console.info('error:', result.stderr);
                            }
                            break;
                        }
                    }
                } catch (e) {
                    console.info(e);
                }
            }
        }
    }
    ,
    stopAPI:async function (conf,isRestart,connect) {//停止API
        //(async () => {
            let list ;//= await require("getprocesses").getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
        if (conf._windows) {
            list = await require("../ps_win").getProcessesWindows();
        }else {
            list = await require("getprocesses").getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
        }
            let apiserver = conf.COMPONENTS_DIR + "/apiserver/index.js";
            //let appClister = conf.COMPONENTS_DIR + "/apiserver/app_cluster.js";
            //let app = conf.COMPONENTS_DIR + "/apiserver";
            if(conf._windows){
                apiserver = apiserver.replace(/\//g,'\\');
                //appClister = appClister.replace(/\//g,'\\');
                //app = app.replace(/\//g,'\\');
            }
            let isKilled = false;
            for (let i = 0; i < list.length; i++) {
                if (list[i].command.indexOf(apiserver) >= 0 || list[i].arguments.join(' ').indexOf(apiserver) >= 0) {
                    basic.killPid(list[i].pid,conf);
                    isKilled = true;
                }
            }
            if(isKilled && basic.isEmpty((await basic.getStatus(conf)).apiMPid)) {
                //console.info("APIService down.");
                connect.write("APIService down.",reportInfo);
                let msgObj = {
                    level:"warn",
                    system:"agent",
                    msg:"APISeverStoppedSuccessfully",
                    title:"APISeverStoppedSuccessfully",
                    serverName:"",
                    sourceId:conf.uuid
                };
                //connectionSendMsg('postMessage',msgObj);
            }else if(isKilled){
                await this.stopAPI(conf,isRestart,connect);
            }else if(!isKilled){
                connect.write('ApiServer is already stopped. Skipping stop operation.',reportInfo);
                //console.info('ApiServer is already stopped. Skipping stop operation.');
            }
            if(isRestart)
                await this.startApi(conf,arguments,connect);
        //})();
    },
    startApi:async function (conf,arguments,connect) {
        //let execString = "." + conf.COMPONENTS_DIR + "/apiserver/start.sh";
        //const outLog = fs.openSync(conf.WORK_DIR + "/logs/ApiServerDebug.log","a");
        //const errLog = fs.openSync(conf.WORK_DIR + "/logs/ApiServerDebug.log","a");
        let env = process.env;
        let backUrl = conf.backend_url;
        backUrl = backUrl.split(',');
        backUrl = backUrl[0].toLowerCase();
        if(basic.isDebug(arguments)){
            console.info("backUrl is:"+backUrl);
            connect.write("backUrl is:"+backUrl,reportInfo);
        }
        backUrl = backUrl.replace("http://","");
        backUrl = backUrl.split(':');
        if(backUrl.length<2) {
            if(conf.backend_url.indexOf("http://")){
                env.TAPDATA_PORT = "80";
            }else{
                env.TAPDATA_PORT = "443";
            }
        }else {
            env.TAPDATA_PORT = backUrl[1].split("/")[0];//conf.tapdata_port;
        }
        env.TAPDATA_HOST = backUrl[0];
        env.TAPDATA_MONGO_URI = conf.uri;//'mongodb://127.0.0.1:27017/tapdata';
        env.API_SERVER_PORT = conf.api_server_port;//'3080';
        env.TAPDATA_MONGO_CONN = conf.mongo_conn;//'mongodb://127.0.0.1:27017/tapdata';
        env.TAPDATA_WORK_DIR = conf.WORK_DIR;//'/root/.tapdata';
        env.NODE_HOME = conf.NODE_HOME;
        env.TAPDATA_HOME = conf.TAPDATA_HOME;
        let cloud_accessCode = conf.cloud_accessCode;
        if(cloud_accessCode === ''){
            cloud_accessCode = '3324cfdf-7d3e-4792-bd32-571638d4562f';
        }
        env.TAPDATA_ACCESS_CODE = cloud_accessCode;
        if(basic.isLinux()) {
            env.PATH = conf.NODE_HOME + ':' + env.PATH;
        }else{
            env.PATH = conf.NODE_HOME + ';' + env.PATH;
        }
        env.ERROR_CODE = conf.apiServerErrorCode;
        if(conf.Decimal128ToNumber && conf.Decimal128ToNumber !== '') {
            env.Decimal128ToNumber = conf.Decimal128ToNumber;
        }
        if(conf.apiServerOptions && conf.apiServerOptions !== '') {
            env.NODE_OPTIONS = conf.apiServerOptions;
        }
        if(conf.api_worker_count && conf.api_worker_count !== "") {
            env.API_WORKER_COUNT = conf.api_worker_count;
        }
        try {
            let str = conf.COMPONENTS_DIR + "/apiserver/dist/";
            str = basic.getExecStr(conf,str);
            fs.accessSync(str, fs.constants.F_OK);
        } catch (e) {
            if(basic.isDebug(arguments)){
                console.info("npm run build");
                connect.write("npm run build",reportInfo);
                connect.write("npm run build env: "+ JSON.stringify(env),reportInfo);
            }
            let execString = "npm run build";// conf.NODE_HOME + "/npm run build";
            if(conf._windows){
                execString = execString.replace(/\//g,'\\');
            }
            require('child_process').execSync(execString, {'env': env, cwd: path.join(conf.COMPONENTS_DIR , "apiserver")});
        }
        let execString = conf.COMPONENTS_DIR + "/apiserver/index.js";//conf._NODE_EXEC +    'node ' +
        if (conf._windows){
            execString = execString.replace(/\//g,"\\");
        }
        if(basic.isDebug(arguments)){
            console.info("exec" + execString);
            connect.write("start apiserver commond : exec" + execString,reportInfo);
            connect.write("start apiserver env : " + JSON.stringify(env),reportInfo);
        }
        try {
            let stdio = ['ignore','ignore','pipe'];//outLog
            let cwd = basic.getExecStr(conf,conf.COMPONENTS_DIR + "/apiserver/");
            if(basic.isDebug(arguments)){
                console.info("start apiserver env :");
                console.info(env);
                console.info("start apiserver args :");
                console.info(execString);
                console.info("start apiserver cwd :");
                console.info(cwd);
                //stdio = ['ignore',outLog,errLog];
            }
            console.info('conf._NODE_EXEC:',conf._NODE_EXEC);
            let subProcess = require('child_process').spawn(conf._NODE_EXEC, [execString], {
                'env': env,
                'cwd': cwd,
                'stdio': stdio,
                'windowsHide': true,
                'detached': true
            });
            const log4js = require("log4js");
            log4js.configure({
                appenders: { cheese: {compress: true, alwaysIncludePattern:true , daysToKeep: 7, type: "dateFile", filename: conf.WORK_DIR + "/logs/tapdata-agent/ApiServerDebug.log" } },
                categories: { default: { appenders: ["cheese"], level: "error" } }
            });
            const logger = log4js.getLogger("cheese");
            subProcess.stderr.on('data',(msg)=>{
                logger.error(msg.toString());
            });
            subProcess.unref();
            //if(!basic.isEmpty((await basic.getStatus(conf)).apiMPid)) {
                connect.write("APIServer started.", reportInfo);
                //console.info("API servic started");
            let msgObj = {
                level:"info",
                system:"agent",
                msg:"APISeverStartedSuccessfully",
                title:"APISeverStartedSuccessfully",
                serverName:"",
                sourceId:conf.uuid
            };
            setTimeout(()=>{connectionSendMsg('postMessage',msgObj);},2000);
        } catch (e) {
            console.error(e);
        }
    }
};
/*
function start(conf,arguments) {
    //let execString = "." + conf.COMPONENTS_DIR + "/apiserver/start.sh";
    const outLog = fs.openSync(conf.COMPONENTS_DIR + "/ApiServerDebug.log","a");
    const errLog = fs.openSync(conf.COMPONENTS_DIR + "/ApiServerDebug.log","a");
    let env = process.env;
    env.TAPDATA_PORT = conf.tapdata_port;
    env.TAPDATA_MONGO_URI = conf.uri;//'mongodb://127.0.0.1:27017/tapdata';
    env.API_SERVER_PORT = conf.api_server_port;//'3080';
    env.TAPDATA_MONGO_CONN = conf.mongo_conn;//'mongodb://127.0.0.1:27017/tapdata';
    env.TAPDATA_WORK_DIR = conf.WORK_DIR;//'/root/.tapdata';
    env.NODE_HOME = conf.NODE_HOME;
    env.TAPDATA_HOME = conf.TAPDATA_HOME;
    env.PATH = conf.NODE_HOME + ':' + env.PATH;
    try {
        let str = conf.COMPONENTS_DIR + "/apiserver/dist/";
        str = basic.getExecStr(conf,str);
        fs.accessSync(str, fs.constants.F_OK);
    } catch (e) {
        if(basic.isDebug(arguments)){
            console.info("npm run build");
        }
        let execString = "npm run build";// conf.NODE_HOME + "/npm run build";
        if(conf._windows){
            execString = execString.replace(/\//g,'\\');
        }
        require('child_process').execSync(execString, {'env': env, cwd: conf.COMPONENTS_DIR + "/apiserver"});
    }
    let execString = conf.COMPONENTS_DIR + "/apiserver/index.js";//conf._NODE_EXEC +    'node ' +
    if (conf._windows){
        execString = execString.replace(/\//g,"\\");
    }
    try {
        let stdio = ['ignore',outLog,errLog];
        let cwd = basic.getExecStr(conf,conf.COMPONENTS_DIR + "/apiserver/");
        if(basic.isDebug(arguments)){
            console.info("start apiserver env :");
            console.info(env);
            console.info("start apiserver args :");
            console.info(execString);
            console.info("start apiserver cwd :");
            console.info(cwd);
            //stdio = ['ignore',outLog,errLog];
        }
        let subProcess = require('child_process').spawn('node', [execString], {
            'env': env,
            'cwd': cwd,
            'stdio': stdio,
            'detached': true
        });
        subProcess.unref();
        console.info("API service started");
    } catch (e) {
        console.error(e);
    }
}

 */