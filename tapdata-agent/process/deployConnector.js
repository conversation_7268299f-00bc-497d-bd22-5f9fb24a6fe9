const {
    Worker, isMainThread, parentPort, workerData,
} = require('worker_threads');
const {deploy} = require("./tapdataManagement");
const fs = require("fs");
const path = require("path");

if (isMainThread) {
    const deployResult = {
        startAt: undefined,
        endAt: undefined,
        running: undefined,
        message: [],
        exitCode: undefined
    }
    let worker;
    const deployConnector = function(conf, args, connect) {

        if (deployResult.running) {
            const msg = `Deploy connector worker is already running, start at ${deployResult.startAt.toLocaleString()}`
            console.log(msg);
            connect.write(msg)
            return
        }

        Object.assign(deployResult, {
            startAt: new Date(),
            endAt: undefined,
            running: true,
            message: [],
            exitCode: undefined
        })

        worker = new Worker(__filename, {
            workerData: {conf, args},
        });
        worker.on('message', (msg) => {
            deployResult.message.push(msg);
            // 更新进度显示
            updateProgressDisplay(msg, connect);
        });
        worker.on('messageerror', (err) => {
            deployResult.message.push(`Received messageerror from deploy connector worker: ${err}`)
            console.error('Received messageerror from deploy connector worker:', err);
        });
        worker.on('error', (err) => {
            deployResult.message.push(`Received error from deploy connector worker: ${err}`)
            console.error('Received error from deploy connector worker:', err);
        });
        worker.on('exit', (code) => {
            deployResult.running = false;
            deployResult.exitCode = code;
            deployResult.endAt = new Date();
            deployResult.message.push('Deploy connector worker has stopped.')

            // 显示最终结果
            if (lastProgressOutput) {
                if (totalFiles > 0) {
                    const duration = (Date.now() - deploymentStartTime) / 1000;
                    connect.write(`Deploy connector completed in ${duration.toFixed(1)}s: ${completedFiles}/${totalFiles} successful${failedFiles > 0 ? `, ${failedFiles} failed` : ''}.`);
                } else {
                    connect.write('Deploy connector completed.');
                }
            }

            if (code !== '0') {
                console.error(`Deploy connector worker stopped with exit code ${code}`)
            } else {
                console.log(`Deploy connector worker exit ${code}`)
            }
            worker.unref()
            worker = null
        });
        connect.write('Deploy connector worker has running...')

        // 初始化进度显示
        initProgressDisplay(connect);
    }
    const stopDeploy = (conf, connect) => {
        if (worker) {
            deployResult.message.push('Stop deploy connector worker.')
            worker.postMessage('stop')
            worker.terminate()
            connect.write('Deploy connector worker has stopped.')
        } else {
            connect.write('Deploy connector worker is not started.')
        }
    }
    const deployStatus = (conf, connect) => {
        if (deployResult.running === undefined) {
            return true;
        }
        const end = deployResult.endAt || new Date()
        const times = Number((end.getTime() - deployResult.startAt.getTime())/60000).toFixed(2);
        let message = []
        message.push(`Deploy connector worker is ${deployResult.running ? 'running' : 'stopped'}, total times ${times}m.`);
        message = message.concat(deployResult.message).join('\r\n')
        connect.write(message)
    }
    const deploying = (conf, connect) => {
        if (deployResult === undefined || deployResult.running === undefined) {
            return false;
        }
        return deployResult.running;
    }

    // 主线程进度显示变量
    let totalFiles = 0;
    let completedFiles = 0;
    let failedFiles = 0;
    let lastProgressOutput = '';
    let deploymentStartTime = Date.now();
    let currentConnect = null;
    let baseProgressMsg = "<<<< Deploying connectors: ";

    // 生成进度条字符串
    function generateProgressBar(percentage, width = 30) {
        const filled = Math.round((percentage / 100) * width);
        const empty = width - filled;
        return '[' + chars(filled, "#") + chars(empty, " ") + ']';
    }
    
    function chars(len, chr) {
        let str = "";
        if (len <= 0) {
            return str;
        }
        for (let i = 0; i < len; i++) {
            str += ("" + chr)
        }
        return str;
    }

    // 计算剩余时间
    function calculateETA() {
        if (completedFiles === 0 || totalFiles === 0) return '0ms';

        const elapsed = (Date.now() - deploymentStartTime) / 1000; // 秒
        const avgTimePerFile = elapsed / completedFiles;
        const remainingFiles = totalFiles - completedFiles - failedFiles;
        const etaSeconds = Math.round(avgTimePerFile * remainingFiles);

        if (etaSeconds < 60) {
            return `${etaSeconds}s`;
        } else if (etaSeconds < 3600) {
            const minutes = Math.floor(etaSeconds / 60);
            const seconds = etaSeconds % 60;
            return `${minutes}m ${seconds}s`;
        } else {
            const hours = Math.floor(etaSeconds / 3600);
            const minutes = Math.floor((etaSeconds % 3600) / 60);
            return `${hours}h ${minutes}m`;
        }
    }

    // 显示进度
    function showProgress(start) {
        if (totalFiles === 0 || !currentConnect) return;

        const percentage = Math.round(((completedFiles + failedFiles) / totalFiles) * 100);
        const progressBar = generateProgressBar(percentage);
        const eta = calculateETA();

        // 构建进度信息部分（类似于 point 变量）
        let progressInfo = `${progressBar} ${completedFiles}/${totalFiles} (${percentage}%)`;

        // 添加失败信息
        if (failedFiles > 0) {
            progressInfo += ` [${failedFiles} failed]`;
        }

        // 添加剩余时间
        if (completedFiles + failedFiles < totalFiles) {
            progressInfo += ` ETA: ${eta}`;
        }

        // 完全模仿第340行的实现：connect.write(waitingMsg+point,reportInfo);
        const reportInfo = {'server':'management','level':'INFO'};
        currentConnect.write(start + baseProgressMsg + progressInfo, reportInfo);
        lastProgressOutput = progressInfo;
    }



    // 更新进度显示
    function updateProgressDisplay(msg, connect) {
        if (typeof msg === 'string') {
            // 匹配 "Found X connectors in directory" 消息获取总数
            const totalMatch = msg.match(/Found (\d+) connectors in directory/);
            if (totalMatch) {
                totalFiles = parseInt(totalMatch[1]);
                deploymentStartTime = Date.now();

                // 模仿第307行：先输出基础消息
                const reportInfo = {'server':'management','level':'INFO'};
                connect.write("Deploying connectors", reportInfo);

                // 立即显示初始进度
                showProgress("");
                return;
            }

            // 匹配 "deployed connector :" 消息统计已完成数
            if (msg.includes('deployed connector :')) {
                completedFiles++;
                showProgress("");
                return;
            }

            // 匹配部署失败的消息
            if (msg.includes('deploy ') && msg.includes(' fail.')) {
                failedFiles++;
                showProgress("");
                return;
            }
        }
    }

    // 初始化进度显示
    function initProgressDisplay(connect) {
        totalFiles = 0;
        completedFiles = 0;
        failedFiles = 0;
        lastProgressOutput = '';
        deploymentStartTime = Date.now();
        currentConnect = connect;
    }


    module.exports = {
        deployResult,
        deployConnector,
        stopDeploy,
        deployStatus,
        deploying
    };
    return;
}


//console.log('worker received workerData: ' + workerData)
async function worker() {
    const { deploy } = require('./tapdataManagement');
    try {
        const start = Date.now();
        await deploy(workerData.conf, {
            write: (msg) => parentPort.postMessage(msg)
        }, workerData.args);
        const msg = `Deploy connector done ${(Date.now() - start)/ 1000}s.`
        parentPort.postMessage(msg)
        console.log(msg)
    } catch (e) {
        console.error('Deploy connector error', e)
        parentPort.postMessage(`Execute deploy connector error ${e.toString()}`);
    } finally {
        parentPort.unref();
    }
}
worker().then(console.log)
