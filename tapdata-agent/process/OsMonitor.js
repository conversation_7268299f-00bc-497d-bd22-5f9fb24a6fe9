const basic = require('../basic');
const getProcesses = require("getprocesses");
const fs = require('fs');
module.exports = {
    startOsMonitor:function (conf,arguments) {
        if(basic.isDebug(arguments)){
            console.info("OsMonitor is starting...");
        }
        let needTar = false;
        if(basic.isDebug(arguments)){
            console.info("check OsMonitor gz file");
        }
        let arr = fs.readdirSync(conf.COMPONENTS_DIR);
        for (let x in arr) {
            if (/os-monitor+[\s\S]*?\.tar\.gz$/.test(arr[x])) {
                needTar = true;
                try {
                    fs.accessSync(conf.COMPONENTS_DIR + "/os-monitor", fs.constants.F_OK);
                    fs.rmdirSync(conf.COMPONENTS_DIR + "/os-monitor", {'recursive': true});
                } catch (e) {}
                const tar = require('tar');
                console.info("Unpack the files...");
                tar.x(  // or tar.extract(
                    {
                        file: conf.COMPONENTS_DIR + "/" + arr[x],
                        C: conf.COMPONENTS_DIR + '/'
                    }
                ).then(_ => {

                    let arr1 = fs.readdirSync(conf.COMPONENTS_DIR);
                    for (let y in arr1) {
                        if (/os-monitor-+[\s\S]*?$/.test(arr1[y])) {
                            try {
                                let from = conf.COMPONENTS_DIR + "/" + arr1[y];
                                let to = conf.COMPONENTS_DIR + "/os-monitor/";
                                if(conf._windows){
                                    from = from.replace(/\//g,'\\');
                                    to = to.replace(/\//g,'\\');
                                }
                                fs.accessSync(from, fs.constants.F_OK);
                                fs.renameSync(from, to);
                                if(basic.isDebug(arguments)){
                                    console.info("rename OsMonitor dir");
                                }
                            } catch (e) {}
                            /*
                            try {
                                fs.accessSync(conf.COMPONENTS_DIR + "/" + arr1[y], fs.constants.F_OK);
                                fs.rmdirSync(conf.COMPONENTS_DIR + "/" + arr1[y], {'recursive': true});
                            } catch (e) {}
                             */
                            try {
                                fs.accessSync(conf.COMPONENTS_DIR + "/" + arr[x], fs.constants.F_OK);
                                fs.rmdirSync(conf.COMPONENTS_DIR + "/" + arr[x], {'recursive': true});
                                if(basic.isDebug(arguments)){
                                    console.info("remove OsMonitor gz file");
                                }
                            } catch (e) {}
                        }
                    }
                    startFun(conf,arguments);
                });
            }
        }
        if (!needTar)
            startFun(conf,arguments);
    }
    ,
    stopOsMonitor:async function (conf) {
        //(async () => {
            let list = await getProcesses.getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
            let  strOS = conf.COMPONENTS_DIR + "/os-monitor/index.js";
            let isKilled = false;
            strOS = basic.getExecStr(conf,strOS);
            for (let i = 0; i < list.length; i++) {
                if (list[i].arguments.join(' ').indexOf(strOS) >= 0) {
                    basic.killPid(list[i].pid,conf);
                    isKilled = true;
                }
            }
            if(isKilled) {
                console.info("Os-Monitor service is down.");
            }
        //})();
    }
};
function startFun(conf,arguments){
    //console.info('Start api server');
    const outLog = fs.openSync(conf.COMPONENTS_DIR + "/OsMonitorDebug.log","a");
    const errLog = fs.openSync(conf.COMPONENTS_DIR + "/OsMonitorDebug.log","a");
    let env = process.env;
    env.TAPDATA_MONGO_URI = conf.uri;//'mongodb://127.0.0.1:27017/tapdata';
    env.TAPDATA_PORT = conf.tapdata_port;
    env.API_SERVER_PORT = conf.api_server_port;//'3080';
    env.NODE_HOME = conf.NODE_HOME;
    env.TAPDATA_MONGO_CONN = conf.mongo_conn;//'mongodb://127.0.0.1:27017/tapdata';
    env.TAPDATA_HOME = conf.TAPDATA_HOME;
    env.TAPDATA_WORK_DIR = conf.WORK_DIR;//'/root/.tapdata';
    if(basic.isLinux()) {
        env.PATH = conf.NODE_HOME + ':' + env.PATH;
    }else{
        env.PATH = conf.NODE_HOME + ';' + env.PATH;
    }
    try {
        let str = conf.COMPONENTS_DIR + "/os-monitor/dist/";
        str = basic.getExecStr(conf,str);
        fs.accessSync(str, fs.constants.F_OK);
    } catch (e) {
        if(basic.isDebug(arguments)){
            console.info("npm run build");
        }
        let execString = "npm run build";
        require('child_process').execSync(execString,{'env': env,cwd:conf.COMPONENTS_DIR + "/os-monitor/"});
    }
    let execString = conf.COMPONENTS_DIR + "/os-monitor/index.js";//conf._NODE_EXEC + ' ' +
    let cwd = conf.COMPONENTS_DIR + "/os-monitor/";
    execString = basic.getExecStr(conf,execString);
    cwd = basic.getExecStr(conf,cwd);
    /*
    if(conf._windows){
        execString = execString.replace(/\//g,'\\');
        cwd = cwd.replace(/\//g,'\\');
    }
     */
    let stdio = ['ignore',outLog,errLog];
    if(basic.isDebug(arguments)){
        console.info("start OsMonitor env :");
        console.info(env);
        console.info("start OsMonitor args :");
        console.info(execString);
        console.info("start OsMonitor cwd :");
        console.info(cwd);
        //stdio = ['inherit','inherit','inherit'];
    }
    let subProcess = require('child_process').spawn('node',[execString], {
        'env': env,
        'cwd':cwd,
        'stdio':stdio,
        'detached': true,
        'windowsHide': true
    });
    subProcess.unref();
    console.info("Os-Monitor service started.");
}