const path = require("path");
const fs = require("fs");
module.exports =async function(conf,env) {
    console.info('begin run')
    let GodExe = '';
    let homeDir = conf.TAPDATA_HOME === '' ? '/' : conf.TAPDATA_HOME;
    if (conf._windows) {
        GodExe += path.join(homeDir, 'run.exe');
    } else if (conf._linux) {
        GodExe += path.join(homeDir, 'run');
    }
    let needDel = false;
    try {
        fs.accessSync(GodExe, fs.constants.F_OK);
        needDel = true;
    } catch (e) {
    }
    console.info('begin run needDel:',needDel)
    if(needDel){
        try {
            fs.unlinkSync(GodExe);
        } catch (e) {
        }
    }
    try {
        let from = path.join(conf.TAPDATA_HOME, 'tapdata');
        if (conf._windows) {
            from += '.exe';
        }
        fs.copyFileSync(from, GodExe);
        let stdio = ['ignore', 'ignore', 'ignore'];
        if (conf._windows) {
            let subProcess1 = require('child_process').spawn('attrib', ['+h', GodExe], {
                'stdio': stdio,
                'detached': true,
                'windowsHide': true
            });
            subProcess1.unref();
        }
    } catch (e) {
        console.info(e)
    }
    let checkBackStr;
    const homePaht = conf.TAPDATA_HOME === '' ? '/' : conf.TAPDATA_HOME;
    if (conf._linux) {
        checkBackStr = path.join(homePaht, "run");
    }else{
        checkBackStr = path.join(homePaht, "run");
    }
    //console.info('spawn update process:',checkBackStr);
    let stdio = 'ignore';
    //console.info('spawn update env:',env);
    let subProcess = require('child_process').spawn(checkBackStr, ['run'], {
        'cwd':conf.TAPDATA_HOME,
        'stdio': stdio,
        'env': env,
        'detached': true,
        'windowsHide':true
    });
    //console.info('spawn update process finish:',checkBackStr);
    subProcess.unref();
}

