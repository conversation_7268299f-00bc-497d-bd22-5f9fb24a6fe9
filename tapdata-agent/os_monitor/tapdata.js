/**
 * <AUTHOR>
 * @date 3/31/19
 * @description
 */
const axios = require('axios');
const appConfig = require('./config');

let token = null;

exports.getToken = getToken = async function(cb){
	cb = cb || function(){};
	if( token ){
		cb(token);
	} else {
		try {
			const response = await axios.post(appConfig.tapDataServer.tokenUrl,
				new URLSearchParams({
					accesscode: appConfig.tapDataServer.accessCode
				}), {
				headers: {
					'Content-Type': 'application/x-www-form-urlencoded'
				}
			});

			if( response.status === 200 ){
				let result = response.data;
				token = result.data.id;
				cb(token);
				console.log('Get access token success,', JSON.stringify(result));
				if( result.data.ttl)
					setTimeout(()=>{
						token = null;
					}, (result.data.ttl - 3600) * 1000) // 提前一小时获取token
			} else {
				console.error('Get access token error,', response.data);
				cb( false )
			}
		} catch (err) {
			console.error('Get access token error', err);
			cb(false);
		}
	}
};
