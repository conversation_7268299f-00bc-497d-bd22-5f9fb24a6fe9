const log = require('./log').app;

const axios = require('axios');
const path = require('path');
const appConfig = require('./config');
const getToken = require('./tapdata').getToken;

const hostname = require('os').hostname();
const startTime = new Date().getTime();
const apiServerStatus = {
	worker_status: {}
};

const report = async function (data, token) {
	//const configPath = path.join(__dirname, 'config.json');
	const configPath = path.join('.', 'config.json');
	const reportServerUrl = appConfig.tapDataServer.reportUrl + '?access_token=' + token;

	if (!reportServerUrl || !reportServerUrl)
		return;

	data = Object.assign(data || {}, appConfig.reportData);

	data['start_time'] = startTime;
	//	data['ping_time'] = new Date().getTime();
	data['hostname'] = hostname;
	data['computer_name'] = hostname;
	data['total_thread'] = 2;
	data['running_thread'] = apiServerStatus.worker_status.status === 'running' ? 2 : 1;
	data['version'] = appConfig.version;
	data['reportIntervals'] = appConfig.reportIntervals;
	data['gatherIntervals'] = appConfig.gatherIntervals;

	Object.assign(data, apiServerStatus);

	if (!data.info) {
		//脏数据，不上报
		return;
	}

	try {
		log.debug('report data', data);
		const response = await axios.post(reportServerUrl, data, {
			headers: {
				'Content-Type': 'application/json'
			}
		});

		log.debug('report complete:', response.data);
	} catch (err) {
		log.error('report fail', err);
	}
};

let reportIntervalId = setInterval(timerCb, appConfig.reportIntervals);

exports.resetTimer = function () {
	clearInterval(reportIntervalId);
	log.info("Reset report timer,the new intervals:", appConfig.reportIntervals);
	reportIntervalId = setInterval(timerCb, appConfig.reportIntervals);

};

exports.setStatus = function (status) {
	Object.assign(apiServerStatus, status);
};
function timerCb() {

	getToken(async token => {
		if (token)
			await report(null, token);
	});

}

