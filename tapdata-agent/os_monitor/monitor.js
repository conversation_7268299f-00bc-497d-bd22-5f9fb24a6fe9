const log = require('./log').monitor;
const axios = require('axios');
const appConfig = require('./config');
const hashCode = require('hashcode').hashCode;
const path = require('path');
const fs = require('fs');
const makeDir = require('make-dir');
const getToken = require('./tapdata').getToken;

/**
 * 最后配置信息的 hashCode，用于比较配置文件是否更新
 * @type {null}
 */
let lastHashCode = null,
	intervalId;

/**
 * 加载配置文件
 */
const
	__listeners = {},
	loadConfig = async function (token) {

		let settingUrl = appConfig.tapDataServer.url + '&access_token=' + token;
		log.debug('download load config from tapDataServer ' + settingUrl);
		try {
			const response = await axios.get(settingUrl);

			log.debug('download config success.');
			let body = response.data;
			if (typeof body === 'object') {
				body = JSON.stringify(body);
			}
			body = body.trim();
				/*if( ! (/^\{.*\}$/.test(body) || /^\[.*\]$/.test(body)) ){
					log.error('the configuration file is not in the expected JSON format.', body);
					return;
				}*/
				log.debug(body);

				//  计算 hashCode 比较是否有修改
				let newHashCode = hashCode().value(body);
				log.debug(`old config hash code: ${lastHashCode}, new config hash code: ${newHashCode}`);

				if (newHashCode !== lastHashCode) {
					lastHashCode = newHashCode;

					log.info('tap data config is changed, cache remote config to local.');

					// // 保存到本地缓存目录
					// fs.writeFileSync(getCacheConfig(), body + "\n");

					try {
						let config = JSON.parse(body).data[0];
						log.debug(config);

						// 通知配置文件更新了
						const msg = {
							type: 'changed',
							data: config
						};
						if (__listeners['message']) {
							__listeners['message'].forEach((l) => {
								if (typeof l === 'function')
									l(msg);
							})
						}


					} catch (e) {
						log.error('parse config error: \n', e);
					}
				}
			}
	} catch (err) {
		log.error('download config fail.', err);
	}
},

	/**
	 * 系统启动时，初始化一些配置信息
	 * @private
	 */
	__init = function (cb) {
		// const localConfigFilePath = getCacheConfig();
		// if (fs.existsSync(localConfigFilePath)) {
		// 	let config = fs.readFileSync(localConfigFilePath).toString();
		// 	lastHashCode = hashCode().value(config.trim());
		// }
		cb();
	},

	/**
	 * 获取本地缓存配置文件路径
	 */
	getCacheConfig = function () {

		const cacheDirPath = appConfig.cacheDir.startsWith('/') ? appConfig.cacheDir : path.join(__dirname, appConfig.cacheDir);
		if (!fs.existsSync(cacheDirPath)) {
			log.info(`create cache dir ${cacheDirPath}`);
			makeDir.sync(cacheDirPath);
		}

		return path.resolve(`${cacheDirPath}/tap_data_server_download_config.json`);
	};

exports.on = function (type, listener) {
	if (!__listeners[type])
		__listeners[type] = [];
	__listeners[type].push(listener);
};
let timeoutId;
exports.start = function () {
	__init(() => {
		timeoutId = setInterval(() => {
			getToken(token => {
				if (token) {
					loadConfig(token);
				}
			})

		}, appConfig.intervals);
	});
};
exports.stop = function () {
	if (intervalId)
		clearInterval(intervalId);
};
