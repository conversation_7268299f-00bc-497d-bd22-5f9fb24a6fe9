/**
 * <AUTHOR>
 * @date 4/20/19
 * @description
 */

'use strict';

const util = require('util');
const axios = require('axios');
const getToken = require('./tapdata').getToken;
const pid = process.pid;
let urlNum = 0;
let urlBegin = 0;
const appConfig = require('./config');
function wrapErrorsWithInspect(items) {
	return items.map((item) => {
		if ((item instanceof Error) && item.stack) {
			return {
				inspect: function () {
					return `${util.format(item)}\n${item.stack}`;
				}
			};
		}

		return item;
	});
}

function format(logData) {
	return util.format.apply(util, wrapErrorsWithInspect(logData));
}

function logFacesAppender(config) {
	return function log(event) {
		// convert to logFaces compact json format
		const lfsEvent = {
			threadId: pid,
			//threadName: config.application || '', // application name
			threadName: event.context['threadName'] || '',
			date: event.startTime.getTime(), // time stamp
			level: event.level.levelStr, // level (priority)
			loggerName: event.categoryName, // logger name
			message: format(event.data) // message text
		};

		// add context variables if exist
		Object.keys(event.context).forEach((key) => {
			lfsEvent[`p_${key}`] = event.context[key];
		});
		console.info('begin get Token');
		// send to server
		getToken(async function(token){
			if(!token)return;
			let url = appConfig.tapDataServer.urls[urlNum] + config.url + '?access_token=' + token;
			let obj = {
				url: url,
				json: true,
				body: lfsEvent,
				timeout: 2000
			};
			urlBegin = urlNum;
			console.info('begin send msg');
			await report(obj,config,token);
			/*
			request.post({
				url: url,
				json: true,
				body: lfsEvent
			}, (err) => {//, resp, body
				if( err ){
					console.error('report fail', err);
				}
			});
			 */
		});
	};
}

async function report(obj,config,token){
	console.info(obj);
	try {
		await axios.post(obj.url, obj.body, {
			headers: {
				'Content-Type': 'application/json'
			},
			timeout: obj.timeout || 2000
		});
	} catch (err) {
		if(urlNum + 1 === urlBegin){
			console.error('report fail', err);
			return;
		}
		if(urlNum + 1 >=  appConfig.tapDataServer.urls.length)
			urlNum = 0;
		else
			urlNum++;
		obj.url =  appConfig.tapDataServer.urls[urlNum] + config.url + '?access_token=' + token;
		await report(obj, config, token);
	}
}
function configure(config) {
	return logFacesAppender(config);
}

module.exports.configure = configure;
