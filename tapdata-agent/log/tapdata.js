/**
 * <AUTHOR>
 * @date 3/31/19
 * @description
 */
const axios = require('axios');
const appConfig = require('./config');

let token = null;
let urlNum = 0;
let urlNow = 0;
exports.getToken = getToken = function(cb){
	cb = cb || function(){};
	if( token ){
		cb(token);
	} else {
		urlNow = urlNum;
		reqToken(cb);
	}
};

function reqToken(cb){
	console.info('get Token '+ appConfig.tapDataServer.urls[urlNow] + appConfig.tapDataServer.tokenUrl);

	// Create form data for axios
	const formData = new URLSearchParams();
	formData.append('accesscode', appConfig.tapDataServer.accessCode);

	axios.post(appConfig.tapDataServer.urls[urlNow] + appConfig.tapDataServer.tokenUrl, formData, {
		timeout: 2000,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded'
		}
	}).then(response => {
		if (response.status === 200) {
			let result = response.data;
			if (typeof result === 'string') {
				result = JSON.parse(result);
			}
			token = result.id;
			console.log('Get access token success,', JSON.stringify(result));
			cb(token);
			if (result.ttl)
				setTimeout(() => {
					token = null;
				}, (result.ttl - 3600) * 1000); // 提前一小时获取token
			return;
		} else {
			console.error('Get access token error,', response.data);
			//cb(false)
		}
		if(urlNow + 1 === urlNum){
			cb(false);
			return;
		}
		if(urlNow + 1 >= appConfig.tapDataServer.urls.length)
			urlNow = 0;
		else
			urlNow++;
		reqToken(cb);
	}).catch(err => {
		console.error('Get access token error', err);
		//cb(false);
		if(urlNow + 1 === urlNum){
			cb(false);
			return;
		}
		if(urlNow + 1 >= appConfig.tapDataServer.urls.length)
			urlNow = 0;
		else
			urlNow++;
		reqToken(cb);
	});
}