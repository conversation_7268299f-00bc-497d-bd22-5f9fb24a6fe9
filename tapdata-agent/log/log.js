/**
 * <AUTHOR>
 * @date 12/11/19
 * @description
 */
const path = require('path');
const appConfig = require('./config');
let log4jsHttp = require('./log4js-http');
const moment = require('moment');
let dateString = moment().format('YYYYMMDDHHmmss');
const fs = require("fs");
const log4js = require('log4js');

let arguments = process.tapdataArguments || [];
let TAPDATA_HOME = path.dirname(process.execPath);//process.cwd();//process.argv[1]
if(TAPDATA_HOME === "/")TAPDATA_HOME = "";
let WORK_DIR;
let isAgent = false;
for (let i = 0; i < arguments.length; i++) {
  if (arguments[i] === '--workDir') {
    WORK_DIR = arguments[i + 1];
    if(WORK_DIR.substring(WORK_DIR.length-1,WORK_DIR.length) === "/" || WORK_DIR.substring(WORK_DIR.length-1,WORK_DIR.length) === "\\"){
      WORK_DIR = WORK_DIR.substring(0,WORK_DIR.length-1);
    }
  }
  if (arguments[i] === 'agent') {
    isAgent = true;
  }
}
if(isAgent){
  dateString = '-resident';
}else{
  dateString = '';
}
if (arguments.length>0 && arguments[0] === 'run') {
  dateString = '-run';
}
if (!WORK_DIR || WORK_DIR === '') {
  if(fs.existsSync(path.join(TAPDATA_HOME,'.workDir'))){
    WORK_DIR = fs.readFileSync(path.join(TAPDATA_HOME,'.workDir'),'utf8').toString();
  }
}
if (!WORK_DIR || WORK_DIR === '') {
  WORK_DIR = path.dirname(process.execPath); //os.homedir();
}

const logPath = path.join(WORK_DIR ,"logs",'tapdata-agent',"agent"+dateString+".log");
//console.info('logger path:',logPath);
log4js.configure({
  appenders: {
    app: {
      type: 'file',
      filename: logPath,
      maxLogSize: 500 * 1024 * 1024,
      backups: 5,
      compress: true
    },
    out: {
      type: 'stdout'
    }
  },
  categories: {
    default: {
      appenders: ['app', 'out'],
      level: 'info'
    },
    app: {
      appenders: ['app', 'out'],
      level: 'info'
    }
  },
});
const logger = log4js.getLogger('app');
module.exports = logger;
