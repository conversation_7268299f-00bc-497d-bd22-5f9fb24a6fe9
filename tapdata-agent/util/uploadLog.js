const OSS = require('ali-oss');
const fs = require('fs');
const streamObj = require("stream");
const zlib = require("zlib");
const path = require('path');
const basic = require("../basic");
const moment = require("moment");
const compressing = require('compressing');
exports.uploadLog = async function(data,conf,ws){
    const logger = require('../log/log');
    logger.info('receive upload log message:');
    const nowTime = new Date().getTime();
    let cpFiles = [];
    try {
        const client = new OSS({
            region: data.ossRegion,
            accessKeyId: data.accessKeyId,
            accessKeySecret: data.accessKeySecret,
            stsToken: data.securityToken,
            bucket: data.logBucket,
            timeout: 600000
        });
        let lastSendTime = 0;

        let uploadDays = data.uploadDays || 3;
        uploadDays = uploadDays*1;
        let days = [];
        for(let i=0;i<uploadDays;i++){
            days.push(moment().add(-1*i,'d').format('YYYYMMDD'))
            days.push(moment().add(-1*i,'d').format('YYYY-MM-DD'))
        }
        let startTime = moment().add(-1 * uploadDays,'d').valueOf();
        let stream = new compressing.tar.Stream();
        let haveNewVersion = false;
        let size = 0;
        try{
            let logPath = path.join(conf.WORK_DIR,'logs','agent');
            let arr = fs.readdirSync(logPath);
            for(let x in arr){
                let mtime = fs.statSync(path.join(conf.WORK_DIR,'logs','agent',arr[x])).mtime;
                mtime = new Date(mtime).getTime();
                if(mtime >= startTime){
                    let selfSize = fs.statSync(path.join(conf.WORK_DIR,'logs','agent',arr[x])).size;
                    if(selfSize * 1 > 0){
                        if(arr[x] === 'tapdata-agent.log'){
                            fs.copyFileSync(path.join(conf.WORK_DIR,'logs','agent',arr[x]),path.join(conf.WORK_DIR,'logs','agent',arr[x] + '.' + nowTime));
                            cpFiles.push(path.join(conf.WORK_DIR,'logs','agent',arr[x] + '.' + nowTime))
                            stream.addEntry(path.join(conf.WORK_DIR, 'logs', 'agent', arr[x] + '.' + nowTime));
                            size += selfSize
                            console.info('Add File :', path.join(conf.WORK_DIR, 'logs', 'agent', arr[x] + '.' + nowTime))
                        }else {
                            stream.addEntry(path.join(conf.WORK_DIR, 'logs', 'agent', arr[x]));
                            size += selfSize
                            console.info('Add File :', path.join(conf.WORK_DIR, 'logs', 'agent', arr[x]))
                        }
                    }
                }
            }
        }catch (e) {
            logger.info(e)
        }
        try{
            let logPath = path.join(conf.WORK_DIR,'logs');
            //fs.statSync(path.join(conf.WORK_DIR,'logs','tapdata-agent.log')).size;
            let arr = fs.readdirSync(logPath);
            for(let x in arr){
                if(arr[x] === 'tapdata-agent.log'){
                    if(haveNewVersion){
                        console.info('Tar File:',conf.WORK_DIR,'logs',arr[x]);
                        await compressing.gzip.compressFile(path.join(conf.WORK_DIR,'logs',arr[x]),path.join(conf.WORK_DIR,'logs',arr[x] +'.'+ moment().format('YYYYMMDD') +'-old.gz'));
                        stream.addEntry( path.join(conf.WORK_DIR,'logs',arr[x] +'.'+ moment().format('YYYYMMDD') +'-old.gz'));
                        size += fs.statSync(path.join(conf.WORK_DIR,'logs',arr[x] +'.'+ moment().format('YYYYMMDD') +'-old.gz')).size;
                        logger.info('Add File :',path.join(conf.WORK_DIR,'logs',arr[x] +'.'+ moment().format('YYYYMMDD') +'-old.gz'))
                        fs.unlinkSync(path.join(conf.WORK_DIR,'logs',arr[x]));
                    }else{
                        //fs.renameSync(path.join(conf.WORK_DIR,'logs',arr[x]),path.join(conf.WORK_DIR,'logs',arr[x]))
                        stream.addEntry( path.join(conf.WORK_DIR,'logs',arr[x]));
                        size += fs.statSyc(path.join(conf.WORK_DIR,'logs',arr[x])).size;
                        logger.info('Add File :',path.join(conf.WORK_DIR,'logs',arr[x]))
                    }
                }
                for(let y in days){
                    if(arr[x].includes(days[y])){
                        //fs.renameSync(path.join(conf.WORK_DIR,'logs','agent',arr[x]),path.join(conf.WORK_DIR,'logs','agent',arr[x] ))
                        stream.addEntry( path.join(conf.WORK_DIR,'logs','agent',arr[x]));
                        size += fs.statSync(path.join(conf.WORK_DIR,'logs','agent',arr[x])).size;
                        logger.info('Add File :',path.join(conf.WORK_DIR,'logs','agent',arr[x]))
                    }
                }
            }
        }catch (e) {
            logger.error(e)
        }
        try{
            let logPath = path.join(conf.WORK_DIR,'logs','jobs');
            let arr = fs.readdirSync(logPath);
            for(let x in arr){
                let mtime = fs.statSync(path.join(conf.WORK_DIR,'logs','jobs',arr[x])).mtime;
                mtime = new Date(mtime).getTime();
                if(mtime >= startTime){
                    fs.copyFileSync(path.join(conf.WORK_DIR,'logs','jobs',arr[x]),path.join(conf.WORK_DIR,'logs','jobs',arr[x] + '.' + nowTime))
                    cpFiles.push(path.join(conf.WORK_DIR,'logs','jobs',arr[x] + '.' + nowTime));
                    let selfSize = fs.statSync(path.join(conf.WORK_DIR,'logs','jobs',arr[x] + '.' + nowTime)).size;
                    if(selfSize * 1 > 0) {
                        stream.addEntry(path.join(conf.WORK_DIR, 'logs', 'jobs', arr[x] + '.' + nowTime));
                        size += selfSize
                        logger.info('Add File :', path.join(conf.WORK_DIR, 'logs', 'jobs', arr[x] + '.' + nowTime))
                    }
                }
            }
        }catch (e) {
            logger.error(e)
        }
        try{
            let logPath = path.join(conf.WORK_DIR,'logs','tapdata-agent');
            let arr = fs.readdirSync(logPath);
            for(let x in arr){
                let mtime = fs.statSync(path.join(conf.WORK_DIR, 'logs', 'tapdata-agent', arr[x])).mtime;
                mtime = new Date(mtime).getTime();
                if (mtime >= startTime) {
                    let selfSize = fs.statSync(path.join(conf.WORK_DIR, 'logs', 'tapdata-agent', arr[x])).size;
                    if (selfSize * 1 > 0) {
                        if(arr[x] === 'agent-resident.log') {
                            fs.copyFileSync(path.join(conf.WORK_DIR,'logs','tapdata-agent',arr[x]),path.join(conf.WORK_DIR,'logs','tapdata-agent',arr[x] + '.' + nowTime))
                            cpFiles.push(path.join(conf.WORK_DIR,'logs','tapdata-agent',arr[x] + '.' + nowTime))
                            stream.addEntry(path.join(conf.WORK_DIR, 'logs', 'tapdata-agent', arr[x] + '.' + nowTime));
                            size += selfSize;
                            logger.info('Add File :', path.join(conf.WORK_DIR, 'logs', 'tapdata-agent', arr[x] + '.' + nowTime))
                        }else {
                            stream.addEntry(path.join(conf.WORK_DIR, 'logs', 'tapdata-agent', arr[x]));
                            size += selfSize;
                            logger.info('Add File :', path.join(conf.WORK_DIR, 'logs', 'tapdata-agent', arr[x]))
                        }
                    }
                }
            }
        }catch (e) {
            logger.error(e)
        }
        logger.info('File totalSize:',size)
        let readSize = 0;
        stream.on('data', (chunk) => {
            readSize += chunk.length;
            const now = (new Date()).getTime();
            if(now - lastSendTime > 1000){
                data.status = 0;
                data.uploadRatio = ((readSize/size)*100).toFixed(0) * 1;
                data.callbackType = 'heartbeat';
                data = handelNull(data);
                let obj = {
                    'type': 'uploadLog',
                    'timestamp': now,
                    'data': data,
                };
                obj.sign = signString(JSON.stringify(obj));
                if(ws){
                    ws.send(JSON.stringify(obj));
                }
                logger.info('Heartbeat:',data.uploadRatio,'%');
                lastSendTime = now;
            }
        });
        let bufferStream = new streamObj.PassThrough();
        const pipeline1 = streamObj.pipeline;
        pipeline1(stream,zlib.createGzip(),bufferStream,(err)=>{
            if(err){
                logger.error(err);
            }
        });
        let gzSize = 0;
        bufferStream.on('data', (chunk) => {
            gzSize += chunk.length;
        });
        let result = await client.putStream(data.uploadAddr, bufferStream);
        if(result && result.res && result.res.status === 200){
            const now = (new Date()).getTime();
            data.fileSize = gzSize;
            data.status = 1;
            data.uploadRatio = 100;
            data.callbackType = 'result';
            data.downloadAddr = result.url;
            data = handelNull(data);
            let obj = {
                'type': 'uploadLog',
                'timestamp': now,
                'data': data,
            };
            obj.sign = signString(JSON.stringify(obj));
            logger.info('Upload finished,send Msg:');
            if(ws){
                ws.send(JSON.stringify(obj));
            }
        }else{
            const now = (new Date()).getTime();
            data.status = 2;
            data.callbackType = 'result';
            delete data.uploadRatio;
            data.errorMsg = JSON.stringify(result);
            data = handelNull(data);
            let obj = {
                'type': 'uploadLog',
                'timestamp': now,
                'data': data,
            };
            obj.sign = signString(JSON.stringify(obj));
            logger.error('Upload Error,send Msg:',data.errorMsg);
            if(ws){
                ws.send(JSON.stringify(obj));
            }
        }
    } catch (e) {
        const now = (new Date()).getTime();
        data.status = 2;
        data.errorMsg = e.toString();
        data.callbackType = 'result';
        delete data.uploadRatio;
        data = handelNull(data);
        let obj = {
            'type': 'uploadLog',
            'timestamp': now,
            'data': data,
        };
        obj.sign = signString(JSON.stringify(obj));
        logger.error('Upload Error,send Msg:',data.errorMsg);
        if(ws){
            ws.send(JSON.stringify(obj));
        }
    } finally {
        for(let x in cpFiles){
            try{
                fs.unlinkSync(cpFiles[x]);
            }catch (e) {
                logger.error(e)
            }
        }
    }
}

function signString(str) {
    return basic.md5('tapdata' + str + '20200202');
}

function handelNull(obj){
    let nObj = {};
    for(let x in obj){
        if(obj[x] !== null){
            nObj[x] = obj[x];
        }
    }
    return nObj;
}