const path = require('path');
const fs = require('fs');
const YAML = require('yamljs');
const basic = require("../basic");
const CryptoJS = require("crypto-js");
const prompts = require("prompts");
const os = require("os");
const readline = require("readline");
const _platform = os.platform();
const _linux = (_platform === 'linux');
const _windows = (_platform === 'win32');

exports.updateBackend = async function(arguments) {
    let sourcePath = '';
    let targetPath = '';
    for(let i=0;i<arguments.length;i++){
        if(arguments[i] === '--source' && arguments.length > i+1){
            sourcePath = arguments[i+1];
        }
        if(arguments[i] === '--target' && arguments.length > i+1){
            targetPath = arguments[i+1];
        }
    }
    if(sourcePath === '' || targetPath === ''){
        console.error("Update error.Please check your argument.");
        return;
    }
    let sourceProcessId = "";
    let targetProcessId = "";
    let sWorkDir;
    let tWorkDir;
    if(fs.existsSync(path.join(sourcePath,'.workDir'))){
        sWorkDir = fs.readFileSync(path.join(sourcePath,'.workDir'),'utf8').toString();
    }else{
        console.error("Update error.Source path is not exists.");
        return;
    }
    if(fs.existsSync(path.join(targetPath,'.workDir'))){
        tWorkDir = fs.readFileSync(path.join(targetPath,'.workDir'),'utf8').toString();
    }else{
        console.error("Update error.Target path is not exists.");
        return;
    }
    console.info('Check path OK.');
    try{
        sourceProcessId = fs.readFileSync(path.join(sWorkDir,'agent.yml'),'utf8').toString();
        sourceProcessId = sourceProcessId.split('agentId:')[1].split('}')[0].trim();
    }catch (e) {
        console.error("Update error.Can't read the source processId.");
        return;
    }
    try{
        targetProcessId = fs.readFileSync(path.join(tWorkDir,'agent.yml'),'utf8').toString();
        targetProcessId = targetProcessId.split('agentId:')[1].split('}')[0].trim();
    }catch (e) {
        console.error("Update error.Can't read the target processId.");
        return;
    }
    console.info('Check processId OK.');
    let sConfig = getConfig(sWorkDir);
    if(!sConfig){
        return;
    }
    let sourceBackendStopped = await checkBackend(sourcePath);
    if(sourceBackendStopped){
        console.error("Update error.Source engine is not running.");
        return;
    }
    let targetBackendStopped = await checkBackend(targetPath);
    if(targetBackendStopped){
        console.error("Update error.Target engine is not running.");
        return;
    }
    const mongoDBClient = await getMongoDB(sConfig,sWorkDir);
    if(!mongoDBClient){
        return;
    }
    const mongoDB = mongoDBClient.db();
    let Setting = await mongoDB.collection("Settings").findOne({key:'jobHeartTimeout'});
    if(!Setting){
        console.error("Update error.Can't get the Job Heart Timeout setting.");
        await mongoDBClient.close();
        return;
    }
    const jobHeartTimeout = (new Date().getTime()) - (parseInt(Setting.value) * 2);
    const sourceWorker = await mongoDB.collection("Workers").findOne({process_id:sourceProcessId,ping_time:{$gte:jobHeartTimeout}});
    if(!sourceWorker){
        console.error("Update error.Source engine is not running.");
        await mongoDBClient.close();
        return;
    }
    const targetWorker = await mongoDB.collection("Workers").findOne({process_id:targetProcessId,ping_time:{$gte:jobHeartTimeout}});
    if(!targetWorker){
        console.error("Update error.Target engine is not running.");
        await mongoDBClient.close();
        return;
    }
    console.info('Check source and target engine status OK.');
    const sourceTaskNumbers = await mongoDB.collection("Task").countDocuments({agentId:sourceProcessId,pingTime:{$gte:jobHeartTimeout},status:'running'});
    if(!sourceTaskNumbers || sourceTaskNumbers === 0){
        console.info("Any Task running in source engine.");
    } else {
        if(sourceTaskNumbers === 1){
            console.info(sourceTaskNumbers + " task running in source engine.");
        } else {
            console.info(sourceTaskNumbers + " tasks running in source engine.");
        }
    }
    if(!await askUser()){
        console.info( "Exit update.");
        await mongoDBClient.close();
        return;
    }
    await stopBackend(sourcePath);
    let waitTimes = 10;
    while(true){
        let res = await checkBackend(sourcePath);
        if(res){
            break;
        }
        if(waitTimes<0){
            console.info( "Update error.Stop engine timeout.");
            await mongoDBClient.close();
            return
        }
        await basic.sleep(1000);
        waitTimes--;
    }
    console.info('Source engine stopped.' );
    await basic.sleep(5000);
    const waittingStart = await mongoDB.collection("Task").countDocuments({agentId:sourceProcessId,pingTime:{$gte:jobHeartTimeout},status:'running'});
    await mongoDB.collection("Task").updateMany({agentId:sourceProcessId,pingTime: {$gte:jobHeartTimeout},status:'running'},{$set:{agentId:targetProcessId,pingTime:1}});
    console.info('Transfer task:' + waittingStart + ' running task:0,please waiting...' );
    waitTimes = 120;
    while (true){
        const jobHeartTimeout = (new Date().getTime()) - (parseInt(Setting.value) * 2);
        let runningNumber = await mongoDB.collection("Task").countDocuments({agentId:targetProcessId,pingTime:{$gte:jobHeartTimeout},status:'running'});
        readline.clearLine(process.stdout, 0);
        readline.moveCursor(process.stdout, 0,-1);
        console.info('Transfer task:' + waittingStart + ' running task:' + runningNumber + ',please waiting...' );
        if(runningNumber >= sourceTaskNumbers){
            console.info( "Update finished.All Task are running.");
            break;
        }
        if(waitTimes<0){
            console.info( "Update finished.Please check your Task status.");
            break;
        }
        await basic.sleep(1000);
        waitTimes--;
    }
    await mongoDBClient.close();
}
async function checkBackend(dir){
    let list;
    let stopped = true;
    if (_windows) {
        list = await require("../ps_win").getProcessesWindows();
    }else {
        list = await require("getprocesses").getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
    }
    let checkBackStr = path.join(dir,'components','tapdata-agent');
    let ticdcStr = path.join(dir,'components','ticdc');
    for (let i = 0; i < list.length; i++) {
        if (list[i].command.indexOf(checkBackStr) >= 0 || list[i].arguments.join(' ').indexOf(checkBackStr) >= 0) {//conf.COMPONENTS_DIR + arguments.join(' ')
            stopped = false;
        }
        if (list[i].command.indexOf(ticdcStr) >= 0 || list[i].arguments.join(' ').indexOf(ticdcStr) >= 0) {//conf.COMPONENTS_DIR + arguments.join(' ')
            stopped = false;
        }
    }
    return stopped;
}

async function stopBackend(dir){
    let list;
    if (_windows) {
        list = await require("../ps_win").getProcessesWindows();
    }else {
        list = await require("getprocesses").getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
    }
    let checkBackStr = path.join(dir,'components','tapdata-agent');
    let ticdcStr = path.join(dir,'components','ticdc');
    let tapdataAgent = path.join(dir,'.tapdata-agent');
    let newTapdataAgent = path.join(dir,'tapdata-agent');
    for (let i = 0; i < list.length; i++) {
        if (list[i].command.indexOf(checkBackStr) >= 0 || list[i].arguments.join(' ').indexOf(checkBackStr) >= 0) {//conf.COMPONENTS_DIR + arguments.join(' ')
            basic.killPid(list[i].pid, {_windows:_windows,_linux:_linux},true);
        }
        if (list[i].command.indexOf(ticdcStr) >= 0 || list[i].arguments.join(' ').indexOf(ticdcStr) >= 0) {//conf.COMPONENTS_DIR + arguments.join(' ')
            basic.killPid(list[i].pid,{_windows:_windows,_linux:_linux},true);
        }
        if ((list[i].command.indexOf(tapdataAgent) >= 0 || list[i].command.indexOf(newTapdataAgent) >= 0) &&
            list[i].arguments.join(' ').indexOf("components")<0 &&
            list[i].arguments.join(' ').indexOf("./subReport")<0 &&
            list[i].arguments.join(' ').indexOf("./subReportStats")<0 &&
            list[i].arguments.join(' ').indexOf("./subProcess")<0) {//conf.COMPONENTS_DIR + arguments.join(' ')
            basic.killPid(list[i].pid,{_windows:_windows,_linux:_linux},true);
        }
    }
}

async function askUser(){
    let questions = [
        {
            type: 'confirm',
            name: 'confirm',
            message: "Are you sure update the engine?",
            initial: false
        },
    ];
    const response = await prompts(questions);
    return response.confirm;
}
async function getMongoDB(conf,workDir){
    const MongoClient = require('mongodb').MongoClient;
    let mongoUri = conf.uri;
    let mongoOpts = {
        'useNewUrlParser': true,
        'useUnifiedTopology': true
    };
    if(conf.ssl === 'true' && !basic.isEmpty(conf.CA_FILENAME) && !basic.isEmpty(conf.CERTKEY_FILENAME)){
        mongoOpts['sslValidate'] = true;
        try {
            const ca = [fs.readFileSync(path.join(workDir, conf.CERT_DIR, conf.CA_FILENAME))];
            const cert = fs.readFileSync(path.join(workDir, conf.CERT_DIR, conf.CERTKEY_FILENAME));
            mongoOpts['ssl'] = true;
            mongoOpts['sslCA'] = ca;
            mongoOpts['sslCert'] = cert;
            mongoOpts['sslKey'] = cert;
            mongoOpts['checkServerIdentity'] = false;
        }catch (e) {
            console.info("Update error.Can't read the SSL files.");
            return;
        }
    }
    try {
        if (!basic.isEmpty(conf.sslPEMKeyFilePassword)) {
            mongoOpts['sslPass'] = conf.sslPEMKeyFilePassword;
        }
        const client = new MongoClient(mongoUri, mongoOpts);
        await client.connect();
        return client;
    }catch (e) {
        console.info("Update error.");
        console.info(e);
    }
}

function getConfig(sWorkDir){
    let sUri;
    let MONGO_SSL_CA = '';
    let MONGO_SSL_CERT_KEY = '';
    let CERT_DIR = 'cert';
    let CA_FILENAME = 'ca.pem';
    let CERTKEY_FILENAME = 'certificateKey.pem';
    let sslPEMKeyFilePassword = '';
    let ssl = '';
    if(fs.existsSync(path.join(sWorkDir,'application.yml'))){
        try{
            let sApplication = fs.readFileSync(path.join(sWorkDir,'application.yml'),'utf8').toString();
            sApplication = YAML.parse(sApplication.toString());
            if (!basic.isEmpty(sApplication['spring']['data']['mongodb']['password'])) {
                sApplication['spring']['data']['mongodb']['password'] = CryptoJS.RC4.decrypt(sApplication['spring']['data']['mongodb']['password'], basic.getKey()).toString(CryptoJS.enc.Utf8);
            }
            if (!basic.isEmpty(sApplication['spring']['data']['mongodb']['sslPEMKeyFilePassword'])) {
                sApplication['spring']['data']['mongodb']['sslPEMKeyFilePassword'] = CryptoJS.RC4.decrypt(sApplication['spring']['data']['mongodb']['sslPEMKeyFilePassword'], basic.getKey()).toString(CryptoJS.enc.Utf8);
            }
            sUri = constructMetaDBSSLParams(constructMetaDBURI(sApplication), sApplication);
            if ("true" === sApplication['spring']['data']['mongodb']['ssl'] && !basic.isEmpty(sApplication['spring']['data']['mongodb']['sslCA'])) {
                MONGO_SSL_CA = fs.readFileSync(path.join(sWorkDir, CERT_DIR, CA_FILENAME));
                MONGO_SSL_CERT_KEY = fs.readFileSync(path.join(sWorkDir, CERT_DIR, CERTKEY_FILENAME));
                ssl = 'true';
            }
            if (!basic.isEmpty(sApplication['spring']['data']['mongodb']['sslPEMKeyFilePassword'])) {
                sslPEMKeyFilePassword = CryptoJS.RC4.decrypt(sApplication['spring']['data']['mongodb']['sslPEMKeyFilePassword'], basic.getKey()).toString(CryptoJS.enc.Utf8);
            }
            return {
                uri:sUri,
                MONGO_SSL_CA:MONGO_SSL_CA,
                MONGO_SSL_CERT_KEY:MONGO_SSL_CERT_KEY,
                ssl:ssl,
                sslPEMKeyFilePassword:sslPEMKeyFilePassword
            }
        }catch (e) {
            console.info("Update error.Can't read the source application.yml.please check the config.");
        }
    }else{
        console.info("Update error.Source application.yml is not exists.");
    }
}

function constructMetaDBURI(yamlObj) {
    let uri = yamlObj['spring']['data']['mongodb']['mongoConnectionString'];
    if(basic.isEmpty(uri))return '';
    if (uri.indexOf('mongodb://') < 0) {
        uri = 'mongodb://' + uri;
    }
    if (!isEmpty(yamlObj['spring']['data']['mongodb']['username']) &&
        !isEmpty(yamlObj['spring']['data']['mongodb']['password']) &&
        !isEmpty(yamlObj['spring']['data']['mongodb']['authenticationDatabase'])) {
        let encodeUsername = encodeURIComponent(yamlObj['spring']['data']['mongodb']['username']);
        let encodePassword = encodeURIComponent(yamlObj['spring']['data']['mongodb']['password']);
        uri = uri.replace("mongodb://", "mongodb://" + encodeUsername + ":" + encodePassword + '@');
        if (uri.indexOf('?') > 0) {
            uri += '&authSource=' + yamlObj['spring']['data']['mongodb']['authenticationDatabase']
        } else {
            uri += '?authSource=' + yamlObj['spring']['data']['mongodb']['authenticationDatabase']
        }
    }
    return uri;
}

function isEmpty(obj) {
    return typeof obj === "undefined" || obj === null || obj === "";
}

function constructMetaDBSSLParams(uri, obj) {
    let mongo_conn = uri;//obj['spring']['data']['mongodb']['uri'];
    if (!isEmpty(mongo_conn)) {
        if (!isEmpty(obj['spring']['data']['mongodb']['sslCertKey'])) {
            mongo_conn += " --ssl --sslAllowInvalidHostnames --sslAllowInvalidCertificates --sslPEMKeyFile " + obj['spring']['data']['mongodb']['sslCertKey'];
        }
        if (!isEmpty(obj['spring']['data']['mongodb']['sslCertKey']) && !isEmpty(obj['spring']['data']['mongodb']['sslPEMKeyFilePassword'])) {
            mongo_conn += " --sslPEMKeyPassword " + obj['spring']['data']['mongodb']['sslPEMKeyFilePassword'];
        }
        if (!isEmpty(obj['spring']['data']['mongodb']['sslCA'])) {
            mongo_conn += " --sslCAFile " + obj['spring']['data']['mongodb']['sslCA'];
        }
    }
    return mongo_conn;
}