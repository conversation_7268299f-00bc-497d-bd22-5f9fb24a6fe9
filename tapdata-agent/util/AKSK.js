const crypto = require('crypto');
const uuid = require('uuid/v4');
function canonicalQueryString(parameters = {}) {
    return Object.keys(parameters)
        .filter(key => key !== "sign")
        .sort()
        .map(key => {
            let value = encodeURIComponent(parameters[key]);
            if (Array.isArray(value)) {
                return value.map( val => encodeURIComponent(key) + "=" + encodeURIComponent(val)).join('&');
            } else {
                return encodeURIComponent(key) + "=" + encodeURIComponent(value);
            }
        })
        .join("&");
}

function signString(stringToSign = '', accessKeySecret) {
    return crypto.createHmac('sha1', accessKeySecret).update(stringToSign).digest()
        .toString('base64');
}

exports.sign = function (accessKey,secretKey,u,body){
    const url = require('url');
    let { query, pathname } = url.parse(u, true);
    let a = query;//{nonce:'aaa',signVersion:'1.0',accessKey:'PEGL75kh6qVBdm00ttHPX8nctT2cUYme',ts:new Date().getTime()};
    query.nonce = uuid();
    query.signVersion = '1.0';
    query.accessKey = accessKey;
    query.ts = new Date().getTime();
    for(let x in query){
        if(['nonce','signVersion','accessKey','ts'].includes(x))
        u = u + '&' + x +'='+query[x]
    }
    let q = canonicalQueryString(query);
    let b = signString(q,secretKey);//GR057waBlh2ZmyEB2bifwTjDv07rCPFw
    //console.info(a+'&sign='+encodeURIComponent(b));
    //console.info(b);
    return u + '&sign='+encodeURIComponent(b)
};
