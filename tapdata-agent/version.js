const basic = require("./basic");
const CryptoJS = require("crypto-js");
const path = require('path');
const fs = require("fs");
const v = "tapdataAgentVersion";
const tapdataVersion = "v1.0.5";

module.exports = function (showVersion,conf) {
    let TAPDATA_HOME = path.dirname(process.execPath);
    let version;
    const backendStauts = basic.readBackendStatus(conf);
    if(backendStauts && backendStauts.version && showVersion){
        console.info("backend version:", backendStauts.version);
    }
    if(v !== "tapdataAgentVersion" && v !== "" || conf.isCloud){
        version = {
            version:v
        }
        if(conf.isCloud){
            version.version = tapdataVersion;
        };
        if(backendStauts && backendStauts.version){
            version.backend = backendStauts.version
        }
        if(showVersion) {
            console.info("version:", version.version);
            return;
        }
        return JSON.stringify(version)
    }
    if(fs.existsSync(path.join(TAPDATA_HOME,'.version'))){
        try {
            version = fs.readFileSync(path.join(TAPDATA_HOME, '.version'), 'utf8').toString();
            version = CryptoJS.RC4.decrypt(version,basic.getKey()).toString(CryptoJS.enc.Utf8);
            if(showVersion) {
                version = JSON.parse(version);
                if (version.version) {
                    console.info("version:", version.version);
                }
                if (version.frontend) {
                    console.info("frontend:", version.frontend);
                }
                if (version.backend) {
                    console.info("backend:", version.backend);
                }
                if (version.apiServer) {
                    console.info("apiServer:", version.apiServer);
                }
                return;
            }
        }catch(e){}
        return version;
    }
    return '';
};