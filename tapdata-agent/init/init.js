/**
 * 系统初始化
 * 与用户交互获得基本配置信息
 * 返回信息包括
 * 用户输入部分：
 * tapdata_port
 * backend_url
 * api_server_port
 * safe
 * mongo_usernameis_ssl
 * mongo_password
 * mongoConnectionString
 * is_ssl
 * mongo_sslCAFile
 * mongo_sslPEMKeyFile
 * mongo_sslPEMKeyFilePassword
 * mongo_authenticationDatabase
 * 其他信息：
 * uri
 * mongo_conn
 * ssl
 * */
const prompts = require('prompts');
const YAML = require('yamljs');
//const os = require("os");
//const {exec} = require('child_process');
const path = require('path');
const fs = require('fs');
const CryptoJS = require("crypto-js");
const basic = require("../basic");
const manage = require('../process/manage');
let downloadUrl = 'http://resource.tapdata.net/package/feagent/';
let serverUrl = 'https://cloud.tapdata.net/';
const getProcesses = require("getprocesses");
const update = require('../util/download').download;
const msg = require('../sendMessage');
const crypto = require("crypto");
const {sendBehavior} = require("../util/sendBehavior");
const { BUILD_IN_MONGODB, SELF_BUILD_MONGODB } = require("../process/mongodb")

module.exports.init = function (arg, conf) {
    //let conf = require("../conf")(undefined, arg);

//let SCRIPT_DIR = conf.SCRIPT_DIR;//'etc';
    //let WORK_DIR = os.homedir() + "/.tapdata";//conf.WORK_DIR;//
    let WORK_DIR = conf.WORK_DIR;
    let tapdata_port = '3030';
    const supportBuildInMongoDB = fs.existsSync(path.join(conf.COMPONENTS_DIR, conf._windows ? 'mongod.exe' : 'mongod'))
    const questions = [
        {
            type: 'text',
            name: 'backend_url',
            message: 'Please enter backend url, comma separated list. e.g.:http://127.0.0.1:3030/ (Default: http://127.0.0.1:3030/): ',//请输入后端连接,逗号分隔列表(默认: http://127.0.0.1:3030): '
            //onRender() {
            //    this.msg = 'Please enter backend url, comma separated list. (Default: http://127.0.0.1:' + tapdata_port + '): ';
            //}
        },
        {
            type: function (prev, values) {
                return values['backend_url'] === '' ? null : null
            },
            name: 'username',
            message: 'Please enter username: '
        },
        {
            type: function (prev, values) {
                return values['backend_url'] === '' ? null : null
            },
            name: 'password',
            message: 'Please enter password: '
        },
        {
            type: function (prev, values) {
                return values['backend_url'] === '' ? 'text' : 'text'
            },
            name: 'tapdata_port',
            message: 'Please enter tapdata port. (Default: 3030): ',//'请输入页面端口(默认: 3030):'
            format: function (value) {
                tapdata_port = value ? value : tapdata_port;
                return value;
            }
        },
        /*
        {
            type: 'text',
            name: 'backend_url',
            message: 'Please enter backend url, comma separated list. (Default: http://127.0.0.1:3030): ',//请输入后端连接,逗号分隔列表(默认: http://127.0.0.1:3030): '
            onRender() {
                this.msg = 'Please enter backend url, comma separated list. (Default: http://127.0.0.1:' + tapdata_port + '): ';
            }
        },
         */
        {
            type: function (prev, values) {
                return values['backend_url'] === '' ? 'text' : 'text'
            },
            name: 'api_server_port',
            message: "Please enter api server port. (Default: 3080): "//'请输入数据分发服务器端口(默认: 3080): '
        },
        {
            type: "confirm",
            name: 'apiServerErrorCode',
            message: "Does API Server response error code?(y/n): ",//'MongoDB中间库是否需要TLS/SSL?(y/n): ',
            initial: true
        },
        {
            type: 'select',
            name: 'mongodbDeployType',
            message: 'Which method is used to initialize the TapData meta-database: ',
            choices: [
                {title: 'Deploy and initialize a new MongoDB single-node replica cluster.', value: BUILD_IN_MONGODB, disabled: !supportBuildInMongoDB},
                {title: 'Using exists self-built MongoDB replica cluster', value: SELF_BUILD_MONGODB},
            ]
        },
        {
            type: function(prev, values) {
                if (values['mongodbDeployType'] === BUILD_IN_MONGODB)
                    return 'number';
                return null;
            },
            initial: '27017',
            name: 'mongodbPort',
            message: 'Please enter mongodb port. (Default: 27017): ',
        },
        {
            type: function (prev, values) {
                if (values['mongodbDeployType'] === BUILD_IN_MONGODB)
                    return null;
                return values['backend_url'] === '' ? 'confirm' : 'confirm'
            },
            name: 'safe',
            message: "Does MongoDB require username/password?(y/n): ",//'MongoDB中间库是否需要账号密码？(y/n): ',
            initial: true
        },
        {
            type: function (prev, values) {
                if (values['mongodbDeployType'] === BUILD_IN_MONGODB)
                    return null;
                return values['safe'] ? 'text' : null
            },
            name: 'mongo_username',
            message: "Please enter MongoDB username (Optional): "//'请输入MongoDB用户名'
        },
        {
            type: function (prev, values) {
                if (values['mongodbDeployType'] === BUILD_IN_MONGODB)
                    return null;
                return values['safe'] ? 'text' : null
            },
            name: 'mongo_password',
            message: "Please enter MongoDB password (Optional): "//'请输入MongoDB密码'
        },
        {
            type: function (prev, values) {
                if (values['mongodbDeployType'] === BUILD_IN_MONGODB)
                    return null;
                return values['backend_url'] === '' ? 'confirm' : 'confirm'
            },
            //type: 'confirm',//prev => prev ? 'confirm' : null,
            name: 'is_ssl',
            message: "Does MongoDB require TLS/SSL?(y/n): ",//'MongoDB中间库是否需要TLS/SSL?(y/n): ',
            initial: true
        },
        {
            type: function (prev, values) {
                if (values['mongodbDeployType'] === BUILD_IN_MONGODB)
                    return null;
                return values['is_ssl'] ? 'text' : null
            },
            name: 'mongo_sslCAFile',
            message: "Please enter MongoDB CA file path: "//'请输入中间库MongoDB CA证书路径: '
        },
        {
            type: function (prev, values) {
                if (values['mongodbDeployType'] === BUILD_IN_MONGODB)
                    return null;
                return values['is_ssl'] ? 'text' : null
            },
            name: 'mongo_sslPEMKeyFile',
            message: "Please enter MongoDB certificate key file path: "//'请输入中间库MongoDB 证书私钥文件路径: '
        },
        {
            type: function (prev, values) {
                if (values['mongodbDeployType'] === BUILD_IN_MONGODB)
                    return null;
                return values['is_ssl'] ? 'text' : null
            },
            name: 'mongo_sslPEMKeyFilePassword',
            message: "Please enter MongoDB certificate key file password(enter if no password): "//'请输入中间库MongoDB 私钥文件密码(没有密码则按回车): '
        },
        {
            type: function (prev, values) {
                if (values['mongodbDeployType'] === BUILD_IN_MONGODB)
                    return null;
                return values['backend_url'] === '' ? 'text' : 'text'
            },
            //type: 'text',
            name: 'mongoConnectionString',
            message: "Please enter MongoDB host, port, database name(Default: 127.0.0.1:27017/tapdata): "//'请输入中间库MongoDB的地址，端口，数据库名称(默认: 127.0.0.1:27017/tapdata): '
        },
        {
            type: function (prev, values) {
                if (values['mongodbDeployType'] === BUILD_IN_MONGODB)
                    return null;
                return values['safe'] ? 'text' : null
            },
            name: 'mongo_authenticationDatabase',
            message: "Please enter MongoDB authentication database(Default: admin): "//'请输入中间库MongoDB的授权数据库(默认: admin): '
        }
    ];
    let questions1 = [
        {
            type: 'text',
            name: 'tapdata_port',
            message: 'Please enter tapdata port. (Default: 3030): ',//'请输入页面端口(默认: 3030):'
            format: function (value) {
                tapdata_port = value ? value : tapdata_port;
                return value;
            }
        },
        {
            type: 'text',
            name: 'backend_url',
            message: 'Please enter backend url, comma separated list. (Default: http://127.0.0.1:3030): ',//请输入后端连接,逗号分隔列表(默认: http://127.0.0.1:3030): '
            onRender() {
                this.msg = 'Please enter backend url, comma separated list. (Default: http://127.0.0.1:' + tapdata_port + '): ';
            }
        },
        {
            type: 'text',
            name: 'api_server_port',
            message: "Please enter api server port. (Default: 3080): "//'请输入数据分发服务器端口(默认: 3080): '
        }
    ];
    console.info("Init tapdata...");//"系统开始初始化"
    const data = fs.readFileSync(WORK_DIR + '/application.yml');
    let yamlObj = YAML.parse(data.toString());
    (async () => {
        /*
        yamlObj['spring'] &&
            yamlObj['spring']['data'] &&
            yamlObj['spring']['data']['mongodb'] &&
            yamlObj['spring']['data']['mongodb']['uri'] &&
            yamlObj['spring']['data']['mongodb']['uri'] !== '' ||
            yamlObj['spring']['data']['mongodb']['uri'] !== 'mongodb://mongo:27017/tapdata'
         */
        for(let i=0;i<arg.length;i++){
            if(arg[i] === '--downloadUrl' && arg.length > i+1 && arg[i+1]){
                downloadUrl = arg[i+1];
            }
            if(arg[i] === '--serverUrl' && arg.length > i+1 && arg[i+1]){
                serverUrl = arg[i+1];
            }
        }
        if(!basic.isEmpty(yamlObj) && yamlObj !== {}){
            try {
                fs.accessSync(path.join(conf.TAPDATA_HOME,'components'), fs.constants.F_OK);
            } catch (e) {
                await newDownload(conf);
                if (arg[0] !== "init") {
                    const data = fs.readFileSync(WORK_DIR + '/application.yml');
                    yamlObj = YAML.parse(data.toString());
                    conf = require("../conf")(yamlObj, arg);
                    await require('../checkGod')(conf);
                    conf.showBackendCheck = false;
                    manage(arg, conf);
                    return;
                }
            }
        }
        if(basic.isEmpty(yamlObj) || yamlObj === {}){
            yamlObj = {};
            let accesscode = '';
            let needInit = true;
            let userId = '';
            /*
            for (let i = 0; i < arg.length; i++) {
                if (arg[i] === "--token" && arg.length > i+2) {
                    let token = arg[i+1];
                    userId = arg[i+2];
                    accesscode = await getAccessCodeByToken(token,userId);
                    if(accesscode){
                        try {
                            needInit = false;
                        }catch(e){
                            console.info(e);
                        }
                    }
                }
            }
             */
            let isDFS = false;
            let token
            for (let i = 0; i < arg.length; i++) {
                if (arg[i] === "--token" && arg.length > i+1) {
                    token = arg[i+1];
                    isDFS = true
                    try{
                        let key = '5fa25b06ee34581d';
                        let iv = '5fa25b06ee34581d';
                        let tokenObj = decrypt(key, iv, token);
                        tokenObj = JSON.parse(tokenObj);
                        if(tokenObj.backend_url && tokenObj.backend_url.length > 6) {
                            serverUrl = tokenObj.backend_url;
                            if (serverUrl.substring(serverUrl.length - 5) === '/api/') {
                                serverUrl = serverUrl.substring(0, serverUrl.length - 4);
                            }
                        }
                        userId = arg[i+2]
                    }catch(e){}
                }
            }
            if(!isDFS) {
                const response = await prompts(questions);
                //accesscode = await getAccessCode(response);
                await checkInfo(response, yamlObj, conf, accesscode);
            }
            if (!await newDownload(conf)) return;
            if(isDFS){
                yamlObj['tapdata'] = {}
                yamlObj['tapdata']['conf'] = {};
                yamlObj['tapdata']['conf']['tapdataPort'] = "3030";
                yamlObj['tapdata']['conf']['backendUrl'] = serverUrl + "api/";
                yamlObj['tapdata']['conf']['apiServerPort'] = "";
                yamlObj['tapdata']['cloud'] = {};
                yamlObj['tapdata']['cloud']['accessCode'] = accesscode;
                yamlObj['tapdata']['cloud']['baseURLs'] = serverUrl + "api/";
                yamlObj['tapdata']['cloud']['username'] = userId;
                if(token){
                    yamlObj['tapdata']['cloud']['token'] = token;
                }
                yamlObj['spring'] = {data:{mongodb:{}}};
                yamlObj['spring']['data']['mongodb']['username'] = "";
                yamlObj['spring']['data']['mongodb']['password'] = "";
                yamlObj['spring']['data']['mongodb']['mongoConnectionString'] = "";
                yamlObj['spring']['data']['mongodb']['uri'] = "";
                yamlObj['spring']['data']['mongodb']['ssl'] = "";
                yamlObj['spring']['data']['mongodb']['sslCA'] = "";
                yamlObj['spring']['data']['mongodb']['sslCertKey'] = "";
                yamlObj['spring']['data']['mongodb']['sslPEMKeyFilePassword'] = "";
                yamlObj['spring']['data']['mongodb']['authenticationDatabase'] = "";
                let yamlStr = YAML.stringify(yamlObj, 5);
                fs.writeFileSync(conf.WORK_DIR + '/application.yml', yamlStr);
            }
        }
        else if (!conf.isNewSystem && arg[0] !== "init" &&
            (
                basic.isEmpty(yamlObj['tapdata']) ||
                basic.isEmpty(yamlObj['tapdata']['conf']) ||
                basic.isEmpty(yamlObj['tapdata']['conf']['backendUrl'])
            )
        ) {
            let obj = {};
            obj.tapdata_port = '';
            obj.api_server_port = '';
            obj.mongoConnectionString = '';
            obj.backend_url = '';
            let response = {};
            try {
                let tapdataConfFile = path.join(WORK_DIR, 'tapdata.conf');
                fs.accessSync(tapdataConfFile, fs.constants.F_OK);
                let tapdataConf = fs.readFileSync(tapdataConfFile).toString();
                let tapdataConfArr = tapdataConf.split(/[\n\r]/);
                for (let i = 0; i < tapdataConfArr.length; i++) {
                    let str = tapdataConfArr[i].split('=');
                    if (str[0] === 'tapdata_port') obj.tapdata_port = str[1];
                    if (str[0] === 'api_server_port') obj.api_server_port = str[1];
                    if (str[0] === 'mongoConnectionString') obj.mongoConnectionString = str[1];
                    if (str[0] === 'backend_url') obj.backend_url = str[1];
                }
            } catch (e) {
                response = await prompts(questions1);
            }
            await checkInfoUpdate(response, yamlObj, obj, conf);
        } else {
            const response = await prompts(questions);
            await checkInfo(response, yamlObj, conf);
        }
        if (arg[0] !== "init") {
            const data = fs.readFileSync(WORK_DIR + '/application.yml');
            yamlObj = YAML.parse(data.toString());
            conf = require("../conf")(yamlObj, arg);
            await require('../checkGod')(conf);
            conf.showBackendCheck = false;
            manage(arg, conf);
        }
    })();
};

function checkInfoUpdate(obj, yamlObj, oldConf, conf) {
    if (oldConf.tapdata_port !== '') obj.tapdata_port = oldConf.tapdata_port;
    if (oldConf.api_server_port !== '') obj.api_server_port = oldConf.api_server_port;
    if (oldConf.mongoConnectionString !== '') obj.mongoConnectionString = oldConf.mongoConnectionString;
    if (oldConf.backend_url !== '') obj.backend_url = oldConf.backend_url;
    let uri = yamlObj['spring']['data']['mongodb']['uri'];
    let userName = '';
    let password = '';
    if (uri.indexOf(':') > 0 && uri.indexOf('@') > 0) {
        let namePassword = uri.substring(uri.indexOf('//') + 2);
        userName = namePassword.substring(0, namePassword.indexOf(':'));
        password = namePassword.substring(namePassword.indexOf(':') + 1, namePassword.indexOf('@'));
    }
    if (isEmpty(obj.tapdata_port)) obj.tapdata_port = '3030';
    if (isEmpty(obj.backend_url)) obj.backend_url = 'http://127.0.0.1:' + obj.tapdata_port;
    let backend_api_url = '';
    let backendList = obj.backend_url.split(",");
    for (let i = 0; i < backendList.length; i++) {
        if (backendList[i].indexOf('/api') < 0) {
            if(backendList[i].substring(backendList[i].length-1) !== '/')backendList[i] = backendList[i] + '/'
            backend_api_url += backendList[i] + 'api/,';
        }
        else
            backend_api_url += backendList[i] + ',';
    }
    backend_api_url = backend_api_url.substring(0, backend_api_url.length - 1);
    obj.backend_url = backend_api_url;
    if (obj.api_server_port === '') obj.api_server_port = '3080';
    let mongoConnectionString = uri;
    if (uri.indexOf('@') > 0)
        mongoConnectionString = 'mongodb://' + uri.substring(uri.indexOf('@') + 1);
    yamlObj['tapdata']['conf'] = {};
    yamlObj['tapdata']['conf']['tapdataPort'] = obj.tapdata_port;
    yamlObj['tapdata']['conf']['backendUrl'] = obj.backend_url;
    yamlObj['tapdata']['conf']['apiServerPort'] = obj.api_server_port;
    if (yamlObj['spring']['data']['mongodb']['ssl'])
        yamlObj['spring']['data']['mongodb']['ssl'] = yamlObj['spring']['data']['mongodb']['ssl'].toString();
    yamlObj['spring']['data']['mongodb']['username'] = userName;
    yamlObj['spring']['data']['mongodb']['password'] = CryptoJS.RC4.encrypt(password, basic.getKey()).toString();
    yamlObj['spring']['data']['mongodb']['mongoConnectionString'] = mongoConnectionString;
    delete yamlObj['spring']['data']['mongodb']['conn_params'];
    let authenticationDatabase = mongoConnectionString.split("authSource=");
    if(authenticationDatabase.length>1){
        authenticationDatabase = authenticationDatabase[1].split("&")[0];
    }else{
        authenticationDatabase = 'admin';
    }
    yamlObj['spring']['data']['mongodb']['authenticationDatabase'] = authenticationDatabase;
    delete yamlObj['spring']['data']['mongodb']['uri'];//obj.uri;
    if (isEmpty(yamlObj['tapdata']['conf']['tapdataJavaOpts'])) {
        yamlObj['tapdata']['conf']['tapdataJavaOpts'] = '';
    }
    if (isEmpty(yamlObj['tapdata']['conf']['SCRIPT_DIR'])) {
        yamlObj['tapdata']['conf']['SCRIPT_DIR'] = 'etc';
    }
    importDataToMongo(obj);
    let yamlStr = YAML.stringify(yamlObj, 5);
    fs.writeFileSync(conf.WORK_DIR + '/application.yml', yamlStr);
    console.info("System initialized. To start Tapdata, run: tapdata start");//"系统初始化完成,系统启动命令：tapdata start"
    return obj;
}
function getAccessCode(obj){
    //console.info(obj.backend_url);
    if(obj.backend_url !== ''){

    }
    else {
        return new Promise(async (resolve, reject)=>{
            const axios = require("axios");
            try {
                const loginResponse = await axios.post(serverUrl+'api/users/login?access_token=null', {
                    password: obj.password,
                    email: obj.username
                }, {
                    headers: {
                        "content-type": "application/json"
                    }
                });

                if (loginResponse.status !== 200) {
                    console.info("Login failed!");
                    return;
                }

                const userResponse = await axios.get('https://cloud.tapdata.net/api/users/' + loginResponse.data.userId + '?access_token=' + loginResponse.data.id);

                if (userResponse.status !== 200) {
                    console.info("Login failed!");
                    return;
                }

                resolve(userResponse.data.data.accesscode);
            } catch (error) {
                console.info("Login failed!");
                reject(error);
            }
        })
    }
}
module.exports.deleteConf =  async function deleteConf(args){
    const os = require("os");
    const _platform = os.platform();
    const _linux = (_platform === 'linux');
    const _windows = (_platform === 'win32');
    let fileName = basic.getWorkDir(args);// conf.WORK_DIR;
    let needDeleteConf = false;
    try {
        if (_windows)
            fileName += "\\application.yml";
        else
            fileName += "/application.yml";
        fs.accessSync(fileName, fs.constants.F_OK);
        let data = fs.readFileSync(fileName);
        let yamlObj = YAML.parse(data.toString());
        if(yamlObj && yamlObj.tapdata && yamlObj.tapdata.cloud && yamlObj.tapdata.cloud.token && yamlObj['tapdata']['conf']){
            needDeleteConf = true;
        }
    } catch (e) {
    }
    if(needDeleteConf === true){
        try{
            fs.unlinkSync(fileName, {'recursive': true});
        }catch(e){
            console.info(e);
        }
        console.info("Removed the configuration file.");
    }
    return true
}
module.exports.checkConfAndProcess =  async function checkConfAndProcess(args){
    const os = require("os");
    const _platform = os.platform();
    const _linux = (_platform === 'linux');
    const _windows = (_platform === 'win32');
    let list;
    if (_windows) {
        list = await require("../ps_win").getProcessesWindows();
    }else {
        list = await getProcesses.getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
    }
    let checkBackStr = '';
    let newCheckBackStr = '';
    if(_windows) {
        checkBackStr = "\\tapdata-agent";
    }else if(_linux){
        checkBackStr = "/.tapdata-agent";
        newCheckBackStr = "/tapdata-agent";
    }
    checkBackStr = basic.getExecStr({_windows:_windows},checkBackStr);
    let TAPDATA_HOME = path.dirname(process.execPath);//process.cwd();//process.argv[1]
    if(TAPDATA_HOME === "/")TAPDATA_HOME = "";
    let checkBackStr1 = path.join(TAPDATA_HOME,'components','tapdata-agent');
    let needStopAgent = false;
    let needDeleteConf = false;
    for (let i = 0; i < list.length; i++) {
        if (( list[i].command.indexOf(checkBackStr) >= 0 || list[i].command.indexOf(newCheckBackStr) >= 0 )&& list[i].arguments.join(' ').indexOf("components")<0) {
            needStopAgent = true;
            break;
        }
        if (list[i].command.indexOf(checkBackStr1) >= 0 || list[i].arguments.join(' ').indexOf(checkBackStr1) >= 0) {
            needStopAgent = true;
            break;
        }
    }
    let fileName = basic.getWorkDir(args);// conf.WORK_DIR;
    try {
        if (_windows)
            fileName += "\\application.yml";
        else
            fileName += "/application.yml";
        fs.accessSync(fileName, fs.constants.F_OK);
        let data = fs.readFileSync(fileName);
        let yamlObj = YAML.parse(data.toString());
        if(yamlObj && yamlObj.tapdata && yamlObj.tapdata.cloud && yamlObj.tapdata.cloud.token && yamlObj['tapdata']['conf']){
            needDeleteConf = true;
        }else{
            needDeleteConf = false;
        }
    } catch (e) {
    }
    let haveComDir = true;
    try {
        fs.accessSync(checkBackStr1+'.jar')
    }catch(e){
        haveComDir = false;
    }
    if(!haveComDir){
        try{
            fs.rmdirSync(path.join(TAPDATA_HOME,'components'),{recursive:true});
        }catch (e) {}
        try{
            if(needDeleteConf){
                fs.unlinkSync(fileName, {'recursive': true});
            }
            //fs.rmdirSync(basic.getWorkDir(args),{recursive:true});
        }catch (e) {}
        return true
    }
    if(needStopAgent === true || needDeleteConf === true) {
        let questions = [
            {
                type: 'confirm',
                name: 'clearConf',
                yesOption: "(y/n)",
                noOption: "(y/n)",
                message: "The configuration file is existed or the agent is running, type y update and start service, type n just start service ? :",
                initial: false
            }
        ];
        let response = await prompts(questions);
        let updateDir = '';
        if (response.clearConf === true) {
            for(let i=0;i<args.length;i++){
                if(args[i] === '--downloadUrl' && args.length > i+1 && args[i+1]){
                    downloadUrl = args[i+1];
                }
            }
            const arguments = process.argv.splice(2);
            let conf = require("../conf")(undefined, arguments);
            const checkGod = require('../checkGod');
            let updateStatus = '';
            await checkGod(conf,updateStatus);
            if(needDeleteConf === true){
                try{
                    fs.unlinkSync(fileName, {'recursive': true});
                }catch(e){
                    console.info(e);
                }
                console.info("Removed the configuration file.");
            }

            console.info('we will update agent,pleases waiting ...')
            setTimeout(()=>{
                let args = [];
                args.push('update');
                args.push('downloadUrl');
                args.push(downloadUrl);
                conf.showBackendCheck = false;
                msg(JSON.stringify(args), conf);
            },10000)
            return false;


            updateDir = downloadUrl.split('/')[downloadUrl.split('/').length-2];
            try {
                if(!await update({},downloadUrl, 'tapdata-agent',updateDir)){
                    console.info('Download fail');
                    return false
                }
            }catch(e){
                console.info('Download fail.');
                return false
            }

            if(needStopAgent === true){
                for (let i = 0; i < list.length; i++) {
                    if (list[i].command.indexOf(checkBackStr) >= 0 && list[i].arguments.join(' ').indexOf("components")<0) {
                        basic.killPid(list[i].pid, {_windows:_windows,_linux:_linux});
                    }
                    if (list[i].command.indexOf(checkBackStr1) >= 0 || list[i].arguments.join(' ').indexOf(checkBackStr1) >= 0) {
                        basic.killPid(list[i].pid, {_windows:_windows,_linux:_linux});
                    }
                }
                console.info("Agent down.");
            }
            if(needDeleteConf === true){
                try{
                    fs.unlinkSync(fileName, {'recursive': true});
                }catch(e){
                    console.info(e);
                }
                console.info("Removed the configuration file.");
            }
            let from = path.join(TAPDATA_HOME,updateDir,'tapdata-agent');
            let to = path.join(TAPDATA_HOME,'components','tapdata-agent.jar');
            let bak = path.join(TAPDATA_HOME,'bak','tapdata-agent.jar');
            try {
                fs.accessSync(path.join(TAPDATA_HOME,'bak'), fs.constants.F_OK);
            } catch (e) {
                fs.mkdirSync(path.join(TAPDATA_HOME,'bak'));
            }
            try {
                fs.accessSync(path.join(TAPDATA_HOME,updateDir), fs.constants.F_OK);
            } catch (e) {
                fs.mkdirSync(path.join(TAPDATA_HOME,updateDir));
            }
            try {
                fs.copyFileSync(to, bak);
            }catch(e){
                console.error('Update fail,will start last version agent.')
                return true
            }
            try {
                fs.copyFileSync(from, to);
            }catch(e){
                try {
                    fs.copyFileSync(bak, to);
                }catch(e){}
                console.error('Update fail,will start last version agent.')
                return true
            }
            console.error('Update finished.')
        }
    }
    return true
};

function getAccessCodeByToken(token, userId) {
    return new Promise(async (resolve, reject) => {
        const axios = require("axios");
        //console.info(serverUrl + 'api/users/' + userId + '?access_token=' + token);
        try {
            const response = await axios.get(serverUrl + 'api/users/' + userId + '?access_token=' + token);//https://cloud.tapdata.net

            if (response.status !== 200) {
                console.info("Login failed!");
                reject(new Error("Login failed"));
                return;
            }
            //console.info(response.data);
            resolve(response.data.data.accesscode);
        } catch (error) {
            console.info("Login failed!");
            reject(error);
        }
    })
}

function checkInfo(obj, yamlObj, conf,accesscode) {
    let CERT_DIR = conf.CERT_DIR;//'cert';
    let CA_FILENAME = conf.CA_FILENAME;//'ca.pem';
    let CERTKEY_FILENAME = conf.CERTKEY_FILENAME;//'certificateKey.pem';
    let WORK_DIR = conf.WORK_DIR;
    if (isEmpty(obj.tapdata_port)) obj.tapdata_port = '3030';
    if (obj.backend_url === '') obj.backend_url = 'http://127.0.0.1:' + obj.tapdata_port;

    let backend_api_url = '';
    let backendList = obj.backend_url.split(",");
    for (let i = 0; i < backendList.length; i++) {
        let baseUrl = backendList[i];
        if(baseUrl.substring(baseUrl.length-1) === "/") {
            backend_api_url += backendList[i] + 'api/,';
        }else{
            backend_api_url += backendList[i] + '/api/,';
        }
    }
    backend_api_url = backend_api_url.substring(0, backend_api_url.length - 1);
    obj.backend_url = backend_api_url;

    if (obj.api_server_port === '') obj.api_server_port = '3080';

    //if(obj.times === 0) {
    //    obj.times = 1;
    if (obj['safe'] === 'true') {
        obj.security = 'true';
    } else {
        obj.security = 'false';
    }
    //}
    if (isEmpty(obj.mongo_authenticationDatabase)) obj.mongo_authenticationDatabase = 'admin';
    /*
    if(obj.security === 'true'){
        //obj.mongo_username = obj.mongo_username;
        //obj.mongo_password = obj.mongo_password;

    }
*/
    //if(obj.ssltime === 0){
    //    obj.ssltime = 1;
    if (obj['is_ssl']) {
        obj.ssl = 'true';
    } else {
        obj.ssl = 'false';
    }
    if (obj['apiServerErrorCode']) {
        obj.apiServerErrorCode = 'true';
    } else {
        obj.apiServerErrorCode = 'false';
    }
    //}
    if (obj.ssl === 'true') {
        //判断文件是否存在
        if (!isEmpty(obj.mongo_sslCAFile)) {
            try {
                fs.accessSync(obj.mongo_sslCAFile, fs.constants.F_OK);
                try {
                    let from = obj.mongo_sslCAFile;
                    let to = WORK_DIR + "/" + CERT_DIR + "/" + CA_FILENAME;
                    to = basic.getExecStr(conf, to);
                    fs.copyFileSync(from, to);
                    //fs.writeFileSync(WORK_DIR + "/" + CERT_DIR + "/" + CA_FILENAME, fs.readFileSync(obj.mongo_sslCAFile));
                    //let doStr = "cp " + obj.mongo_sslCAFile + " " + WORK_DIR + "/" + CERT_DIR + "/" + CA_FILENAME;
                    //exec.execSync(doStr);
                } catch (e) {
                    console.error(e);
                }
                obj.mongo_sslCAFile = WORK_DIR + "/" + CERT_DIR + "/" + CA_FILENAME;
            } catch (e) {
                console.error("CA file " + obj.mongo_sslCAFile + " not exists, will skip this config option.");
            }
        } else {
            console.error("CA file " + obj.mongo_sslCAFile + " not exists, will skip this config option.");
        }
        //判断文件是否存在
        if (!isEmpty(obj.mongo_sslPEMKeyFile)) {
            try {
                fs.accessSync(obj.mongo_sslPEMKeyFile, fs.constants.F_OK);
                try {
                    let from = obj.mongo_sslPEMKeyFile;
                    let to = WORK_DIR + "/" + CERT_DIR + "/" + CERTKEY_FILENAME;
                    to = basic.getExecStr(conf, to);
                    fs.copyFileSync(from, to);
                    //fs.writeFileSync(WORK_DIR + "/" + CERT_DIR + "/" + CERTKEY_FILENAME, fs.readFileSync(obj.mongo_sslPEMKeyFile));
                    //let doStr = "cp " + obj.mongo_sslPEMKeyFile + " " + WORK_DIR + "/" + CERT_DIR + "/" + CERTKEY_FILENAME;
                    //exec.execSync(doStr);
                } catch (e) {
                    console.error(e);
                }
                obj.mongo_sslPEMKeyFile = WORK_DIR + "/" + CERT_DIR + "/" + CERTKEY_FILENAME;
                //obj.mongo_sslPEMKeyFilePassword = obj.mongo_sslPEMKeyFilePassword;
            } catch (e) {
                console.error("Pem key file " + obj.mongo_sslPEMKeyFile + " not exists, ssl config will set to false.");
            }
        } else {
            console.error("Pem key file " + obj.mongo_sslPEMKeyFile + " not exists, ssl config will set to false.");
            obj.ssl = 'false';
        }

    } else {
        obj.ssl = 'false';
        obj.mongo_sslCAFile = '';
        obj.mongo_sslPEMKeyFile = '';
        obj.mongo_sslPEMKeyFilePassword = '';
    }
    const mongodbPort = obj.mongodbPort || 27017
    if (obj.mongoConnectionString === '' || !obj.mongoConnectionString) obj.mongoConnectionString = `127.0.0.1:${mongodbPort}/tapdata`;

    let uri = constructMetaDBURI(obj);
    obj.uri = uri;
    let mongodb_uri_password = mongodb_uri_password_desensitization();
    console.info("MongoDB uri: " + mongodb_uri_password + ' ' + uri);
    let mongo_conn = constructMetaDBSSLParams(obj, uri);
    obj.mongo_conn = mongo_conn;
    console.info("MongoDB connection command: mongo " + basic.getExecStr(conf, mongodb_uri_password) + ' ' + basic.getExecStr(conf, mongo_conn));
    if (isEmpty(obj['mongo_username'])) {
        obj['mongo_username'] = '';
    }
    if (isEmpty(obj['mongo_password'])) {
        obj['mongo_password'] = '';
    }
    if(!yamlObj || !yamlObj.tapdata){
        yamlObj = {
            spring:{data:{mongodb:{}}},
            tapdata:{
                cloud:{accessCode:'3324cfdf-7d3e-4792-bd32-571638d4562f',retryTime:'3',baseURLs:''},
                mode: 'cluster',
                conf:{}
            }
        }
    }
    if(accesscode){
        yamlObj['tapdata']['cloud']['accessCode'] = accesscode;
        yamlObj['tapdata']['cloud']['baseURLs'] = obj.backend_url;
        yamlObj['tapdata']['cloud']['username'] = obj.username;
    }
    if (!yamlObj['tapdata']['conf'])
        yamlObj['tapdata']['conf'] = {};
    yamlObj['tapdata']['conf']['tapdataPort'] = obj.tapdata_port;
    yamlObj['tapdata']['conf']['backendUrl'] = obj.backend_url;
    yamlObj['tapdata']['conf']['apiServerPort'] = obj.api_server_port?obj.api_server_port:'';
    yamlObj['tapdata']['conf']['apiServerErrorCode'] = obj.apiServerErrorCode?obj.apiServerErrorCode:'';
    yamlObj['tapdata']['conf']['mongodbDeployType'] = obj.mongodbDeployType ? obj.mongodbDeployType : BUILD_IN_MONGODB;
    yamlObj['tapdata']['conf']['mongodbPort'] = mongodbPort;
    yamlObj['spring']['data']['mongodb']['username'] = obj['mongo_username'];
    yamlObj['spring']['data']['mongodb']['password'] = CryptoJS.RC4.encrypt(obj['mongo_password'], basic.getKey()).toString();
    yamlObj['spring']['data']['mongodb']['mongoConnectionString'] = obj.mongoConnectionString;
    yamlObj['spring']['data']['mongodb']['uri'] = '';//obj.uri;
    yamlObj['spring']['data']['mongodb']['ssl'] = obj.ssl;
    yamlObj['spring']['data']['mongodb']['sslCA'] = basic.getExecStr(conf, obj.mongo_sslCAFile);
    yamlObj['spring']['data']['mongodb']['sslCertKey'] = basic.getExecStr(conf, obj.mongo_sslPEMKeyFile);
    yamlObj['spring']['data']['mongodb']['sslPEMKeyFilePassword'] = CryptoJS.RC4.encrypt(obj['mongo_sslPEMKeyFilePassword'], basic.getKey()).toString();
    yamlObj['spring']['data']['mongodb']['authenticationDatabase'] = obj.mongo_authenticationDatabase;
    if (isEmpty(yamlObj['tapdata']['conf']['tapdataJavaOpts'])) {
        yamlObj['tapdata']['conf']['tapdataJavaOpts'] = '';
    }
    //if (isEmpty(yamlObj['tapdata']['conf']['AGENT_JAR_FILE'])) {
    //    yamlObj['tapdata']['conf']['AGENT_JAR_FILE'] = '';
    //}
    if (isEmpty(yamlObj['tapdata']['conf']['SCRIPT_DIR'])) {
        yamlObj['tapdata']['conf']['SCRIPT_DIR'] = 'etc';
    }
    obj.SCRIPT_DIR = yamlObj['tapdata']['conf']['SCRIPT_DIR'];
    obj.WORK_DIR = WORK_DIR;
    if(!basic.isEmpty(obj.uri)) {
        importDataToMongo(obj);
    }
    let yamlStr = YAML.stringify(yamlObj, 5);
    fs.writeFileSync(WORK_DIR + '/application.yml', yamlStr);
    console.info("System initialized. To start Tapdata, run: tapdata start");//"系统初始化完成,系统启动命令：tapdata start"
    return obj;
}


function constructMetaDBURI(obj) {
    if(basic.isEmpty(obj.mongoConnectionString))return '';
    let uri = obj.mongoConnectionString;
    if (uri.indexOf('mongodb://') < 0) {
        uri = 'mongodb://' + uri;
    }
    if (!isEmpty(obj['mongo_username']) && !isEmpty(obj['mongo_password']) && !isEmpty(obj.mongo_authenticationDatabase)) {
        let encodeUsername = encodeURIComponent(obj['mongo_username']);
        let encodePassword = encodeURIComponent(obj['mongo_password']);
        uri = uri.replace("mongodb://", "mongodb://" + encodeUsername + ":" + encodePassword + '@');
        if (uri.indexOf('?') > 0) {
            uri += '&authSource=' + obj.mongo_authenticationDatabase
        } else {
            uri += '?authSource=' + obj.mongo_authenticationDatabase
        }
    }
    return uri;
}

function mongodb_uri_password_desensitization(obj, str) {
    if (isEmpty(str)) return '';
    let print_uri = str;
    if (print_uri) {
        print_uri = "mongodb://" + print_uri.replace('mongodb://', "").replace(/:.*@/, ":xxxxx@");
    }
    return print_uri;
}

function constructMetaDBSSLParams(obj, uri) {
    if(basic.isEmpty(uri))return '';
    let mongo_conn = uri;
    if (!isEmpty(mongo_conn)) {
        if (!isEmpty(obj.mongo_sslPEMKeyFile)) {
            mongo_conn += " --ssl --sslAllowInvalidHostnames --sslAllowInvalidCertificates --sslPEMKeyFile " + obj.mongo_sslPEMKeyFile;
        }
        if (!isEmpty(obj.mongo_sslPEMKeyFile) && !isEmpty(obj.mongo_sslPEMKeyFilePassword)) {
            mongo_conn += " --sslPEMKeyPassword " + obj.mongo_sslPEMKeyFilePassword;
        }
        if (!isEmpty(obj.mongo_sslCAFile)) {
            mongo_conn += " --sslCAFile " + obj.mongo_sslCAFile;
        }
    }
    return mongo_conn;
}

function isEmpty(obj) {
    return typeof obj === "undefined" || obj === null || obj === "";
}

function importDataToMongo() {//obj
    require("./mongoInit");
    /*
    console.info('Import '+obj.SCRIPT_DIR+'/mongo_setup.js to MongoDB');
    let execString = 'mongo '+obj.mongo_conn+' --eval "var script_dir = \''+obj.SCRIPT_DIR+'\';load(\'./'+obj.SCRIPT_DIR+'/mongo_setup.js\')"';
    execSync(execString);
    console.info('Mongodb configuration is saved to '+obj.WORK_DIR+'/application.yml . You can modify the mongodb connection information here.');

     */
}
async function download(conf){
    const readline = require('readline');
    try {
        fs.accessSync(path.join(conf.TAPDATA_HOME,'components'), fs.constants.F_OK);
    } catch (e) {
        const http = require('https');
        console.info('Download components...');
        const filename = path.join(conf.TAPDATA_HOME,'tapdata.tar.gz');

        try{
            fs.unlinkSync(filename);
        }catch(e){}
        const promise = new Promise(function (resolve, reject) {
            const url = conf._windows ? 'https://cloud.tapdata.net/static/tapdata-win.tar.gz' : 'https://cloud.tapdata.net/static/tapdata-linux.tar.gz';
            const file = fs.createWriteStream(filename);
            http.get(url, (res, err) => {
                if (err){
                    file.close();
                    fs.unlinkSync(filename);
                    reject();
                }
                if (res.statusCode !== 200) {
                    console.info('Download file failed');
                    reject();
                }
                res.on('end', async () => {
                    //console.info('finish download');
                    resolve();
                });
                let cur = 0;
                let len = parseInt(res.headers['content-length'], 10);
                let total = len / 1048576;
                let remaining = 0;
                let start = Date.now();
                let remainingMinutes = 0;
                let remainingSeconds = 0;
                console.info( "Downloading " + (100.0 * cur / len).toFixed(2) + "% " + (cur / 1048576).toFixed(2) + " mb " + ".Total size: " + total.toFixed(2) + " mb");
                res.on('data',(m)=>{
                    cur += m.length;
                    readline.clearLine(process.stdout, 0);
                    readline.moveCursor(process.stdout, 0,-1);
                    console.info( "Downloading " + (100.0 * cur / len).toFixed(2) + "% " + (cur / 1048576).toFixed(2) + " mb " + ".Total size: " + total.toFixed(2) + " mb");
                });
                file.on('finish', () => {
                    file.close();
                }).on('error', (err) => {
                    try {
                        fs.unlinkSync(filename);
                    } catch (e) {
                    }
                });
                res.pipe(file);
            });
        });
        if(!await promise.then(() => {
            return true;
        }, (err) => {
            unlinkYAM(conf);
            return false;
        }))return false;

        console.info('Unpack the files...');
        const tar = require('tar');
        try {
            await tar.x(
                {
                    file: filename,
                    C: conf.TAPDATA_HOME
                }
            );
        }catch (e) {
            fs.unlinkSync(filename);
            await unlinkYAM(conf);
            console.info('Download failed');
            return false;
        }
        try{
            console.info('organize files...');
            let arr = fs.readdirSync(conf.TAPDATA_HOME);
            for (let o in arr) {
                if (/tapdata-v+[\s\S]*?/.test(arr[o])) {
                    try{
                        fs.rmdirSync(path.join(conf.TAPDATA_HOME, arr[o], 'logs\\app.log'));
                    }catch (e) {}
                    try{
                        fs.unlinkSync(path.join(conf.TAPDATA_HOME, arr[o], 'logs\\app.log'));
                    }catch (e) {}
                    try{
                        fs.unlinkSync(path.join(conf.TAPDATA_HOME, arr[o], 'tapdata'));
                    }catch (e) {}
                    let arr1 = fs.readdirSync(path.join(conf.TAPDATA_HOME, arr[o], 'components'));
                    for (let x in arr1) {
                        //console.info(arr1[x]);
                        if (/frontend-+[\s\S]*?\.tar\.gz$/.test(arr1[x])) {
                            fs.unlinkSync(path.join(conf.TAPDATA_HOME, arr[o], 'components',arr1[x]));
                        }
                        if (/logs+[\s\S]*?\.tar\.gz$/.test(arr1[x])) {
                            fs.unlinkSync(path.join(conf.TAPDATA_HOME, arr[o], 'components',arr1[x]));
                        }
                    }
                    //console.info('begin move');
                    arr1 = fs.readdirSync(path.join(conf.TAPDATA_HOME, arr[o]));
                    for (let x in arr1) {
                        //console.info(arr1[x]);
                        if (arr1[x] !== 'logs\\app.log' && arr1[x] !== 'tapdata-env.sh')
                            createDocs(path.join(conf.TAPDATA_HOME, arr[o], arr1[x]), path.join(conf.TAPDATA_HOME, arr1[x]));
                        if(arr1[x] === 'tapdata-env.sh'){
                            fs.writeFileSync(path.join(conf.TAPDATA_HOME, arr1[x]), fs.readFileSync(path.join(conf.TAPDATA_HOME, arr[o], arr1[x])));
                        }
                    }
                    deleteall(path.join(conf.TAPDATA_HOME, arr[o]));
                }
            }
            try{
                fs.unlinkSync(filename);
            }catch (e) {}
        }catch(e){
            console.info(e);
            return false;
        }
        return true;
    }
}
async function unlinkYAM(conf){
    if(conf.backend_url.indexOf('https://cloud.tapdata.net')>-1 || conf.backend_url === ''){
        await fs.unlinkSync(path.join(conf.WORK_DIR , 'application.yml'));
    }
}
function mkdirsSync(dirname) {
    if (fs.existsSync(dirname)) {
        return true;
    } else {
        if (mkdirsSync(path.dirname(dirname))) {
            //console.log("mkdirsSync = " + dirname);
            fs.mkdirSync(dirname);
            return true;
        }
    }
}

function _copy(src, dist) {
    let paths = fs.readdirSync(src);
    paths.forEach(function(p) {
        let _src = src + '/' +p;
        let _dist = dist + '/' +p;
        let stat = fs.statSync(_src);
        if(stat.isFile()) {// 判断是文件还是目录
            fs.writeFileSync(_dist, fs.readFileSync(_src));
        } else if(stat.isDirectory()) {
            copyDir(_src, _dist)// 当是目录是，递归复制
        }
    })
}
function copyDir(src,dist){
    let b = fs.existsSync(dist);
    //console.log("dist = " + dist);
    if(!b){
        //console.log("mk dist = ",dist);
        mkdirsSync(dist);//创建目录
    }
    //console.log("_copy start");
    _copy(src,dist);
}

function createDocs(src,dist,callback){
    //console.log("createDocs...");
    copyDir(src,dist);
    //console.log("copyDir finish exec callback");
    if(callback){
        callback();
    }
}
function deleteall(path) {
    let files = [];
    if(fs.existsSync(path)) {
        files = fs.readdirSync(path);
        files.forEach(function(file, index) {
            let curPath = path + "/" + file;
            if(fs.statSync(curPath).isDirectory()) { // recurse
                deleteall(curPath);
            } else { // delete file
                fs.unlinkSync(curPath);
            }
        });
        fs.rmdirSync(path);
    }
}

async function newDownload(conf){
    const readline = require('readline');
    try {
        fs.accessSync(path.join(conf.TAPDATA_HOME,'components'), fs.constants.F_OK);
    } catch (e) {
        let http = require('http');
        if(downloadUrl.includes("https"))
            http = require('https');
        console.info('Download components...');
        const filename = path.join(conf.TAPDATA_HOME,'tapdata-agent.jar');
        try{
            fs.unlinkSync(filename);
        }catch(e){}
        //const promise = downloadFile('http://127.0.0.1:8080/static/tapdata-agent.jar',filename);

        const promise = new Promise(function (resolve, reject) {
            const url = downloadUrl + 'tapdata-agent';//cloud.tapdata.net
            const file = fs.createWriteStream(filename);
            http.get(url, (res, err) => {
                if (err){
                    file.close();
                    fs.unlinkSync(filename);
                    reject();
                }
                if (res.statusCode !== 200) {
                    console.info('Download file failed');
                    reject();
                }
                res.on('end', async () => {
                    //console.info('finish download');
                    resolve();
                });
                let cur = 0;
                let len = parseInt(res.headers['content-length'], 10);
                let total = len / 1048576;
                let remaining = 0;
                let start = Date.now();
                let remainingMinutes = 0;
                let remainingSeconds = 0;
                let speed = 0;
                let speedStr = "";
                console.info( "Downloading " + (100.0 * cur / len).toFixed(2) + "% " + (cur / 1048576).toFixed(2) + " MB" + ", Total size: " + total.toFixed(2) + " MB");
                res.on('data',(m)=>{
                    cur += m.length;
                    readline.clearLine(process.stdout, 0);
                    readline.moveCursor(process.stdout, 0,-1);
                    speed = Math.floor(cur / (Date.now() - start + 1) / 1024 * 1000);
                    speedStr = speed + " KB/s";
                    if (speed > 1024) {
                        speedStr = (speed / 1024).toFixed(1) + " MB/s";
                    }
                    remaining = Math.floor((len - cur) / (cur / (Date.now() - start + 1)) / 1000);
                    remainingMinutes = Math.floor(remaining / 60);
                    remainingSeconds = remaining - 60 * remainingMinutes;
                    console.info( "Downloading " + (100.0 * cur / len).toFixed(2) + "% " + (cur / 1048576).toFixed(2) + " MB" + ", Total size: " + total.toFixed(2) + " MB, avg speed: " + speedStr + ", remaining time: " + remainingMinutes + "m " + remainingSeconds + "s     ");
                });
                file.on('finish', () => {
                    file.close();
                }).on('error', (err) => {
                    try {
                        fs.unlinkSync(filename);
                    } catch (e) {
                    }
                });
                res.pipe(file);
            });
        });

        if(!await promise.then(() => {
            return true;
        }, (err) => {
            unlinkYAM(conf);
            return false;
        }))return false;
        const filename1 = path.join(conf.TAPDATA_HOME,'log4j2.yml');
        const promise1 = new Promise(function (resolve, reject) {
            //const url = url;//'http://127.0.0.1:8080/static/tapdata-agent.jar';//cloud.tapdata.net
            const file = fs.createWriteStream(filename1);
            http.get( downloadUrl + 'log4j2.yml', (res, err) => {
                if (err){
                    file.close();
                    fs.unlinkSync(filename1);
                    reject();
                }
                if (res.statusCode !== 200) {
                    console.info('Download file failed');
                    reject();
                }
                res.on('end', async () => {
                    //console.info('finish download');
                    resolve();
                });
                let cur = 0;
                let len = parseInt(res.headers['content-length'], 10);
                let total = len / 1024;
                console.info( "Downloading " + (100.0 * cur / len).toFixed(2) + "% " + (cur / 1024).toFixed(2) + " KB " + ".Total size: " + total.toFixed(2) + " KB");
                res.on('data',(m)=>{
                    cur += m.length;
                    readline.clearLine(process.stdout, 0);
                    readline.moveCursor(process.stdout, 0,-1);
                    console.info( "Downloading " + (100.0 * cur / len).toFixed(2) + "% " + (cur / 1048576).toFixed(2) + " KB " + ".Total size: " + total.toFixed(2) + " KB");
                });
                file.on('finish', () => {
                    file.close();
                }).on('error', (err) => {
                    try {
                        fs.unlinkSync(filename1);
                    } catch (e) {
                    }
                });
                res.pipe(file);
            });
        });

        if(!await promise1.then(() => {
            return true;
        }, (err) => {
            unlinkYAM(conf);
            return false;
        }))return false;

        const filename2 = path.join(conf.TAPDATA_HOME,'.version');
        const promise2 = new Promise(function (resolve, reject) {
            //const url = url;//'http://127.0.0.1:8080/static/tapdata-agent.jar';//cloud.tapdata.net
            const file = fs.createWriteStream(filename2);
            http.get( downloadUrl + '.version', (res, err) => {
                if (err){
                    file.close();
                    fs.unlinkSync(filename1);
                    reject();
                }
                if (res.statusCode !== 200) {
                    console.info('Download file failed');
                    reject();
                }
                res.on('end', async () => {
                    //console.info('finish download');
                    resolve();
                });
                let cur = 0;
                let len = parseInt(res.headers['content-length'], 10);
                let total = len / 1024;
                console.info( "Downloading " + (100.0 * cur / len).toFixed(2) + "% " + (cur / 1024).toFixed(2) + " KB " + ".Total size: " + total.toFixed(2) + " KB");
                res.on('data',(m)=>{
                    cur += m.length;
                    readline.clearLine(process.stdout, 0);
                    readline.moveCursor(process.stdout, 0,-1);
                    console.info( "Downloading " + (100.0 * cur / len).toFixed(2) + "% " + (cur / 1048576).toFixed(2) + " KB " + ".Total size: " + total.toFixed(2) + " KB");
                });
                file.on('finish', () => {
                    file.close();
                }).on('error', (err) => {
                    try {
                        fs.unlinkSync(filename1);
                    } catch (e) {
                    }
                });
                res.pipe(file);
            });
        });

        if(!await promise2.then(() => {
            return true;
        }, (err) => {
            unlinkYAM(conf);
            return false;
        }))return false;

        try{
            console.info('organize files...');
            try{
                fs.mkdirSync(path.join(conf.TAPDATA_HOME,'components'));
            }catch(e){}
            try{
                fs.mkdirSync(path.join(conf.TAPDATA_HOME,'etc'));
            }catch(e){}
            let from = path.join(conf.TAPDATA_HOME,'tapdata-agent.jar');
            let to = path.join(conf.TAPDATA_HOME,'components','tapdata-agent.jar');
            fs.copyFileSync(from, to);
            let from1 = path.join(conf.TAPDATA_HOME,'log4j2.yml');
            let to1 = path.join(conf.TAPDATA_HOME,'etc','log4j2.yml');
            fs.copyFileSync(from1, to1);
            fs.unlinkSync(from);
            fs.unlinkSync(from1);
        }catch(e){
            console.info(e);
            sendBehavior(conf,{code:'downloadAgent',result:'failed',msg:e})
            return false;
        }
        sendBehavior(conf,{code:'downloadAgent',result:'successed',msg:''})
        return true;
    }
    return true;
}

function downloadFile(url,filename){
    return new Promise(function (resolve, reject) {
        //const url = url;//'http://127.0.0.1:8080/static/tapdata-agent.jar';//cloud.tapdata.net
        const file = fs.createWriteStream(filename);
        const http = require('https');//https
        http.get(url, (res, err) => {
            if (err){
                file.close();
                fs.unlinkSync(filename);
                reject();
            }
            if (res.statusCode !== 200) {
                console.info('Download file failed');
                reject();
            }
            res.on('end', async () => {
                //console.info('finish download');
                resolve();
            });
            let cur = 0;
            let len = parseInt(res.headers['content-length'], 10);
            let total = len / 1048576;
            let remaining = 0;
            let start = Date.now();
            let remainingMinutes = 0;
            let remainingSeconds = 0;
            let speed = 0;
            let speedStr = "";
            console.info( "Downloading " + (100.0 * cur / len).toFixed(2) + "% " + (cur / 1048576).toFixed(2) + " MB" + ", Total size: " + total.toFixed(2) + " MB");
            res.on('data',(m)=>{
                cur += m.length;
                readline.clearLine(process.stdout, 0);
                readline.moveCursor(process.stdout, 0,-1);
                speed = Math.floor(cur / (Date.now() - start + 1) / 1024 * 1000);
                speedStr = speed + " KB/s";
                if (speed > 1024) {
                    speedStr = (speed / 1024).toFixed(1) + " MB/s";
                }
                remaining = Math.floor((len - cur) / (cur / (Date.now() - start + 1)) / 1000);
                remainingMinutes = Math.floor(remaining / 60);
                remainingSeconds = remaining - 60 * remainingMinutes;
                console.info( "Downloading " + (100.0 * cur / len).toFixed(2) + "% " + (cur / 1048576).toFixed(2) + " MB" + ", Total size: " + total.toFixed(2) + " MB, avg speed: " + speedStr + ", remaining time: " + remainingMinutes + "m " + remainingSeconds + "s     ");
            });
            file.on('finish', () => {
                file.close();
            }).on('error', (err) => {
                try {
                    fs.unlinkSync(filename);
                } catch (e) {
                }
            });
            res.pipe(file);
        });
    });
}

function decrypt(key, iv, crypted) {
    crypted = new Buffer.from(crypted, 'base64').toString('binary');
    var decipher = crypto.createDecipheriv('aes-128-cbc', key, iv);
    var decoded = decipher.update(crypted, 'binary', 'utf8');
    decoded += decipher.final('utf8');
    let rs = new Buffer.from(decoded,'base64').toString()
    return rs;
};