const fs = require('fs');
const basic = require('../basic');
const ObjectId = require('mongodb').ObjectID;
const path = require('path');
module.exports = async function (life, conf,arguments) {
    let dir = conf.TAPDATA_HOME + "/etc/init/";
    dir = basic.getExecStr(conf, dir);
    if(basic.isDebug(arguments)){
        console.info("init MongoDB--read Files");
    }
    let arr = fs.readdirSync(dir);
    if (arr.length === 0) {
        return true;
    }
    arr.sort();
    if(basic.isDebug(arguments)){
        console.info("init MongoDB--get " + arr.length + " files");
        console.info("init MongoDB--connect MongoDB...");
    }
    const MongoClient = require('mongodb').MongoClient;
    let mongoUri = conf.uri;
    let mongoOpts = {
        'useNewUrlParser': true,
        'useUnifiedTopology': true
    };
    if(conf.ssl === 'true' && !basic.isEmpty(conf.CA_FILENAME) && !basic.isEmpty(conf.CERTKEY_FILENAME)){
        mongoOpts['sslValidate'] = true;
        try {
            const ca = [fs.readFileSync(path.join(conf.WORK_DIR, conf.CERT_DIR, conf.CA_FILENAME))];
            const cert = fs.readFileSync(path.join(conf.WORK_DIR, conf.CERT_DIR, conf.CERTKEY_FILENAME));
            mongoOpts['ssl'] = true;
            mongoOpts['sslCA'] = ca;
            mongoOpts['sslCert'] = cert;
            mongoOpts['sslKey'] = cert;
            mongoOpts['checkServerIdentity'] = false;
        }catch (e) {
            console.info(e);
            return;
        }
    }
    if(!basic.isEmpty(conf.sslPEMKeyFilePassword)){
        mongoOpts['sslPass'] = conf.sslPEMKeyFilePassword;
    }
    const client = new MongoClient(mongoUri, mongoOpts);
    try {
        await client.connect();
        const db = client.db();
        console.info("--------------------------------");
        console.info("MongoDB init ...");
        for (let i = 0; i < arr.length; i++) {
            const file = dir + arr[i].toString();
            if(basic.isDebug(arguments)){
                console.info("init MongoDB--exec " + file);
            }
            const js = require(file);
            await js(db,ObjectId);
            if(basic.isDebug(arguments)){
                console.info("init MongoDB--exec " + file + " OK");
            }
        }
        let version = require("../version")(false,conf);
        if(version){
        db.collection("Settings").updateOne({"_id": "66"}, {
            $setOnInsert: {
                "category": "version",
                "key": "version",
                "user_visible": false,
                "hot_reloading": false,
                "category_sort": 1
            },
            $set: {
                "value": version
            }
        }, {upsert: true});
        }
        console.info("MongoDB init finished.");
        console.info("--------------------------------");
    } catch (e) {
        console.info(e);
        return false;
    } finally {
        await client.close();
    }
    return true;
};