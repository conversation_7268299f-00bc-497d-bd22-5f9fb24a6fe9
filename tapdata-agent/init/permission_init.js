module.exports =[{
    name: "Dashboard", description: "门户", need_permission: true,
    resources: [
        {type: "page", path: "/dashboard"},
        {type: "button", code: "dashboard.data-publish"},
        {type: "button", code: "dashboard.data-tendency"},
        {type: "button", code: "dashboard.job-statistics"}
    ]
}, {
    name: "Views metadata", description: "查看数据编目", need_permission: true,
    resources: [
        {type: "page", path: "/metadataDefinition"},
        {type: "page", path: "/metadataInstances/:id"},
        {type: "api", uri: "/MetadataDefinition", method: "get"},
        {type: "api", uri: "/MetadataDefinition/count", method: "get"},
        {type: "api", uri: "/MetadataInstances", method: "get"}
    ]
}, {
    name: "Manage metadata classification", description: "数据编目管理", need_permission: true,
    resources: [
        {type: "button", code: "add_classification"},
        {type: "button", code: "delete_classification"},
        {type: "button", code: "edit_classification"},
        {type: "api", uri: "/MetadataDefinitions", method: "post"},
        {type: "api", uri: "/MetadataDefinitions", method: "delete"},
        {type: "api", uri: "/MetadataInstances", method: "patch"}
    ]
}, {
    name: "Manage collection index", description: "管理数据集索引", need_permission: true,
    resources: [
        {type: "button", code: "add_index"},
        {type: "api", uri: "/api/ScheduleTasks", method: "post"},
        {type: "api", uri: "/api/ScheduleTasks", method: "get"}
    ]
}, {
    name: "Change metadata name", description: "修改元数据名称", need_permission: true,
    resources: [
        {type: "button", code: "change_metadata_name"},
        {type: "api", uri: "/MetadataInstances/:id", method: "patch"}
    ]
}, {
    name: "Manage data model", description: "管理数据模型", need_permission: true,
    resources: [
        {type: "page", path: "/dataModels"},
        {type: "page", path: "/dataModel/:id"},
        {type: "api", uri: "/dataModels", method: "get"},
        {type: "api", uri: "/dataModels/count", method: "get"},
        {type: "api", uri: "/dataModels", method: "post"},
        {type: "api", uri: "/dataModels/:id", method: "get"},
        {type: "api", uri: "/dataModels/:id", method: "delete"},
        {type: "api", uri: "/dataModels/:id", method: "patch"}
    ]
}, {
    name: "View data quality", description: "查看数据质量、过滤规则", need_permission: true,
    resources: [
        {type: "page", path: "/dataQuality"},
        {type: "page", path: "/dataQuality/:id"},
        {type: "page", path: "/dataRules"},
        {type: "api", uri: "/DataCatalogs", method: "get"},
        {type: "api", uri: "/DataRules", method: "get"}
    ]
}, {
    name: "Manage data rules", description: "管理数据过滤规则", need_permission: true,
    resources: [
        {type: "page", path: "/dataRule/:id"},
        {type: "page", path: "/dataRule"},
        {type: "api", uri: "/DataRules", method: "post"},
        {type: "api", uri: "/DataRules", method: "patch"},
        {type: "api", uri: "/DataRules", method: "delete"},
        {type: "button", code: "create_dataRule"},
        {type: "button", code: "edit_dataRule"},
        {type: "button", code: "delete_dataRule"},
        {type: "page", path: '/dataRules'}
    ]
}, {
    name: "Data Publish", description: "数据发布", need_permission: true,
    resources: [
        {type: "page", path: "/modules"},
        {type: "page", path: "/module"},
        {type: "page", path: "/module/:id"}
    ]
}, {
    name: "Api Document and Test", description: "API文档及测试", need_permission: true,
    resources: [
        {type: "page", path: "/apiDocAndTest"}
    ]
}, {
    name: "Api Analysis", description: "API统计", need_permission: true,
    resources: [
        {type: "page", path: "/apiAnalysis"}
    ]
}, {
    name: "Data Explorer", description: "数据浏览", need_permission: true,
    resources: [
        {type: "page", path: "/dataExplorer"}
    ]
}, {
    name: "View jobs", description: "查看任务面板、任务监控", need_permission: true,
    resources: [
        {type: "page", path: "/dataCollect"},
        {type: "page", path: "/monitor/:id"},
        {type: "api", uri: "/Jobconnections", method: "get"},
        {type: "api", uri: "/Jobs", method: "get"}
    ]
}, {
    name: "Create jobs", description: "创建任务", need_permission: true,
    resources: [
        {type: "button", code: "create_job"},
        {type: "page", path: "/job"},
        {type: "api", uri: "/Jobs", method: "post"}
    ]
}, {
    name: "Edit jobs", description: "编辑任务", need_permission: true,
    resources: [
        {type: "button", code: "edit_job"},
        {type: "button", code: "save_job"},
        {type: "page", path: "/job/:id"},
        {type: "api", uri: "/Jobs", method: "patch"}
    ]
}, {
    name: "Delete jobs", description: "删除任务", need_permission: true,
    resources: [
        {type: "button", code: "delete_job"},
        {type: "api", uri: "/Jobs", method: "delete"}
    ]
}, {
    name: "Manage jobs", description: "启动、停止、重置、复制任务", need_permission: true,
    resources: [
        {type: "page", path: "/jsFuncs"},
        {type: "button", code: "start_job"},
        {type: "button", code: "stop_job"},
        {type: "button", code: "reset_job"},
        {type: "button", code: "duplicate_job"},
        {type: "api", uri: "/Jobs", method: "patch"}
    ]
}, {
    name: "View connections", description: "查看连接列表", need_permission: true,
    resources: [
        {type: "page", path: "/connections"},
        {type: "api", uri: "/Connections", method: "get"}
    ]
}, {
    name: "Create connections", description: "创建连接", need_permission: true,
    resources: [
        {type: "page", path: "/connection"},
        {type: "api", uri: "/Connections", method: "post"},
        {type: "button", code: "create_connection"}
    ]
}, {
    name: "Edit connections", description: "编辑连接", need_permission: true,
    resources: [
        {type: "page", path: "/connection/:id"},
        {type: "api", uri: "/Connections", method: "patch"},
        {type: "button", code: "edit_connection"},
        {type: "button", code: "test_save_connection"}
    ]
}, {
    name: "Delete connections", description: "删除连接", need_permission: true,
    resources: [
        {type: "api", uri: "/Connections", method: "delete"},
        {type: "button", code: "delete_connection"}
    ]
}, {
    name: "View agents", description: "查看进程列表", need_permission: true,
    resources: [
        {type: "page", path: "/agents"},
        {type: "api", uri: "/Workers", method: "get"}
    ]
}, {
    name: "View user manager", description: "查看用户列表", need_permission: true,
    resources: [
        {type: "page", path: "/users"},
        {type: "api", uri: "/users", method: "get"}
    ]
}, {
    name: "Create user", description: "创建用户", need_permission: true,
    resources: [
        {type: "page", path: "/user"},
        {type: "api", uri: "/users", method: "post"},
        {type: "button", code: "create_user"}
    ]
}, {
    name: "Edit user", description: "管理用户", need_permission: true,
    resources: [
        {type: "page", path: "/users"},
        {type: "page", path: "/user/:id"},
        {type: "page", path: "/users/:id"},
        {type: "api", uri: "/users", method: "patch"},
        {type: "button", code: "edit_user"},
        {type: "button", code: "save_user"}
    ]
}, {
    name: "Delete user", description: "删除用户", need_permission: true,
    resources: [
        {type: "api", uri: "/users", method: "delete"},
        {type: "button", code: "delete_user"}
    ]
}, {
    name: "View roles", description: "查看角色列表", need_permission: true,
    resources: [
        {type: "page", name: "roles"},
        {type: "api", uri: "/roles", method: "get"}
    ]
}, {
    name: "Manage roles", description: "管理角色（创建、编辑、删除）", need_permission: true,
    resources: [
        {type: "button", code: "create_role"},
        {type: "button", code: "edit_role"},
        {type: "button", code: "save_role"},
        {type: "button", code: "delete_role"},
        {type: "page", name: "roles"},
        {type: "page", path: "/role/:id"},
        {type: "page", path: "/role"},
        {type: "api", uri: "/roles", method: "post"},
        {type: "api", uri: "/roles", method: "patch"},
        {type: "api", uri: "/roles", method: "delete"},
        {type: "api", uri: "/Permissions", method: "get"},
        {type: "api", uri: "/Permissions", method: "post"},
        {type: "api", uri: "/Permissions", method: "patch"},
        {type: "api", uri: "/Permissions", method: "delete"},
        {type: "api", uri: "/RoleMappings", method: "get"},
        {type: "api", uri: "/RoleMappings", method: "post"},
        {type: "api", uri: "/RoleMappings", method: "patch"},
        {type: "api", uri: "/RoleMappings", method: "delete"}
    ]
}, {
    name: "View client apps", description: "查看API 客户端", need_permission: true,
    resources: [
        {type: "page", name: "applications"},
        {type: "api", uri: "/Applications", method: "get"}
    ]
}, {
    name: "Manage client apps", description: "管理 API 客户端", need_permission: true,
    resources: [
        {type: "page", name: "application"},
        {type: "button", code: "create_apps"},
        {type: "button", code: "edit_apps"},
        {type: "button", code: "save_apps"},
        {type: "button", code: "delete_apps"},
        {type: "api", uri: "/Applications", method: "post"},
        {type: "api", uri: "/Applications", method: "patch"},
        {type: "api", uri: "/Applications", method: "delete"}
    ]
}, {
    name: "Manage API Server", description: "管理 API 服务器", need_permission: true,
    resources: [
        {type: "page", name: "apiServers"},
        {type: "api", uri: "/ApiServers", method: "get"},
        {type: "page", name: "apiServer"},
        {type: "api", uri: "/ApiServers", method: "post"},
        {type: "api", uri: "/ApiServers", method: "patch"},
        {type: "api", uri: "/ApiServers", method: "delete"}
    ]
}, {
    name: "Settings", description: "设置管理", need_permission: true,
    resources: [
        {type: "page", name: "settings"},
        {type: "button", code: "save_settings"},
        {type: "api", uri: "/Settings", method: "get"},
        {type: "api", uri: "/Settings", method: "post"},
        {type: "api", uri: "/Settings", method: "patch"}
    ]
}, {
    name: "ScheduleTasks", description: "调度任务管理", need_permission: true,
    resources: [
        {type: "page", name: "tasks"},
        {type: "api", uri: "/ScheduleTasks", method: "get"},
        {type: "api", uri: "/ScheduleTasks", method: "post"},
        {type: "api", uri: "/ScheduleTasks", method: "patch"},
        {type: "page", name: "taskHistories"},
        {type: "api", uri: "/TaskHistories", method: "get"}
    ]
}, {
    name: "Projects", description: "项目分组", need_permission: true,
    resources: [
        {type: "page", name: "projects"},
        {type: "api", uri: "/Projects", method: "get"},
        {type: "api", uri: "/Projects", method: "post"},
        {type: "api", uri: "/Projects", method: "patch"},
        {type: "page", path: "/project/:id"},
        {type: "page", path: "/project"}
        // {type: "page", name: "taskHistories"},
        // {type: "api", uri: "/TaskHistories", method: "get"}
    ]
}];