const getProcesses = require("getprocesses");
//const conf = require("./conf")();
const path = require('path');
const basic = require('./basic');
module.exports =async function(conf,updateStatus){
    //(async () => {
    let list = await getProcesses.getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
    let checkBackStr = '';
    let newCheckBackStr = '';

    checkBackStr = path.join(conf.TAPDATA_HOME, "tapdata-agent");
    newCheckBackStr = path.join(conf.TAPDATA_HOME, "tapdata-agent");

    checkBackStr = basic.getExecStr(conf,checkBackStr);
    newCheckBackStr = basic.getExecStr(conf,newCheckBackStr);
    for (let i = 0; i < list.length; i++) {
        if ( (list[i].command.indexOf(checkBackStr) >= 0 || list[i].command.indexOf(newCheckBackStr) >= 0 || list[i].arguments.join(' ').indexOf("tapdata-agent agent")>=0)&&
            list[i].arguments.join(' ').indexOf("components")<0 &&
            list[i].arguments.join(' ').indexOf("./subReport")<0 &&
            list[i].arguments.join(' ').indexOf("./subReportStats")<0 &&
            list[i].arguments.join(' ').indexOf("./subProcess")<0) {
            return;
        }
    }
    //let env = process.env;
    let stdio = ['ignore','ignore','ignore'];
    let subProcess = require('child_process').spawn(checkBackStr, ['agent','--workDir',conf.WORK_DIR, updateStatus || ''], {//,'--workDir',conf.WORK_DIR
            'cwd':conf.TAPDATA_HOME,
            'stdio': stdio,
            //'env': env,
            'detached': true,
            'windowsHide':true
        });
    subProcess.unref();
    let start = (new Date()).getTime();
    while((new Date()).getTime() - start < 2000) {
    }
    //})();
};
