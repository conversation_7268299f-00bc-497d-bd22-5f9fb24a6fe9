const fs = require("fs");
const path = require('path');
const prompts = require('prompts');
const YAML = require('yamljs');
const crypto = require('crypto');
const basic = require("./basic");
const version = require("./version");
const sendBehavior = require('./util/sendBehavior').sendBehavior;

module.exports = async function (arguments) {
    let TAPDATA_HOME = path.dirname(process.execPath);//process.cwd();//process.argv[1]
    if(TAPDATA_HOME === "/")TAPDATA_HOME = "";
    const COMPONENTS_DIR = TAPDATA_HOME + "/components";
    const AGENT_JAR_FILE = getAgentJar(COMPONENTS_DIR);
    let WORK_DIR = '';
    if (fs.existsSync(path.join(TAPDATA_HOME, '.workDir'))) {
        WORK_DIR = fs.readFileSync(path.join(TAPDATA_HOME, '.workDir'), 'utf8').toString();
    }
    if (!WORK_DIR || WORK_DIR === '') {
        WORK_DIR = path.dirname(process.execPath); //os.homedir();
    }
    let yamlObj
    try {
        let fileName = path.join(WORK_DIR,"application.yml");
        fs.accessSync(fileName, fs.constants.F_OK);
        let data = fs.readFileSync(WORK_DIR + '/application.yml');
        yamlObj = YAML.parse(data.toString());
        if (yamlObj && yamlObj.tapdata && yamlObj.tapdata.cloud && yamlObj.tapdata.cloud.token && AGENT_JAR_FILE !== '') {
            return ['start']
        }
    } catch (e) {
    }
    if(yamlObj && yamlObj.tapdata && yamlObj.tapdata.cloud && yamlObj.tapdata.cloud.token && AGENT_JAR_FILE === ''){
        let downloadUrl = '';
        const userToken = yamlObj.tapdata.cloud.token;
        try {
            let key = '5fa25b06ee34581d';
            let iv = '5fa25b06ee34581d';
            let token = decrypt(key, iv, userToken);
            token = JSON.parse(token);
            downloadUrl = await ws(token);
        } catch (e) {
            if(e)
                console.error('Incorrect token')
            return await exit(true);
        }
        if (!downloadUrl || downloadUrl === '') {
            console.info("downloadUrl is Empty");
            return await exit();
        }
        if (!userToken || userToken === '') {
            console.info("userToken is Empty");
            return await exit();
        }
        let tokenObj = basic.decodeToken(userToken);
        tokenObj.isCloud = true;
        tokenObj.cloud_token = userToken;
        sendBehavior(tokenObj,{code:'haveTokenStart',result:'successed',msg:''});
        return ['start','backend','--token',userToken,'--downloadUrl',downloadUrl];
    }
    console.info("Welcome to Tapdata");
    console.info("https://cloud.tapdata.net");
    const questions = [
        {
            type: 'text',
            name: 'args',
            message: 'Please enter your token'
        }
    ];
    console.info('-------------------------');
    console.info('请在下方输入您Agent的Token（点击右键粘贴Token）。如果您还没有Token，请前往 https://cloud.tapdata.net/ 注册Tapdata Cloud账号，创建一个Agent来获取您的Token。');
    const response = await prompts(questions);
    if(response.args === ''){
        return await exit();
    }
    let downloadUrl = '';

    const userToken = response.args;
    try {
        let key = '5fa25b06ee34581d';
        let iv = '5fa25b06ee34581d';
        let token = decrypt(key, iv, userToken);
        token = JSON.parse(token);
        downloadUrl = await ws(token);
    } catch (e) {
        if(e)
            console.error('Incorrect token')
        sendBehavior({cloud_token:userToken,isCloud:true},{code:'incorrectToken',result:'failed',msg:'Incorrect token'});
        return await exit(true);
    }
    if (!userToken || userToken === '') {
        console.info("userToken is Empty");
        return await exit();
    }
    if (!downloadUrl || downloadUrl === '') {
        console.info("downloadUrl is Empty");
        return await exit();
    }

    try{
        fs.accessSync(path.join(conf.TAPDATA_HOME, 'components','tapdata-agent.jar'), fs.constants.F_OK);
        return ['start','backend','--token',userToken]
    }catch (e) {
    }
    let tokenObj = basic.decodeToken(userToken);
    tokenObj.isCloud = true;
    tokenObj.cloud_token = userToken;
    sendBehavior(tokenObj,{code:'inputToken',result:'successed',msg:''});
    return ['start','backend','--token',userToken,'--downloadUrl',downloadUrl];


    const args = response.args.split(" ");
    let haveDownloadUrl = false;
    let haveToken = false;
    let resArgs = [];
    for(let i=0;i<args.length;i++){
        if(args[i] === '--downloadUrl' && args.length > i){
            haveDownloadUrl = true
        }else if(args[i] === '--token' && args.length > i){
            haveToken = true
            args[i+1] = args[i+1].replace(/'/g,'')
        }
        if(args[i] !== 'tapdata'){
            resArgs.push(args[i])
        }
    }
    if(!haveDownloadUrl || !haveToken){
        return await exit();
    }
    return resArgs
};

async function exit(onlyExit){
    if(!onlyExit) {
        console.info("Illegal command");
        console.info("Visitors to our website check the command.");
        console.info("https://cloud.tapdata.net/console/#/instance");
    }
    const questions = [
        {
            type: 'text',
            name: 'args',
            message: 'Exit Tapdata.'
        }
    ]
    const response = await prompts(questions);
    process.exit(1);
}

function getAgentJar(COMPONENTS_DIR) {
    try {
        let arr = fs.readdirSync(COMPONENTS_DIR);
        for (let x in arr) {
            if (/tapdata-agent+[\s\S]*?\.jar$/.test(arr[x])) {
                return arr[x];
            }
        }
    }catch(e){
        return '';
    }
}

function decrypt(key, iv, crypted) {
    crypted = new Buffer.from(crypted, 'base64').toString('binary');
    let decipher = crypto.createDecipheriv('aes-128-cbc', key, iv);
    let decoded = decipher.update(crypted, 'binary', 'utf8');
    decoded += decipher.final('utf8');
    let rs = new Buffer.from(decoded,'base64').toString()
    return rs;
};
function signString(str) {
    return basic.md5('tapdata' + str + '20200202');
}
function ws(conf){
    return new Promise(function (resolve, reject) {
        const ws = basic.connWs(conf);
        const timeout = setTimeout(()=>{
            ws.close();
            reject()
        },10000)
        ws.on('open', function open() {
            const now = (new Date()).getTime();
            const agentVersion = JSON.parse(version(false,conf));
            let obj = {
                'type': 'checkTapdataAgentVersion',
                'timestamp': now,
                'data': {version:agentVersion.version}
            };
            obj.sign = signString(JSON.stringify(obj));
            ws.send(JSON.stringify(obj));
        });
        ws.on('message', msg => {
            try {
                let data = JSON.parse(msg);
                if(data.type === 'checkTapdataAgentVersion'){
                    if(data.data && ( data.data.enable === false || data.data.enable === 'false')){
                        if(conf._windows) {
                            console.info('您的tapdata.exe版本过低，请访问 https://cloud.tapdata.net/ 下载最新版本。');
                        }else{
                            console.info('您的tapdata版本过低，请访问 https://cloud.tapdata.net/ 下载最新版本。');
                        }
                        reject();
                        return;
                    }
                    const now = (new Date()).getTime();
                    let obj = {
                        'type': 'downloadUrl',
                        'timestamp': now,
                        'data': {userId:conf.user_id}
                    };
                    obj.sign = signString(JSON.stringify(obj));
                    ws.send(JSON.stringify(obj));
                }
                if(data.type === 'downloadUrl' && data.data && data.data !== '') {
                    clearTimeout(timeout);
                    ws.close();
                    resolve(data.data.downloadUrl);
                }
            } catch (e) {
                console.info(e);
                reject(e)
            }
        });
        ws.on('error', (e) => {
            console.info('Connection Error');
            console.info(e);
        });
        ws.on('close', () => {
        });
    })
}