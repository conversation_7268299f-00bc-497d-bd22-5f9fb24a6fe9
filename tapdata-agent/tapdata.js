/**
 * Tapdata启动程序
 * etc/init/ 中的脚本是数据库初始化脚本，这些脚本需要放在程序的etc/init/文件夹中。
 * 程序会根据文件名正序的顺序逐个执行。
 * 包括程序初始化、数据库初始化、系统各个模块启动、停止、重启、状态查看功能。
 * 使用方法
 * tapdata [option] [subsystem][-debug]
 * Option:
 * start            Start Tapdata
 * stop             Stop Tapdata
 * restart          Restart Tapdata
 * status           Check the running status
 * init             Initialize Tapdata Configuration
 * resetpassword    Reset MongoDB password or certificate key file password
 * help             Show this usage information
 *
 * Subsystem:
 * frontend         User interaction system
 * backend          Data exchange system
 * apiserver        Interface service system
 * os-monitor       Operating system monitoring
 *
 * -debug:
 * show debug information
 *
 * 系统初始化时，判断用户目录下有无.tapdata目录。如不存在则创建目录。
 * 判断.tapdata目录下是否存在配置文件。如果没有配置文件，则从安装包将配置文件拷贝到.tapdata目录下。
 * 首次使用系统自动进入初始化模式，需要用户确认系统配置信息和MongoDB连接信息。
 * 系统首次启动时会将components目录下的压缩文件进行解压缩，会出现一段等待时间。
 * 系统启动时判断模块是否已经启动，如果已经启动则不再次启动系统。
 * 系统停止后台时，需要用户二次确认后再停止系统。
 * os-monitor不会参与默认启动与停止。需单独启动与停止。
 * 如需更新模块，将模块压缩包放入components文件夹，系统会在启动时解压压缩包并替换源程序。
 * 前台压缩包文件名要求：用“frontend-”开头，以“.tar.gz”结尾。
 * API压缩包文件名要求：用“apiserver”开头，以“.tar.gz”结尾。
 * os-monitor压缩包文件名要求：用“os-monitor”开头，以“.tar.gz”结尾。
 * */
//获取系统配置参数
process.systemStartTime = new Date().getTime();
(async () => {
    printTitle();
    let arguments = process.argv.splice(2);
    process.tapdataArguments = arguments;
    let needCheckProcess = false;
    let haveToken = false;
    if(arguments[0] === 'run'){
        const subprocessRun = require("./process/subprocessRun");
        await subprocessRun();
        return;
    }
    if(arguments.length > 2 && arguments[0] === 'upgrade'){
        const updateBackend = require("./util/upgrade");
        await updateBackend(arguments);
        process.exit(1);
        return;
    }
    if(arguments.length > 2 && arguments[0] === 'update' && arguments[1] === 'backend'){
        const updateBackend = require("./util/updateBackend").updateBackend;
        await updateBackend(arguments);
        return;
    }
    if(arguments.length === 0 || arguments.length === 2 && arguments[0] === '--backendUrl'){
        arguments = await require("./checkRunable")(arguments);
        if(!arguments)return;
    }
    for(let i=0;i<arguments.length;i++){
        if(arguments[i] === '--token'){
            haveToken = true;
        }
        if(arguments[i] === '--downloadUrl'){
            needCheckProcess = true;
        }
    }
    if(needCheckProcess) {
        try {
            await require("./init/init").checkConfAndProcess(arguments)
        }catch(e){
            return;
        }
    }else if(haveToken){
        try {
            if(!await require("./init/init").deleteConf(arguments)){
                return;
            }
        }catch(e){
            return;
        }
    }
    let conf = require("./conf")(undefined, arguments);
    process.env.tapdata_work_dir = conf.WORK_DIR;
    if(arguments[0] === '--version' || arguments[0] === '--v'){
        require("./version")(true,conf);
        return;
    }
    if (await require("./process/agent").stopAPI(conf, arguments)) return;
//如果没有初始化，则执行初始化代码
    if (conf.tapdata_port === "" || conf.backend_url === "" || needCheckProcess) {
        const init = require("./init/init").init;
        init(arguments, conf);
        return;
    }
    if(arguments[0] === 'init'){
        const init = require("./init/init").init;
        init(arguments,conf);
        return;
    }
//获取用户输入参数
    if (arguments[0] === 'update') {
        require('./util/update').update(conf,arguments).then()
        return
    }
    if (arguments[0] === 'agent') {
        if (arguments.length > 1 && '--workDir' !== arguments[1]) {
            require('./basic').printHelp();
            return;
        }
        require('./connection')(arguments);
        if(conf.appType !== 'DFS') {
            require('./os_monitor/index');
        }
        return;
    }
//检查上帝进程
    const checkGod = require('./checkGod');
    let updateStatus = '';
    for(let i=0;i<arguments.length;i++) {
        if (['done', 'fail'].includes(arguments[i])) {
            updateStatus = arguments[i]
        }
    }
    await checkGod(conf,updateStatus);
//根据用户输入参数，执行对应程序。
    const manage = require('./process/manage');
    manage(arguments, conf);
})();

function printTitle(){
    console.log('\x1B[32m%s\x1B[0m', ' _______       _____  _____       _______');
    console.log('\x1B[32m%s\x1B[0m', '|__   __|/\\   |  __ \\|  __ \\   /\\|__   __|/\\    ');
    console.log('\x1B[32m%s\x1B[0m', '   | |  /  \\  | |__) | |  | | /  \\  | |  /  \\   ');
    console.log('\x1B[32m%s\x1B[0m', '   | | / /\\ \\ |  ___/| |  | |/ /\\ \\ | | / /\\ \\  ');
    console.log('\x1B[32m%s\x1B[0m', '   | |/ ____ \\| |    | |__| / ____ \\| |/ ____ \\ ');
    console.log('\x1B[32m%s\x1B[0m', '   |_/_/    \\_\\_|    |_____/_/    \\_\\_/_/    \\_\\ ');
    console.log('');
}

let exitHdl = function (signal) {
};
process.on('beforeExit', exitHdl);
process.on('uncaughtException', function (err) {
    console.error(err);
    console.error(err.stack)
});
function handle(signal) {
    console.error(`Received ${signal}`);
    process.exit()
}
process.on('SIGINT', handle);
process.on('SIGHUP', handle);
process.on('SIGBREAK', handle);
process.on('SIGTERM', handle);
