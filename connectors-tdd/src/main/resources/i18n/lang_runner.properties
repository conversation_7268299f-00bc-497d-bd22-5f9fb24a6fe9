CONNECTOR_CAPABILITIES_START= 的扫描结果显示，数据源实现了以下能力方法:%s
TOTAL_CAPABILITIES_OF=%s总共具备的能力有%s个。%s
ERROR.CASE=运行/调试失败的结果：\n
WARN.CASE=运行/调试警告的结果：\n
SUCCEED.CASE=运行/调试成功的结果：\n
logo.tip=\n\nTip: If you want to execute js function which name is writeRecord, maybe the command you can enter is:\n\t10 or writeRecord or insertRecord or updateRecord or deleteRecord .
logo.tip.after=\nPlease select the function name to execute the above command(more commands use space to split ):
log.not.support=The following js method name or class path cannot find a matching execution target:
log.not.support.msg=Please specify the name correctly. The supported js method name/class path name as described in the above table.

TEST_SUCCEED_END=%s(✔)非常棒! %s 的所有方法运行/调试全部通过!
TEST_ERROR_END=%s(╳) Oops, PDK %s 未通过所有方法的运行/调试，请解决以上问题。
SUCCEED_WITH_WARN=但是部分方法的运行/调试结果显示为警告，请检查。
SUMMARY_END= ▣ 针对 %s 数据源进行方法进行运行/调试，已覆盖用例总计 %s 条。\n \t◉ 累计执行用例 %s 条： 成功的方法 [%s] 条 | 告警的方法 [%s] 条 | 失败的方法 [%s] 条。\n \t◈ 累计跳过方法 %s 条。\n

ONCE_HISTORY=%s结果：%s， 通过方法：%s， 警告方法：%s， 错误方法：%s， 跳过方法：%s

#NOT_SUPPORT_FUNCTION=检测到数据源存在未实现的方法，终止了当前用例的运行/调试流程，未实现的方法为：%s
TEST_RESULT_SUCCEED=运行/调试成功
TEST_RESULT_ERROR=运行/调试失败
TEST_RESULT_WARN=运行/调试告警
base.jumpCase.list=跳过的运行/调试方法列表：
SUCCEED_COUNT_LABEL=通过方法：
WARN_COUNT_LABEL=告警方法：
ERROR_COUNT_LABEL=错误方法：
HAS_WARN_COUNT=但是其中有%s个方法发生了警告。
TEST_OF_SUCCEED=成功的结果如下
TEST_OF_WARN=告警的结果如下
TEST_OF_ERROR=失败的结果如下

formatValue=%s
jsFunctionInNeed=JavaScript Function %s may not implemented. Debugging/running can only be carried out after that method is implemented in the connector.js method.

batchCountRun=BatchCount全量记录数（依赖WriteRecordFunction）
batchCountRun.run=batch_count全量记录数运行/调试
batchCountRun.noTable=Cannot get tableName in properties file.
batchCountRun.succeed=Table count of table %s is %s.
batchCountRun.noFunction=Cannot support batch_count function in javaScript's file connector.js.


batchReadRun=BatchCountFunction全量读取（依赖BatchReadFunction）
batchReadRun.run=batch_read全量读取运行/调试

commandRun=CommandCallbackFunction获取数据源相关数据（依赖CommandCallbackFunction）
commandRun.run=command_callback获取数据源相关数据运行/调试
commandRun.notCommandInfo=Can not get commandInfo from command_callback in pdk run properties json file.

connectionTestRun=ConnectionTest连接测试（依赖ConnectionTestFunction）
connectionTestRun.run=connection_test连接测试运行/调试

discoverSchemaRun=DiscoverSchema发现表（依赖DiscoverSchema）
discoverSchemaRun.run=discover_schema发现表运行/调试

streamReadRun=StreamReadFunction增量读（依赖StreamReadFunction）
streamReadRun.run=stream_read运行/调试

tableCountRun=TableCountFunction获取表数目（依赖TableCountFunction）
tableCountRun.run=table_count/discover_schema获取表数目运行/调试
tableCountRun.succeed=Table count is %s.

timestampToStreamOffsetRun=TimestampToStreamOffsetFunction获取增量时间断点（依赖TimestampToStreamOffsetFunction）
timestampToStreamOffsetRun.run=timestamp_to_stream_offset获取增量时间断点运行/调试

webHookEventRun=RawDataCallbackFilterFunctionV2处理WebHook事件（依赖RawDataCallbackFilterFunctionV2）
webHookEventRun.run=web_hook_event处理WebHook事件运行/调试

writeRecordRun=WriteRecordFunction数据写入（依赖WriteRecordFunction）
writeRecordRun.run=write_record数据写入运行/调试
