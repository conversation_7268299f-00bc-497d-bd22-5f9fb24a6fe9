
fieldModification.all.wait=正在执行：用例1，修改全部数据正常，请稍后...
fieldModification.all.throw=执行过程发现异常，请检查数据源实现：%s。
fieldModification.all.insert.error=建表后插入%s条多字段的数据，但是插入失败。插入返回的结果中插入%s条，不符合实际预期，实际应该插入%s条，请检查数据源写入数据的能力。
fieldModification.all.insert.succeed=建表后插入%s条多字段的数据，但是插入成功。插入返回的结果中插入%s条，符合实际预期.
fieldModification.not.filter=此数据源既没有实现QueryByFilterFunction也没有实现QueryByAdvanceFilterFunction，因此建表后插入%s条多字段的数据后不再进行数据比对。

fieldModification.all.update.error=建表并插入%s条多字段的数据，同时执行修改全部字段的操作，但是修改过程发生错误。预期修改%s条，实际返回的结果中：插入%s条，修改%s条，删除%s条。
fieldModification.all.update.succeed=建表并插入%s条多字段的数据，同时执行修改全部字段的操作，修改成功，下一步是：查询这些数据并进行比值验证。
fieldModification.all.query.throw=建表并插入%s条多字段的数据，同时执行修改全部字段的操作，在执行QueryByFilterFunction/QueryByAdvanceFilterFunction查询数据时，抛出了一个异常，error: %s.
fieldModification.all.query.fail=建表并插入%s条多字段的数据，同时执行修改全部字段的操作，经过QueryByFilterFunction/QueryByAdvanceFilterFunction查询显示返回%s条条数据，不符合预期，预期应该返回。
fieldModification.all.query.error=建表并插入%s条多字段的数据，同时执行修改全部字段的操作，对QueryByFilterFunction/QueryByAdvanceFilterFunction查询显示返回%s条数据进行比值验证，数据前后对比不一致，对比结果为：\n\t\t\t（Left为修改前的数据，Right为修改后查询出来的数据）\n%s
fieldModification.all.query.succeed=建表并插入%s条多字段的数据，同时执行修改全部字段的操作，对QueryByFilterFunction/QueryByAdvanceFilterFunction查询显示返回%s条数据进行比值验证，数据前后对比不致，符合预期。

fieldModification.some.update.error=建表并插入%s条多字段的数据，同时执行修改其中%s个字段的并将完整的记录用于构建修改事件操作，但是修改过程发生错误。预期修改%s条，实际返回的结果中：插入%s条，修改%s条，删除%s条。
fieldModification.some.update.succeed=建表并插入%s条多字段的数据，同时执行修改其中%s个字段的并将完整的记录用于构建修改事件操作，修改成功，下一步是：查询这些数据并进行比值验证。
fieldModification.some.query.throw=建表并插入%s条多字段的数据，同时执行修改其中%s个字段的并将完整的记录用于构建修改事件操作，在执行QueryByFilterFunction/QueryByAdvanceFilterFunction查询数据时，抛出了一个异常，error: %s.
fieldModification.some.query.fail=建表并插入%s条多字段的数据，同时执行修改其中%s个字段的并将完整的记录用于构建修改事件操作，经过QueryByFilterFunction/QueryByAdvanceFilterFunction查询显示返回%s条数据，不符合预期，预期应该返回。
fieldModification.some.query.error=建表并插入%s条多字段的数据，同时执行修改其中%s个字段的并将完整的记录用于构建修改事件操作，对QueryByFilterFunction/QueryByAdvanceFilterFunction查询显示返回%s条数据进行比值验证，数据前后对比不一致，对比结果为：\n\t\t\t（Left为修改前的数据，Right为修改后查询出来的数据）\n%s
fieldModification.some.query.succeed=建表并插入%s条多字段的数据，同时执行修改其中%s个字段的并将完整的记录用于构建修改事件操作，对QueryByFilterFunction/QueryByAdvanceFilterFunction查询显示返回%s条数据进行比值验证，数据前后对比不致，符合预期。

fieldModification.some2.update.error=建表并插入%s条多字段的数据，同时执行修改其中%s个字段的，将修改的字段用于构建修改事件操作，但是修改过程发生错误。预期修改%s条，实际返回的结果中：插入%s条，修改%s条，删除%s条。
fieldModification.some2.update.succeed=建表并插入%s条多字段的数据，同时执行修改其中%s个字段的，将修改的字段用于构建修改事件操作，修改成功，下一步是：查询这些数据并进行比值验证。
fieldModification.some2.query.throw=建表并插入%s条多字段的数据，同时执行修改其中%s个字段的，将修改的字段用于构建修改事件操作，在执行QueryByFilterFunction/QueryByAdvanceFilterFunction查询数据时，抛出了一个异常，error: %s.
fieldModification.some2.query.fail=建表并插入%s条多字段的数据，同时执行修改其中%s个字段的，将修改的字段用于构建修改事件操作，经过QueryByFilterFunction/QueryByAdvanceFilterFunction查询显示返回%s条条数据，不符合预期，预期应该返回。
fieldModification.some2.query.error=建表并插入%s条多字段的数据，同时执行修改其中%s个字段的，将修改的字段用于构建修改事件操作，对QueryByFilterFunction/QueryByAdvanceFilterFunction查询显示返回%s条数据进行比值验证，数据前后对比不一致，对比结果为：\n\t\t\t（Left为修改前的数据，Right为修改后查询出来的数据）\n%s
fieldModification.some2.query.succeed=建表并插入%s条多字段的数据，同时执行修改其中%s个字段的，将修改的字段用于构建修改事件操作，对QueryByFilterFunction/QueryByAdvanceFilterFunction查询显示返回%s条数据进行比值验证，数据前后对比不致，符合预期。
#----------------------------------------------------------------
fieldModification.part.wait=正在执行：用例2， 修改部分数据正常，请稍后...
fieldModification.part.throw=执行过程发现异常，请检查数据源实现:%s。
#----------------------------------------------------------------
createTableUsingField.all.wait=正在执行：用例1，尝试每个字段是否建表成功，请稍后...
createTableUsingField.notDataTypes=未从JSON Schema中找到DataType的描述，请添加描述后再检查此用例。
createTableUsingField.all.errorFiled=处理DataType时发生异常，生成表模型失败，字段名称：%s，字段类型：%s, 出错信息：%s.
#----------------------------------------------------------------
sequentialTest.wait=执行已开始：WriteRecord的事件需要顺序执行（依赖WriteRecordFunction和QueryByAdvanceFilter/QueryByFilter）...
sequentialTest.modify.wait=正在执行：用例1，相同主键的数据发生连续事件最后能被正常修改，请稍后...
sequentialTest.modify.query.fail=插入%s条数据后，连续修改了%s次，经过（QueryByAdvanceFilter/QueryByFilter）查询，查询结果%s条，查询不符合预期。
sequentialTest.modify.query.succeed=插入%s条数据后，连续修改了%s次，经过（QueryByAdvanceFilter/QueryByFilter）查询，查询到的数据（Right）与数据最后一次修改后（Left）一致,获取到结果并比对通过。
sequentialTest.modify.query.notEquals=插入%s条数据后，连续修改了%s次，经过（QueryByAdvanceFilter/QueryByFilter）查询，获取到结果但比对不通过，查询到的数据（Right）与数据最后一次修改后（Left）不一致，对比结果为：\n%s。
sequentialTest.insertRecord=测试开始插入数据%s条，插入返回结果显示插入操作数%s条，修改操作数%s条，删除操作数%s条，插入成功。
sequentialTest.insertRecord.fail=测试开始插入数据%s条，插入返回结果显示插入操作数%s条，修改操作数%s条，删除操作数%s条，插入失败，数据写入出现问题，请在数据源端检查写入操作。
sequentialTest.insertRecord.throw=测试开始插入数据%s条，但是插入数据过程中产生了异常，异常信息：%s。
sequentialTest.modifyRecord=测试开始插入数据%s条后，第%s次修改，修改执行返回的结果显示，新增数%s，修改数%s，删除数%s,修改符合预期，本次修改成功。
sequentialTest.modifyRecord.fail=测试开始插入数据%s条后，第%s次修改，修改执行返回的结果显示，新增数%s，修改数%s，删除数%s,修改不符合预期，本次修改失败。
sequentialTest.modifyRecord.throw=测试开始插入数据%s条后，第%s次修改，修改执行过程中出现了异常：%s，本次修改操作失败。
#----------------------------------------------------------------
sequentialTest.delete.wait=正在执行：用例2，相同主键的数据发生连续事件最后能被删除掉，请稍后...
sequentialTest.deleteRecord.throw=测试开始插入数据%s条后，连续%s次修改后删除数据，删除执行过程中出现了异常：%s，本次操作失败。
sequentialTest.deleteRecord.succeed=测试开始插入数据%s条后，连续%s次修改后删除数据，删除执行返回的结果显示，新增数%s，修改数%s，删除数%s，修改符合预期，本次操作成功。
sequentialTest.deleteRecord.fail=测试开始插入数据%s条后，连续%s次修改后删除数据，删除执行返回的结果显示，新增数%s，修改数%s，删除数%s，修改不符合预期，本次操作失败。
sequentialTest.delete.fail=测试开始插入数据%s条后，连续%s次修改后删除数据，经过（QueryByAdvanceFilter/QueryByFilter）查询，查询结果%s条，查询不符合预期。
sequentialTest.delete.succeed=测试开始插入数据%s条后，连续%s次修改后删除数据，经过（QueryByAdvanceFilter/QueryByFilter）查询，查询无结果，查询符合预期。
#-----------------------------------------------------------------
sequentialTest.more.wait=正在执行：用例3，相同主键的数据发生连续删除新增事件最后数据是正确的，请稍后...
sequentialTest.more.succeed=相同主键的数据，发生新增->修改->删除->新增->删除->新增后，经过（QueryByAdvanceFilter/QueryByFilter）查询显示，查询数据%s条，且符合预期：查询到的数据（Right）与数据最后一次修改后（Left）一致。
sequentialTest.more.fail=相同主键的数据，发生新增->修改->删除->新增->删除->新增后，经过（QueryByAdvanceFilter/QueryByFilter）查询显示，查询数据%s条，实际应该存在%s条，操作不符合预期。
sequentialTest.more.notEquals=相同主键的数据，发生新增->修改->删除->新增->删除->新增后，经过（QueryByAdvanceFilter/QueryByFilter）查询显示，查询数据%s条，但不符合预期，原因：查询到的数据（Right）与数据最后一次修改后（Left）不一致，对比结果为：\n%s\n\t\t\t\t（Left为修改前的数据，Right为修改后查询出来的数据）
#-----------------------------------------------------------------
batchWriteTest.wait=执行已开始：WriteRecord的事件多批次执行（依赖WriteRecordFunction和QueryByAdvanceFilter/QueryByFilter）...
batchWrite.batch.wait=正在执行：用例1，10条数据，按10批插入，请稍后...
batchWrite.batch.query.fail=不同主键的数据，按%s批次插入，每个批次%s条数据，最后经过（QueryByAdvanceFilter/QueryByFilter）查询显示查询数据%s条，数目不符合预期。
batchWrite.batch.query.notEquals=不同主键的数据，按%s批次插入，每个批次%s条数据，最后经过（QueryByAdvanceFilter/QueryByFilter）查询显示查询数据%s条，数目符合预期。此时对第%s批数据进行比对，但实际比对数据不符合预期，原因：查询到的数据（Right）与插入前的数据（Left）不一致，对比结果为：\n%s。
batchWrite.batch.query.succeed=不同主键的数据，按%s批次插入，每个批次%s条数据，最后经过（QueryByAdvanceFilter/QueryByFilter）查询显示查询数据%s条，数目符合预期。此时对第%s批数据进行比对，数目符合预期且数据比对符合预期。
batchWrite.insertRecord.throw=不同主键的数据，按%s批次插入，每个批次%s条数据，在插入第%s批数据时发生了异常，异常信息：%s。
batchWrite.insertRecord.fail=不同主键的数据，按%s批次插入，每个批次%s条数据，插入第%s批数据时返回结果显示，插入数%s，修改数%s，删除数%s，与预期结果不符，插入失败。
batchWrite.insertRecord=不同主键的数据，按%s批次插入，每个批次%s条数据，插入第%s批数据时返回结果显示，插入数%s，修改数%s，删除数%s，与预期结果相符，插入成功。
#-----------------------------------------------------------------
multiWrite.wait=执行已开始：WriteRecord的多线程支持（依赖WriteRecordFunction和QueryByAdvanceFilter/QueryByFilter）...
multiWrite.modify.wait=正在执行：用例1， 采用4个线程写入修改测试，请稍后...
multiWrite.delete.wait=正在执行：用例2， 采用4个线程删除测试，请稍后...
multiWrite.insertRecord.throw=采用4个线程写入，每个线程对各自不同主键的内容进行操作，当前线程：%s，执行数据插入时发生了异常：%s。
multiWrite.insertRecord.fail=采用4个线程写入，每个线程对各自不同主键的内容进行操作，当前线程：%s，执行插入时插入%s条数据结果不符合预期，插入操作的执行结果显示插入数%s，修改数%s，删除数%s，与预期结果不符。
multiWrite.insertRecord=采用4个线程写入，每个线程对各自不同主键的内容进行操作，当前线程：%s，执行插入时插入%s条数据结果符合预期，插入操作的执行结果显示插入数%s，修改数%s，删除数%s，与预期结果相符。
multiWrite.modifyRecord.throw=采用4个线程写入，每个线程对各自不同主键的内容进行操作，当前线程：%s，执行插入时插入%s条数据后，在执行了第%s次修改时发生了异常：%s。
multiWrite.modifyRecord.fail=采用4个线程写入，每个线程对各自不同主键的内容进行操作，当前线程：%s，执行插入时插入%s条数据后，在执行了第%s次修改时的结果不符合预期，本次修改操作的执行结果显示插入数%s，修改数%s，删除数%s，与预期结果不符。
multiWrite.modifyRecord=采用4个线程写入，每个线程对各自不同主键的内容进行操作，当前线程：%s，执行插入时插入%s条数据后，在执行了第%s次修改时的结果符合预期，本次修改操作的执行结果显示插入数%s，修改数%s，删除数%s，与预期结果相符。
multiWrite.modify.query.fail=采用4个线程写入，每个线程对各自不同主键的内容进行操作，当前线程：%s，执行插入%s条数据并修改%s次后，经过（QueryByAdvanceFilter/QueryByFilter）查询显示记录数为%s，不符合预期，预期结果数应该是%s。
multiWrite.modify.query.notEquals=采用4个线程写入，每个线程对各自不同主键的内容进行操作，当前线程：%s，执行插入%s条数据并修改%s次后，经过（QueryByAdvanceFilter/QueryByFilter）查询显示记录数为%s，与预期结果数相同，但是数据比对不通过，查询到的数据（Right）与数据最后一次修改后（Left）不一致，对比结果为：\n%s。
multiWrite.modify.query.succeed=采用4个线程写入，每个线程对各自不同主键的内容进行操作，当前线程：%s，执行插入%s条数据并修改%s次后，经过（QueryByAdvanceFilter/QueryByFilter）查询显示记录数为%s，与预期结果数相同，且数据比对通过，查询到的数据与数据最后一次修改后一致。
multiWrite.deleteRecord.throw=采用4个线程写入，每个线程对各自不同主键的内容进行操作，当前线程：%s，执行插入%s条数据并修改%s次后，在执行删除操作时发生了异常：%s。
multiWrite.deleteRecord.fail=采用4个线程写入，每个线程对各自不同主键的内容进行操作，当前线程：%s，执行插入%s条数据并修改%s次后并删除数据，删除操作的执行结果显示插入数%s，修改数%s，删除数%s，与预期结果不符。
multiWrite.deleteRecord.succeed=采用4个线程写入，每个线程对各自不同主键的内容进行操作，当前线程：%s，执行插入%s条数据并修改%s次后并删除数据，删除操作的执行结果显示插入数%s，修改数%s，删除数%s，与预期结果相符。
multiWrite.delete.fail=采用4个线程写入，每个线程对各自不同主键的内容进行操作，当前线程：%s，执行插入%s条数据并修改%s次后并删除数据，经过（QueryByAdvanceFilter/QueryByFilter）查询显示记录数为%s，不符合预期，预期结果数据应该已删除并查询不到。
multiWrite.delete.succeed=采用4个线程写入，每个线程对各自不同主键的内容进行操作，当前线程：%s，执行插入%s条数据并修改%s次后并删除数据，经过（QueryByAdvanceFilter/QueryByFilter）查询显示记录数为0，符合预期。
#---------------------------------------------------------------------------
writeTimeTest.wait=执行已开始：时间类型数据写入（依赖WriteRecordFunction和QueryByAdvanceFilter/QueryByFilter）...
writeTime.queryFilter.wait=正在执行：用例1，写入时间并使用（QueryByFilterFunction）查询时间验证是否正确...
writeTime.batchRead.wait=正在执行：用例2，写入时间并使用（BatchReadFunction）查出时间验证是否正确...
writeTime.insertRecord.throw=以DateTime（GMT-0的时间）数据结构写入时间类型数据，写入数据时发生了异常：%s。
writeTime.insertRecord.fail=以DateTime（GMT-0的时间）数据结构写入时间类型数据，执行插入时插入%s条数据结果不符合预期，插入操作的执行结果显示插入数%s，修改数%s，删除数%s，与预期结果不符。
writeTime.insertRecord=以DateTime（GMT-0的时间）数据结构写入时间类型数据，执行插入时插入%s条数据结果符合预期，插入操作的执行结果显示插入数%s，修改数%s，删除数%s，与预期结果相符。
writeTime.queryFilter.fail=以DateTime（GMT-0的时间）数据结构写入时间类型数据，执行插入时插入%s条数据后，通过（QueryByAdvanceFilter/QueryByFilter）查询显示记录数为%s，实际记录数应该为%s，不符合预期。
writeTime.queryFilter.notEquals=以DateTime（GMT-0的时间）数据结构写入时间类型数据，执行插入时插入%s条数据后，通过（QueryByAdvanceFilter/QueryByFilter）查询显示记录数为%s，记录数与实际插入数相同但数据比对不通过，查询到的数据（Right）与插入前的数据（Left）不一致，对比结果为：\n%s。
writeTime.queryFilter.succeed=以DateTime（GMT-0的时间）数据结构写入时间类型数据，执行插入时插入%s条数据后，通过（QueryByAdvanceFilter/QueryByFilter）查询显示记录数为%s，记录数与实际插入数相同且数据比对通过，查询到的数据与插入前的数据一致。
writeTime.batchRead.throw=以DateTime（GMT-0的时间）数据结构写入时间类型数据，执行插入时插入%s条数据后，通过BatchReadFunction读取记录时发生异常，异常信息：%s。
writeTime.batchRead.fail=以DateTime（GMT-0的时间）数据结构写入时间类型数据，执行插入时插入%s条数据后，通过BatchReadFunction读取记录，读取后返回的结果不符合预期，预期返回结果应为%条，实际返回结果%s条。
writeTime.batchRead.notEvent=以DateTime（GMT-0的时间）数据结构写入时间类型数据，执行插入时插入%s条数据后，通过BatchReadFunction读取记录，读取后返回的结果中的TapEvent为空，不符合预期。
writeTime.batchRead.table.null=以DateTime（GMT-0的时间）数据结构写入时间类型数据，执行插入时插入%s条数据后，通过BatchReadFunction读取记录，读取后返回的结果中的TapTable为空。
writeTime.batchRead.time.null=以DateTime（GMT-0的时间）数据结构写入时间类型数据，执行插入时插入%s条数据后，通过BatchReadFunction读取记录，读取后返回的结果中事件的的发生时间为空。
writeTime.batchRead.after.null=以DateTime（GMT-0的时间）数据结构写入时间类型数据，执行插入时插入%s条数据后，通过BatchReadFunction读取记录，读取后返回的结果中数据内容为空，时间插入的数据不为空，查询或写入不符合预期。
writeTime.batchRead.notEquals=以DateTime（GMT-0的时间）数据结构写入时间类型数据，执行插入时插入%s条数据后，通过BatchReadFunction读取记录，记录数与实际插入数相同但数据比对不通过，查询到的数据（Right）与插入前的数据（Left）不一致，对比结果为：\n%s。
writeTime.batchRead.succeed=以DateTime（GMT-0的时间）数据结构写入时间类型数据，执行插入时插入%s条数据后，通过BatchReadFunction读取记录，记录数与实际插入数相同但数据比对不通过，查询到的数据与插入前的数据一致，对比结果符合预期。
#-----------------------------------------------------------------------------
tapValueTest.wait=执行已开始：各种TapValue写入到目标，数据能被转换成为原始值（依赖WriteRecordFunction）...
tapValueTest.value.wait=正在执行：用例1，FromTapValue的值转换正常...
#-----------------------------------------------------------------------------
checkThreadTest.wait=执行已开始：连接测试线程泄露检查...
checkThreadTest.alone.wait=正在执行：用例1，单线程多次执行连接测试后检查线程数量...
checkThreadTest.multi.wait=正在执行：用例2，多线程多次执行连接测试后检查线程数量...
checkThreadTest.connection.alone.throw=单线程执行连接测试第%s次时连接测试出现错误，%s错误信息：%s。
checkThreadTest.connection.multi.throw=多线程执行连接测试第%s次时连接测试出现错误，当前线程：%s，错误信息：%s。
checkThreadTest.alone.fail=单线程执行测试连接方法%s次，测试前记录线程数量为：%s，线程列表为：\n%s测试后的线程数量为：%s，线程列表为：\n%s前后不一致，多出的线程栈为：\n%s。
checkThreadTest.alone.succeed=单线程执行测试连接方法%s次，测试前记录线程数量为：%s，线程列表为：\n%s测试后的线程数量为：%s，线程列表为：\n%s前后一致符合预期。
checkThreadTest.multi.fail=多线程执行测试连接方法，执行线程%s个，每个线程执行%s次，测试前记录线程数量为：%s，线程列表为：\n%s测试后的线程数量为：%s，线程列表为：\n%s前后不一致，多出的线程栈为：\n%s。
checkThreadTest.multi.succeed=多线程执行测试连接方法，执行线程%s个，每个线程执行%s次，测试前记录线程数量为：%s，线程列表为：\n%s测试后的线程数量为：%s，线程列表为：\n%s前后一致符合预期。
#-----------------------------------------------------------------------------
checkStreamRead.wait=执行已开始：增量验证（依赖StreamReadFunction和TimestampToStreamOffsetFunction）...
checkStreamRead.stream.wait=正在执行：用例1，增量启动成功后写入数据/修改数据/删除数据...
checkStreamRead.stream.notOpen=开启增量启动成功之后，StreamReadConsumer.streamReadStarted()方法需要被数据源调用，但是检测结果显示此方法未调用，不符合预期;
checkStreamRead.stream.opened=StreamReadConsumer.streamReadStarted()方法已调用，符合预期，流程继续。
checkStreamRead.stream.throw=执行增量启动并写入数据、修改数据、删除数据过程中，发现开启增量失败，失败信息：%s，流程已结束，请自查。
checkStreamRead.insert.throw=执行增量启动，增量开启成功，但是在插入%s条数据操作时发生异常，异常信息：%s，流程已结束，请自查。
checkStreamRead.insert.fail=执行增量启动，增量开启成功，此时插入数据%s条，插入返回结果显示插入操作数%s条，修改操作数%s条，删除操作数%s条，前后数据不一致，插入有误，不符合预期。
checkStreamRead.insert=执行增量启动，增量开启成功，此时插入数据%s条，插入返回结果显示插入操作数%s条，修改操作数%s条，删除操作数%s条，插入成功。
checkStreamRead.insert.delay=执行增量启动，增量开启成功，此时插入数据%s条，从插入成功后开始计时，到读取到增量数据，记录的增量延迟达到%s秒，延迟过高，不符合预期，预期值应小于5秒，请自查并及时降低延迟。
checkStreamRead.insert.timely=执行增量启动，增量开启成功，此时插入数据%s条，从插入成功后开始计时，到读取到增量数据，记录的增量延迟只有%s秒，预期值小于5秒，延迟符合预期。
checkStreamRead.insert.countError=执行增量启动，增量开启成功，此时插入数据%s条，增量返回结果应该包含%s条数据，但实际只返回了%s条数据，不符合预期。
checkStreamRead.insert.typeError=执行增量启动，增量开启成功，插入数据%s条后，经过StreamRead查询返回的结果中第%s条数据不是插入事件，预期返回结果中有且仅有%s条插入事件，不符合预期，流程已结束，请自查。
checkStreamRead.insert.orderError=执行增量启动，增量开启成功，插入数据%s条后，经过StreamRead查询返回的结果中从第%s条数据开始，发现返回的数据不是按插入的顺序返回，不符合预期，流程已结束，请自查。
checkStreamRead.update.throw=执行增量启动，增量开启成功，已插入%s条数据，此时随机修改其中%s条，但是在修改时发生异常，异常信息：%s，流程已结束，请自查。
checkStreamRead.update.fail=执行增量启动，增量开启成功，此时插入数据%s条，此时随机修改其中%s条，修改返回结果显示插入操作数%s条，修改操作数%s条，删除操作数%s条，前后数据不一致，修改有误，不符合预期。
checkStreamRead.update=执行增量启动，增量开启成功，此时插入数据%s条，此时随机修改其中%s条，修改返回结果显示插入操作数%s条，修改操作数%s条，删除操作数%s条，修改成功。
checkStreamRead.update.delay=执行增量启动，增量开启成功，此时插入数据%s条，此时随机修改其中%s条，从修改成功后开始计时，到读取到增量数据，记录的增量延迟达到%s秒，延迟过高，不符合预期，预期值应小于5秒，请自查并及时降低延迟。
checkStreamRead.update.timely=执行增量启动，增量开启成功，此时插入数据%s条，此时随机修改其中%s条，从修改成功后开始计时，到读取到增量数据，记录的增量延迟只有%s秒，预期值小于5秒，延迟符合预期。

checkStreamRead.update.typeError=执行增量启动，增量开启成功，插入%s条数据后，随机修改%s条，经过StreamRead查询返回的结果中第%s条数据不是修改事件，预期返回结果中有且仅有%s条修改事件，不符合预期，流程已结束，请自查。
checkStreamRead.update.noKeys=执行增量启动，增量开启成功，插入%s条数据后，随机修改%s条，经过StreamRead查询返回的结果中第%s条数据是修改事件，但是事件before中的内容为空，预期应该至少包含主键信息，请自查。
checkStreamRead.update.keyError=执行增量启动，增量开启成功，插入%s条数据后，随机修改%s条，经过StreamRead查询返回的结果中第%s条数据是修改事件，同时事件after中的内容不为空，但是缺少对应的主键，与预期应该至少包含主键信息相悖，流程已结束，请自查。
checkStreamRead.update.orderError=执行增量启动，增量开启成功，插入%s条数据后，随机修改%s条，经过StreamRead查询返回的结果中第%s条数据是修改事件，同时事件after中的内容不为空并包含了对应的主键，但是返回的数据不是按修改的顺序返回，不符合预期，流程已结束，请自查。
checkStreamRead.update.incomplete=执行增量启动，增量开启成功，插入%s条数据后，随机修改%s条，经过StreamRead查询返回的结果中第%s条数据的修改事件中after内容不是全字段，与实际不符，实际应该包含整条记录，增量返回的内容为：%s，原始内容为：%s。
checkStreamRead.update.complete=执行增量启动，增量开启成功，插入%s条数据后，随机修改%s条，经过StreamRead查询返回的结果中第%s条数据的修改事件中after内容是全字段，与实际相符，增量返回的内容为：%s，原始内容为：%s。
checkStreamRead.update.notEquals=执行增量启动，增量开启成功，插入%s条数据后，随机修改%s条，经过StreamRead查询返回的结果中第%s条数据的修改事件中after内容与原始值存在差异，增量返回的数据（Right）与修改前的数据（Left）不一致，对比结果为：\n%s。
checkStreamRead.update.equals=执行增量启动，增量开启成功，插入%s条数据后，随机修改%s条，经过StreamRead查询返回的结果中第%s条数据的修改事件中after的数据与插入前的数据一致，对比结果符合预期。

checkStreamRead.delete.throw=执行增量启动，增量开启成功，插入%s条数据并随机修改%s条后，随机删除%s条数据，但是在删除数据操作时发生异常，异常信息：%s，流程已结束，请自查。
checkStreamRead.delete.fail=执行增量，增量开启成功，插入%s条数据并随机修改%s条后，随机删除%s条数据，删除返回结果显示插入操作数%s条，修改操作数%s条，删除操作数%s条，前后操作数不一致，删除有误，不符合预期。
checkStreamRead.delete=执行增量启动，增量开启成功，插入%s条数据并随机修改%s条后，随机删除%s条数据，删除返回结果显示插入操作数%s条，修改操作数%s条，删除操作数%s条，删除成功。
checkStreamRead.delete.delay=执行增量启动，增量开启成功，插入%s条数据并随机修改%s条后，随机删除%s条数据，从修改成功后开始计时，到读取到增量数据，记录的增量延迟达到%s秒，延迟过高，不符合预期，预期值应小于5秒，请自查并及时降低延迟。
checkStreamRead.delete.timely=执行增量启动，增量开启成功，插入%s条数据并随机修改%s条后，随机删除%s条数据，从修改成功后开始计时，到读取到增量数据，记录的增量延迟只有%s秒，预期值小于5秒，延迟符合预期。
checkStreamRead.delete.typeError=执行增量启动，增量开启成功，插入%s条数据并随机修改%s条后，随机删除%s条数据后，经过StreamRead查询返回的结果中第%s条数据不是删除事件，预期返回结果中有且仅有%s条删除事件，不符合预期，流程已结束，请自查。
checkStreamRead.delete.noKeys=执行增量启动，增量开启成功，插入%s条数据并随机修改%s条后，随机删除%s条数据后，经过StreamRead查询返回的结果中第%s条数据是删除事件，但是事件before中的内容为空，预期应该至少包含主键信息，实际不符合预期，流程已结束，请自查。
checkStreamRead.delete.keyError=执行增量启动，增量开启成功，插入%s条数据并随机修改%s条后，随机删除%s条数据后，经过StreamRead查询返回的结果中第%s条数据是删除事件，同时事件before中的内容不为空，但是缺少对应的主键，与预期应该至少包含主键信息相悖，测试表的主键有：%s，删除事件的Before仅存在键值：%s，流程已结束，请自查。
checkStreamRead.delete.orderError=执行增量启动，增量开启成功，插入%s条数据并随机修改%s条后，随机删除%s条数据后，经过StreamRead查询返回的结果中第%s条数据是删除事件，同时事件before中的内容不为空并包含了对应的主键，但是返回的数据不是按删除的顺序返回，不符合预期，测试表的主键有：%s，删除事件的Before中存在键值：%s，存在名为 %s 的主键原始记录的值为 %s ，与删除事件的After中的值不一致，流程已结束，请自查。

#=======================================================================================
checkCreateIndex.wait=执行已开始：创建索引验证（依赖QueryIndexesFunction和CreateIndexFunction）...
checkCreateIndex.unique.wait=正在执行：用例1，创建唯一索引...
checkCreateIndex.ascDesc.wait=正在执行：用例2，创建升序和降序的索引...
checkCreateIndex.delete.wait=正在执行：用例3，删除索引验证...

checkCreateIndex.unique.create.fail=创建索引失败，创建索引过程中出现异常：%s。
checkCreateIndex.unique.query.fail=创建索引成功后查询创建的索引信息，但是查询的过程中出现异常：%s。
checkCreateIndex.unique.create.error=创建索引后查询索引信息，发现查询到的索引信息与实际创建的索引不一致，创建前的索引信息：%s，创建后查询出的结果为：%s，索引创建不符合预期，请检查CreateIndexFunction或QueryIndexesFunction。
checkCreateIndex.unique.create.succeed=创建索引后查询索引信息，发现查询到的索引信息与实际创建的索引一致，创建前的索引信息：%s，创建后查询出的结果为：%s。
checkCreateIndex.unique.insert.throw=创建索引并查询索引信息后，插入第%s条数据，插入过程发生异常，异常信息：%s，请检查数据源写入操作的实现过程，当前测试流程已终止。
checkCreateIndex.unique.insert.error=创建索引并查询索引信息后，插入第%s条数据，插入操作的实际返回结果为：插入%s，修改%是，删除%s，结果不符合预期，请检查数据源写入的实现过程。
checkCreateIndex.unique.insert.succeed=创建索引并查询索引信息后，插入第%s条数据，插入操作的实际返回结果为：插入%s，修改%是，删除%s，结果符合预期，下一步：再插入此数据检查索引是否生效。
checkCreateIndex.unique.insert1.succeed=创建索引并查询索引信息后，插入%s条数据后，再次插入相同索引的记录，插入结果符合预期：插入未成功，返回的插入数：%s，修改数：%s，删除数：%s。
checkCreateIndex.unique.insert1.error=创建索引并查询索引信息后，插入%s条数据后，再次插入相同索引的记录并插入成功，结果不符合预期，预期结果不应该插入此数据，返回的插入：%s，修改：%s，删除：%s。
checkCreateIndex.unique.insert1.throw=创建索引并查询索引信息后，插入%s条数据后，再次插入相同索引的记录，此时插入操作发生异常，请检查数据源实现，根据预期此次插入应该是失败的但是不应该发生异常，异常信息：%s。

checkCreateIndex.index.delete.throw=创建索引成功后，执行索引删除，但是删除过程中发现异常，异常信息：%s，请检查删除索引的实现过程。
checkCreateIndex.delete.succeed=创建索引成功后，执行索引删除，但是使用QueryIndexesFunction查询索引后返回结果中包含了创建的索引，索引在执行删除后任然存在，不符合预期，请检查删除索引的实现过程。
checkCreateIndex.delete.fail=创建索引成功后，执行索引删除，使用QueryIndexesFunction查询索引后返回结果中已不包含创建的索引，索引在执行删除后不存在，符合预期。

#======================================================================================
batchPauseAndStream.wait=执行已开始：模拟引擎的全量加增量过程...
batchPauseAndStream.batch.wait=正在执行：用例1，读取全量过程中sleep停顿，通过另外一个线程插入，修改，删除，检查数据是否能一致...
batchPauseAndStream.error.batch=在BatchRead读取过程中出现了一个异常：%s，请检查BatchReadFunction的实现过程。
batchPauseAndStream.count.fail=读取全量过程中sleep停顿，通过另外一个线程插入，修改，删除，使用BatchRead读出记录总数为：%s条，实际插入数据：%s条，不符合预期。
batchPauseAndStream.count.succeed=读取全量过程中sleep停顿，通过另外一个线程插入，修改，删除，使用BatchRead读出记录总数为：%s条，实际插入数据：%s条，符合预期。
batchPauseAndStream.exact.equals.failed=读取全量过程中sleep停顿，通过另外一个线程插入，修改，删除，使用BatchRead读出的第%s条数据，主键%s为%s，与插入前进行值比对，但是未通过数值匹配，匹配结果（Left指BatchRead获取前的结果，Right指BatchRead获取后的结果）：\n\t%s
batchPauseAndStream.exact.equals.succeed=读取全量过程中sleep停顿，通过另外一个线程插入，修改，删除，使用BatchRead读出的第%s条数据，主键%s为%s，与插入前进行值比对，已通过数值匹配，测试通过。
batchPauseAndStream.error.event=读取全量过程中sleep停顿，通过另外一个线程插入，修改，删除，使用BatchRead读出的第%s条数据是一条无效数据，既不是TapInsertRecordEvent，也不是TapUpdateRecordEvent，更不是TapDeleteRecordEvent，不符合预期，请检查结果。
batchPauseAndStream.insertRecord.throw=模拟引擎的全量加增量过程，先插入%s条数据，插入过程出现异常，异常信息：%s，请检查实现过程。
batchPauseAndStream.insertRecord.fail=模拟引擎的全量加增量过程，先插入%s条数据，插入失败，插入结果显示，插入数：%s、修改数：%s、删除数：%s，不符合预期。
batchPauseAndStream.insertRecord=模拟引擎的全量加增量过程，先插入%s条数据，插入结果显示，插入数：%s、修改数：%s、删除数：%s，插入数据成功。
batchPauseAndStream.insertRecord.flat.throw=读取全量过程中sleep停顿，通过另外一个线程插入%s条数据，插入过程出现异常，异常信息：%s，请检查。
batchPauseAndStream.insertRecord.flat.fail=读取全量过程中sleep停顿，通过另外一个线程插入%s条数据，插入失败，插入显示：插入结果显示，插入数：%s、修改数：%s、删除数：%s，插入数据失败。
batchPauseAndStream.insertRecord.flat=读取全量过程中sleep停顿，通过另外一个线程插入%s条数据，插入结果显示，插入数：%s、修改数：%s、删除数：%s，插入数据成功。
batchPauseAndStream.modifyRecord.throw=读取全量过程中sleep停顿，通过另外一个线程插入%s条数据后，执行修改，修改过程出现异常，异常信息：%s，请检查。
batchPauseAndStream.modifyRecord.fail=读取全量过程中sleep停顿，通过另外一个线程插入%s条数据后，执行修改，修改结果显示：插入结果显示：插入数：%s、修改数：%s、删除数：%s，修改数据失败。
batchPauseAndStream.modifyRecord=读取全量过程中sleep停顿，通过另外一个线程插入%s条数据后，执行修改，修改结果显示：插入数：%s、修改数：%s、删除数：%s，修改数据成功。
batchPauseAndStream.deleteRecord.throw=读取全量过程中sleep停顿，通过另外一个线程插入%s条数据并修改后，执行删除，删除过程出现异常，异常信息：%s。
batchPauseAndStream.deleteRecord.fail=读取全量过程中sleep停顿，通过另外一个线程插入%s条数据并修改后，执行删除，删除结果显示：插入数：%s、修改数：%s、删除数：%s，删除数据失败。
batchPauseAndStream.deleteRecord.succeed=读取全量过程中sleep停顿，通过另外一个线程插入%s条数据并修改后，执行删除，删除结果显示：插入数：%s、修改数：%s、删除数：%s，删除数据成功。

#=====================================================================================
codecsFilterManager.wait=正在执行：混合字段的编解码性能对比...
codecsFilterManager.batch.wait=正在执行：用例1，混合字段的编解码性能对比5字段、50字段、100字段、500字段...
codecsFilterManager.batch.v=假设要在%s个字段的表中做插入%s条数据的操作，对其进行值转换是的转换速率为(参考值为3w/s)：%s w/s.






