## SHEIN as a Source

Official website: https://openapi-portal.sheincorp.cn/#/home/<USER>/999999

### Instructions for use

Before using SHEIN as the source, you need to go to SHEIN to contact your corresponding system contact:
- Apply to open an access account
- Obtain openKeyId and secretKey from the specific system contact of SHEIN

#### Create Connection

The connection properties that need to be configured to create a connection include the following parts:

- Open Key ID: After applying to open an access account (entered by the SHEIN backend and provided to the user with openKeyId), fill the obtained openKeyId in this input box
- Secret Key: Apply to open an access account (entered by the SHEIN backend and provided to the user's secretKey), and fill the obtained secretKey into this input account

#### As a source

- You can obtain purchase orders from the merchant service procurement module, including urgent purchases and stock up
- The SHEIN data source serves as the target, supporting full quantity procurement and incremental polling procurement