{"_id": "683761974cf9b840dba2cabd", "name": "default_limit_api", "datasource": "67456a47cb9b0b2eb80ed48b", "tableName": "_tapdata_test_table", "apiVersion": "v1", "basePath": "default_test", "readPreference": "", "readConcern": "", "prefix": "test", "apiType": "defaultApi", "status": "active", "paths": [{"name": "findPage", "method": "GET", "result": "Page<Document>", "fields": [{"autoincrement": "NO", "tapType": "{\"bytes\":24,\"type\":10}", "data_type": "OBJECT_ID", "previousDataType": "OBJECT_ID", "field_name": "_id", "previousFieldName": "_id", "id": "67456a47cb9b0b2eb80ed48b__tapdata_test_table__id", "is_auto_allowed": true, "is_deleted": false, "is_nullable": true, "original_field_name": "_id", "primaryKey": true, "primary_key_position": 1, "source": "auto", "unique": false, "comment": "", "columnPosition": 1, "originalDataType": "OBJECT_ID", "sourceDbType": "MongoDB", "useDefaultValue": true}, {"autoincrement": "NO", "tapType": "{\"bytes\":120,\"type\":10}", "data_type": "STRING(120)", "previousDataType": "STRING(120)", "field_name": "name", "previousFieldName": "name", "id": "67456a47cb9b0b2eb80ed48b__tapdata_test_table_name", "is_auto_allowed": true, "is_deleted": false, "is_nullable": true, "original_field_name": "name", "primaryKey": false, "source": "auto", "unique": false, "comment": "", "columnPosition": 2, "originalDataType": "STRING(120)", "sourceDbType": "MongoDB", "useDefaultValue": true}], "type": "preset", "acl": ["DefaultRoleForNewUser"], "params": [{"name": "page", "type": "number", "defaultvalue": "1", "description": "分页编号"}, {"name": "limit", "type": "number", "defaultvalue": "20", "description": "每个分页返回的记录数"}, {"name": "filter", "type": "object", "description": "过滤条件"}], "path": "", "where": [], "sort": []}], "fields": [{"autoincrement": "NO", "tapType": "{\"bytes\":24,\"type\":10}", "data_type": "OBJECT_ID", "previousDataType": "OBJECT_ID", "field_name": "_id", "previousFieldName": "_id", "id": "67456a47cb9b0b2eb80ed48b__tapdata_test_table__id", "is_auto_allowed": true, "is_deleted": false, "is_nullable": true, "original_field_name": "_id", "primaryKey": true, "primary_key_position": 1, "source": "auto", "unique": false, "comment": "", "columnPosition": 1, "originalDataType": "OBJECT_ID", "sourceDbType": "MongoDB", "useDefaultValue": true}, {"autoincrement": "NO", "tapType": "{\"bytes\":120,\"type\":10}", "data_type": "STRING(120)", "previousDataType": "STRING(120)", "field_name": "name", "previousFieldName": "name", "id": "67456a47cb9b0b2eb80ed48b__tapdata_test_table_name", "is_auto_allowed": true, "is_deleted": false, "is_nullable": true, "original_field_name": "name", "primaryKey": false, "source": "auto", "unique": false, "comment": "", "columnPosition": 2, "originalDataType": "STRING(120)", "sourceDbType": "MongoDB", "useDefaultValue": true}], "listtags": [{"id": "673327d7f1cfec2f9caf8d67"}], "connection": "67456a47cb9b0b2eb80ed48b", "connectionId": "67456a47cb9b0b2eb80ed48b", "operationType": "GET", "connectionType": "MongoDB", "connectionName": "qa_mongodb_repl_42240_share_1716348488635_3031", "pathAccessMethod": "customize", "createTime": "2025-05-28T19:18:47.765Z", "last_updated": "2025-06-03T09:54:29.580Z", "user_id": "62bc5008d4958d013d97c7a6", "lastUpdBy": "62bc5008d4958d013d97c7a6", "createUser": "<EMAIL>", "_class": "com.tapdata.tm.modules.entity.ModulesEntity"}