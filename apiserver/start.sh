#!/bin/bash

# Parse command line arguments
WORKER_COUNT=""
SHOW_HELP=false

while [[ $# -gt 0 ]]; do
  case $1 in
    -p|--workers)
      WORKER_COUNT="$2"
      shift 2
      ;;
    -h|--help)
      SHOW_HELP=true
      shift
      ;;
    *)
      echo "Unknown option: $1"
      SHOW_HELP=true
      shift
      ;;
  esac
done

# Show help if requested or invalid arguments
if [ "$SHOW_HELP" = true ]; then
  echo "Usage: $0 [OPTIONS]"
  echo ""
  echo "Options:"
  echo "  -p, --workers NUMBER    Set the number of worker processes"
  echo "                          (default: 0 = CPU cores count)"
  echo "  -h, --help             Show this help message"
  echo ""
  echo "Examples:"
  echo "  $0                     # Start with default worker count (CPU cores)"
  echo "  $0 -p 4                # Start with 4 worker processes"
  echo "  $0 --workers 1         # Start with 1 worker process"
  echo ""
  exit 0
fi

WORK_DIR="`pwd`"
APP_HOME="$(cd `dirname $0`; pwd)"

# Load nvm if available and ensure we use a compatible Node.js version
if [ -f ~/.nvm/nvm.sh ]; then
    source ~/.nvm/nvm.sh
    # Try to use Node.js 22 if available, otherwise use the latest available
    if nvm list | grep -q "v22"; then
        nvm use 22 >/dev/null 2>&1
    elif nvm list | grep -q "v18"; then
        nvm use 18 >/dev/null 2>&1
    elif nvm list | grep -q "v16"; then
        nvm use 16 >/dev/null 2>&1
    fi
fi

# Check current Node.js version
CURRENT_NODE_VERSION=$(node -v 2>/dev/null | sed 's/v//' | cut -d. -f1)
if [ -z "$CURRENT_NODE_VERSION" ] || [ "$CURRENT_NODE_VERSION" -lt 16 ]; then
    if [ -f "$APP_HOME/NDK/node/bin/node" ]; then
        export PATH=$APP_HOME/NDK/node/bin:$PATH
        echo "Using NDK Node.js (system Node.js version too old or not found)"
    else
        echo "Warning: Node.js version $CURRENT_NODE_VERSION may be too old. Consider upgrading to Node.js 16+"
    fi
else
    echo "Using system Node.js version: v$CURRENT_NODE_VERSION"
fi

# Set API_WORKER_COUNT environment variable if specified
if [ -n "$WORKER_COUNT" ]; then
  export API_WORKER_COUNT="$WORKER_COUNT"
  echo "Setting worker count to: $WORKER_COUNT"
fi

echo
echo
echo "Shell PATH: "$PATH
echo
echo "Path of node: `which node`"
echo "Version of node: `node -v`"
# echo "Version of npm: "
# npm version
# echo "Version of npx: `npx -v`"
echo
echo "Start API Server..."
echo
echo "APP_HOME: $APP_HOME"
echo "WORK_DIR: $WORK_DIR"

# Display worker count information
if [ -n "$API_WORKER_COUNT" ]; then
  echo "Worker Count: $API_WORKER_COUNT (manually set)"
else
  CPU_CORES=$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo "unknown")
  echo "Worker Count: $CPU_CORES (auto-detected CPU cores)"
fi

echo
echo "Watch logs with:"
echo "tail -f ${TAPDATA_WORK_DIR:=~/.tapdata}/logs/api-server.log"
echo
echo "Config file at:"
echo "$APP_HOME/config.js"
echo
echo "Stop API server with:"
echo "$APP_HOME/stop.sh"
echo

if [ -d "$APP_HOME/dist" ]; then
	node $APP_HOME/index.js --max-old-space-size=1024 > run.log 2>&1 &
	# node $APP_HOME/index.js > /dev/null &
	echo "API Server started."
else
	cd $APP_HOME
	npm run build
	cd $WORK_DIR
	node $APP_HOME/index.js --max-old-space-size=1024 > run.log 2>&1 &
	# node $APP_HOME/index.js > /dev/null &
	echo "API Server started."
fi

