/**
 * 测试动态时间窗口限速功能
 * 验证小于 1 QPS 的限速是否通过调整时间窗口正确实现
 */

const { RateLimiter } = require('./dist/src/rate-limiter');

console.log('🚀 动态时间窗口限速功能测试\n');

// 测试场景配置
const testScenarios = [
    {
        workers: 8,
        configLimit: 4,
        expectedPerWorker: 0.5,
        expectedWindow: 2000,
        description: "8 workers, 4 QPS → 每个 worker 0.5 QPS (每 2 秒 1 个请求)"
    },
    {
        workers: 10,
        configLimit: 3,
        expectedPerWorker: 0.3,
        expectedWindow: 3333,
        description: "10 workers, 3 QPS → 每个 worker 0.3 QPS (每 3.33 秒 1 个请求)"
    },
    {
        workers: 4,
        configLimit: 1,
        expectedPerWorker: 0.25,
        expectedWindow: 4000,
        description: "4 workers, 1 QPS → 每个 worker 0.25 QPS (每 4 秒 1 个请求)"
    },
    {
        workers: 2,
        configLimit: 5,
        expectedPerWorker: 2.5,
        expectedWindow: 1000,
        description: "2 workers, 5 QPS → 每个 worker 2 QPS (每 1 秒 2 个请求)"
    },
    {
        workers: 1,
        configLimit: 10,
        expectedPerWorker: 10,
        expectedWindow: 1000,
        description: "1 worker, 10 QPS → 每个 worker 10 QPS (每 1 秒 10 个请求)"
    }
];

console.log('📊 限制计算验证');
console.log('=' .repeat(100));
console.log('| Workers | Config | Per Worker | Window(ms) | Effective | Description');
console.log('|---------|--------|------------|------------|-----------|-------------');

testScenarios.forEach(scenario => {
    // 模拟不同的 worker 数量
    const originalEnv = process.env.API_WORKER_COUNT;
    process.env.API_WORKER_COUNT = scenario.workers.toString();

    // 创建新的限速器实例
    const testRateLimiter = new (require('./dist/src/rate-limiter').RateLimiter)();

    // 获取详细的限制计算信息
    const limitInfo = testRateLimiter.getLimitCalculationInfo(scenario.configLimit);

    const status = Math.abs(limitInfo.windowMs - scenario.expectedWindow) <= 100 ? '✅' : '❌';

    console.log(`| ${scenario.workers.toString().padEnd(7)} | ${scenario.configLimit.toString().padEnd(6)} | ${limitInfo.exactWorkerLimit.toFixed(3).padEnd(10)} | ${limitInfo.windowMs.toString().padEnd(10)} | ${limitInfo.effectiveLimit.toString().padEnd(9)} | ${status} ${limitInfo.description}`);

    // 恢复环境变量
    if (originalEnv) {
        process.env.API_WORKER_COUNT = originalEnv;
    } else {
        delete process.env.API_WORKER_COUNT;
    }
});

console.log('\n');

// 实际限速测试
console.log('🧪 实际限速效果测试');
console.log('=' .repeat(50));

async function testDynamicWindow(workerCount, configLimit, testName) {
    console.log(`\n📋 ${testName}`);
    console.log('-' .repeat(40));

    // 设置环境变量
    const originalEnv = process.env.API_WORKER_COUNT;
    process.env.API_WORKER_COUNT = workerCount.toString();

    // 创建新的限速器实例
    const testRateLimiter = new (require('./dist/src/rate-limiter').RateLimiter)();

    const apiId = `test-dynamic-${workerCount}-${configLimit}`;
    const limitInfo = testRateLimiter.getLimitCalculationInfo(configLimit);

    console.log(`配置: ${configLimit} QPS, ${workerCount} workers`);
    console.log(`计算结果: ${limitInfo.description}`);
    console.log(`时间窗口: ${limitInfo.windowMs} ms`);

    // 测试第一个请求（应该被允许）
    console.log('\n测试请求:');
    const startTime = Date.now();

    const firstRequest = testRateLimiter.checkRateLimit(apiId, { limit: configLimit });
    console.log(`请求 1 (${Date.now() - startTime}ms): ${firstRequest ? '✅ 允许' : '❌ 被限制'}`);

    // 立即测试第二个请求（应该被限制）
    const secondRequest = testRateLimiter.checkRateLimit(apiId, { limit: configLimit });
    console.log(`请求 2 (${Date.now() - startTime}ms): ${secondRequest ? '✅ 允许' : '❌ 被限制'}`);

    if (limitInfo.windowMs > 1000) {
        console.log(`\n⏰ 等待时间窗口重置 (${limitInfo.windowMs}ms)...`);

        // 等待时间窗口重置
        await new Promise(resolve => setTimeout(resolve, limitInfo.windowMs + 100));

        // 测试窗口重置后的请求
        const thirdRequest = testRateLimiter.checkRateLimit(apiId, { limit: configLimit });
        console.log(`请求 3 (${Date.now() - startTime}ms): ${thirdRequest ? '✅ 允许' : '❌ 被限制'}`);

        if (firstRequest && !secondRequest && thirdRequest) {
            console.log('✅ 动态时间窗口测试通过');
        } else {
            console.log('❌ 动态时间窗口测试失败');
        }
    } else {
        // 对于正常的 1 秒窗口，测试多个请求
        let allowedCount = firstRequest ? 1 : 0; // 计算第一个请求
        if (secondRequest) allowedCount++; // 计算第二个请求

        for (let i = 3; i <= limitInfo.effectiveLimit + 2; i++) {
            const request = testRateLimiter.checkRateLimit(apiId, { limit: configLimit });
            console.log(`请求 ${i} (${Date.now() - startTime}ms): ${request ? '✅ 允许' : '❌ 被限制'}`);
            if (request) allowedCount++;
        }

        if (allowedCount === limitInfo.effectiveLimit) {
            console.log('✅ 正常时间窗口测试通过');
        } else {
            console.log(`❌ 正常时间窗口测试失败: 期望 ${limitInfo.effectiveLimit}, 实际 ${allowedCount}`);
        }
    }

    // 恢复环境变量
    if (originalEnv) {
        process.env.API_WORKER_COUNT = originalEnv;
    } else {
        delete process.env.API_WORKER_COUNT;
    }
}

// 运行测试
async function runTests() {
    // 测试小于 1 QPS 的场景
    await testDynamicWindow(8, 4, "场景1: 8 workers, 4 QPS (每个 worker 0.5 QPS)");
    await testDynamicWindow(4, 1, "场景2: 4 workers, 1 QPS (每个 worker 0.25 QPS)");

    // 测试正常的场景
    await testDynamicWindow(2, 6, "场景3: 2 workers, 6 QPS (每个 worker 3 QPS)");

    console.log('\n🎉 所有测试完成!');

    console.log('\n📝 功能总结:');
    console.log('1. ✅ 支持小于 1 QPS 的精确限速');
    console.log('2. ✅ 通过动态调整时间窗口实现精确控制');
    console.log('3. ✅ 保持总体限速不超过配置值');
    console.log('4. ✅ 向后兼容正常的限速场景');

    console.log('\n🔧 实现原理:');
    console.log('- 当每个 worker 限制 >= 1 QPS: 使用 1 秒时间窗口');
    console.log('- 当每个 worker 限制 < 1 QPS: 扩大时间窗口，保持 1 个请求/窗口');
    console.log('- 例如: 0.5 QPS = 1 个请求/2 秒, 0.25 QPS = 1 个请求/4 秒');
}

runTests().catch(console.error);
