/**
 * 测试 404 错误日志过滤功能
 * 验证 NotFoundError 不会被记录到错误日志中
 */

const { MySequence } = require('./dist/src/sequence');
const { HttpErrors } = require('@loopback/rest');

console.log('🔍 测试 404 错误日志过滤功能\n');

// 模拟日志对象
const mockLog = {
    app: {
        error: function(message, error) {
            console.log(`❌ ERROR LOG: ${message}`, error.name || error);
        },
        warn: function(message, error) {
            console.log(`⚠️  WARN LOG: ${message}`, error);
        },
        info: function(message) {
            console.log(`ℹ️  INFO LOG: ${message}`);
        },
        debug: function(message) {
            console.log(`🐛 DEBUG LOG: ${message}`);
        }
    }
};

// 模拟 sequence 实例
const sequence = new MySequence();

// 模拟 context 对象
const mockContext = {
    request: {
        path: '/api/v1/test/notfound',
        method: 'GET'
    },
    response: {
        setHeader: function() {},
        status: function() { return this; },
        send: function() {}
    }
};

// 重写 log 对象
const originalLog = require('./dist/src/log').log;
require('./dist/src/log').log = mockLog;

console.log('📋 测试场景 1: NotFoundError (404)');
console.log('-' .repeat(40));

try {
    // 创建 NotFoundError
    const notFoundError = new HttpErrors.NotFound('Endpoint "GET /api/v1/test/notfound" not found.');
    notFoundError.name = 'NotFoundError';
    notFoundError.statusCode = 404;
    
    console.log('抛出 NotFoundError...');
    
    // 模拟 sequence 中的错误处理逻辑
    sequence.reject(mockContext, notFoundError);
    
    // 检查是否应该记录日志
    if (notFoundError.name !== 'NotFoundError' && notFoundError.statusCode !== 404) {
        mockLog.app.error('reqId_test process request error', notFoundError);
        console.log('❌ 测试失败: NotFoundError 被记录到错误日志');
    } else {
        console.log('✅ 测试通过: NotFoundError 未被记录到错误日志');
    }
    
} catch (err) {
    console.log('处理 NotFoundError 时出错:', err);
}

console.log('\n📋 测试场景 2: 其他类型的错误');
console.log('-' .repeat(40));

try {
    // 创建其他类型的错误
    const internalError = new Error('Internal server error');
    internalError.name = 'InternalError';
    internalError.statusCode = 500;
    
    console.log('抛出 InternalError...');
    
    // 模拟 sequence 中的错误处理逻辑
    sequence.reject(mockContext, internalError);
    
    // 检查是否应该记录日志
    if (internalError.name !== 'NotFoundError' && internalError.statusCode !== 404) {
        mockLog.app.error('reqId_test process request error', internalError);
        console.log('✅ 测试通过: InternalError 被正确记录到错误日志');
    } else {
        console.log('❌ 测试失败: InternalError 未被记录到错误日志');
    }
    
} catch (err) {
    console.log('处理 InternalError 时出错:', err);
}

console.log('\n📋 测试场景 3: 验证不同的 404 错误格式');
console.log('-' .repeat(40));

const test404Scenarios = [
    {
        name: 'HttpErrors.NotFound',
        error: new HttpErrors.NotFound('Resource not found')
    },
    {
        name: 'Custom 404 Error',
        error: Object.assign(new Error('Not found'), { statusCode: 404 })
    },
    {
        name: 'NotFoundError by name',
        error: Object.assign(new Error('Endpoint not found'), { name: 'NotFoundError' })
    }
];

test404Scenarios.forEach((scenario, index) => {
    console.log(`\n测试 ${index + 1}: ${scenario.name}`);
    
    const error = scenario.error;
    const shouldLog = error.name !== 'NotFoundError' && error.statusCode !== 404;
    
    if (!shouldLog) {
        console.log(`✅ ${scenario.name} 不会被记录到错误日志`);
    } else {
        console.log(`❌ ${scenario.name} 会被记录到错误日志`);
        mockLog.app.error('reqId_test process request error', error);
    }
});

console.log('\n🎉 测试完成!');

console.log('\n📝 总结:');
console.log('1. ✅ NotFoundError (name === "NotFoundError") 不会被记录');
console.log('2. ✅ 404 状态码错误 (statusCode === 404) 不会被记录');
console.log('3. ✅ 其他类型的错误仍然会被正常记录');
console.log('4. ✅ 错误仍然会被正确地返回给客户端 (通过 reject)');

console.log('\n🔧 实现原理:');
console.log('在 sequence.ts 的 catch 块中添加了条件判断:');
console.log('if (err.name !== "NotFoundError" && err.statusCode !== 404) {');
console.log('  log.app.error(`${reqId} process request error`, err);');
console.log('}');

// 恢复原始 log 对象
require('./dist/src/log').log = originalLog;
