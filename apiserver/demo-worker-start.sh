#!/bin/bash

echo "🚀 API Server Worker 启动演示"
echo "================================"
echo ""

echo "📋 可用的启动选项:"
echo ""

# 显示帮助信息
echo "1. 查看帮助信息:"
echo "   ./start.sh -h"
echo ""
./start.sh -h
echo ""

echo "2. 不同 Worker 数量启动示例:"
echo ""

echo "   单 Worker 模式 (适合开发环境):"
echo "   ./start.sh -p 1"
echo ""

echo "   四 Worker 模式 (适合中等负载):"
echo "   ./start.sh -p 4"
echo ""

echo "   八 Worker 模式 (适合高负载):"
echo "   ./start.sh -p 8"
echo ""

echo "   默认模式 (使用 CPU 核心数):"
echo "   ./start.sh"
echo ""

echo "📊 Worker 数量对限速的影响:"
echo ""
echo "假设 API 配置限制为 10 QPS:"
echo ""
echo "| Worker 数量 | 每个 Worker 限制 | 总体限制 |"
echo "|-------------|------------------|----------|"
echo "| 1           | 10 QPS           | 10 QPS   |"
echo "| 2           | 5 QPS            | 10 QPS   |"
echo "| 4           | 2 QPS            | 8 QPS    |"
echo "| 8           | 1 QPS            | 8 QPS    |"
echo "| 16          | 1 QPS            | 16 QPS   |"
echo ""

echo "💡 建议:"
echo ""
echo "- 开发环境: 使用 1 个 Worker (./start.sh -p 1)"
echo "- 测试环境: 使用 2-4 个 Worker (./start.sh -p 4)"
echo "- 生产环境: 根据 CPU 核心数和负载情况调整"
echo ""

echo "🔍 监控 Worker 信息:"
echo ""
echo "启动后可以在日志中看到 Worker 数量信息，例如:"
echo "Worker Count: 4 (manually set)"
echo "或"
echo "Worker Count: 16 (auto-detected CPU cores)"
echo ""

echo "📝 环境变量方式 (高级用法):"
echo ""
echo "export API_WORKER_COUNT=4"
echo "./start.sh"
echo ""

echo "🎉 演示完成!"
echo ""
echo "选择合适的 Worker 数量启动 API Server，享受智能限速功能！"
