/**
 * 测试 originalConfig 中是否包含 limit 字段
 * 验证配置传递链路是否正确
 */

const CRUDAPI = require('./dist/apis/crudAPI');
const tapDataConfigAdapter = require('./generators/tapDataConfigAdapter');

console.log('🔍 测试 originalConfig 中的 limit 字段传递\n');

// 模拟 API 定义数据（包含 limit 字段）
const mockApiDefinition = {
  connections: [
    {
      id: 'test-datasource-id',
      name: 'test-datasource',
      database_type: 'MongoDB',
      database_host: 'localhost',
      database_port: 27017,
      database_name: 'test_db'
    }
  ],
  apis: [
    {
      id: 'test-api-id',
      name: 'test-api',
      description: 'Test API with rate limit',
      datasource: 'test-datasource-id',
      tablename: 'test_table',
      apiVersion: 'v1',
      basePath: 'test',
      prefix: 'api',
      limit: 15, // 设置限制为 15 QPS
      status: 'active',
      fields: [
        {
          field_name: '_id',
          data_type: 'string',
          primary_key_position: 1
        },
        {
          field_name: 'name',
          data_type: 'string',
          primary_key_position: 0
        }
      ],
      paths: [
        {
          name: 'findPage',
          method: 'GET',
          type: 'preset',
          result: 'Page<Document>',
          description: 'Find page with rate limit',
          path: '',
          acl: ['DefaultRole']
        }
      ]
    }
  ]
};

console.log('📋 测试步骤 1: tapDataConfigAdapter 处理');
console.log('=' .repeat(50));

// 测试 tapDataConfigAdapter
const adaptedConfig = tapDataConfigAdapter(mockApiDefinition);

if (adaptedConfig && adaptedConfig.models && adaptedConfig.models.length > 0) {
  const model = adaptedConfig.models[0];
  console.log('✅ tapDataConfigAdapter 处理成功');
  console.log(`   模型名称: ${model.tablename}`);
  console.log(`   API ID: ${model.apiId}`);
  console.log(`   Limit 字段: ${model.limit}`);
  
  if (model.limit === 15) {
    console.log('✅ tapDataConfigAdapter 正确传递了 limit 字段');
  } else {
    console.log('❌ tapDataConfigAdapter 未正确传递 limit 字段');
  }
} else {
  console.log('❌ tapDataConfigAdapter 处理失败');
  process.exit(1);
}

console.log('\n📋 测试步骤 2: CRUDAPI 处理');
console.log('=' .repeat(50));

// 模拟数据源对象
const mockDataSource = {
  getName: () => 'test-datasource',
  getType: () => 'MongoDB',
  config: {
    settings: {
      schema: 'test_schema'
    }
  }
};

// 使用适配后的配置创建 CRUDAPI
const apiConfig = adaptedConfig.models[0];
try {
  const crudAPI = new CRUDAPI(mockDataSource, apiConfig);
  
  // 获取控制器配置
  const controller = crudAPI.getController();
  
  if (controller && controller.originalConfig) {
    console.log('✅ CRUDAPI 处理成功');
    console.log(`   控制器名称: ${controller.name}`);
    console.log(`   API ID: ${controller.apiId}`);
    console.log(`   originalConfig.limit: ${controller.originalConfig.limit}`);
    
    if (controller.originalConfig.limit === 15) {
      console.log('✅ CRUDAPI 正确传递了 originalConfig.limit 字段');
    } else {
      console.log('❌ CRUDAPI 未正确传递 originalConfig.limit 字段');
      console.log(`   期望值: 15, 实际值: ${controller.originalConfig.limit}`);
    }
    
    // 显示 originalConfig 的完整内容
    console.log('\n📄 originalConfig 完整内容:');
    console.log(JSON.stringify(controller.originalConfig, null, 2));
    
  } else {
    console.log('❌ CRUDAPI 未生成有效的控制器配置');
  }
} catch (error) {
  console.log('❌ CRUDAPI 处理出错:', error.message);
}

console.log('\n📋 测试步骤 3: 验证模板渲染数据');
console.log('=' .repeat(50));

// 模拟模板渲染时的数据结构
try {
  const crudAPI = new CRUDAPI(mockDataSource, apiConfig);
  const controller = crudAPI.getController();
  
  if (controller) {
    // 这是传递给模板的数据结构
    const templateData = {
      name: controller.name,
      modelName: controller.modelName,
      repositoryName: controller.repositoryName,
      tableName: controller.tableName,
      apiId: controller.apiId,
      apiName: controller.apiName,
      api: controller.api,
      originalConfig: controller.originalConfig, // 这里是关键
      dataSource: controller.dataSource
    };
    
    console.log('✅ 模板数据准备成功');
    console.log(`   templateData.originalConfig.limit: ${templateData.originalConfig.limit}`);
    
    if (templateData.originalConfig.limit === 15) {
      console.log('✅ 模板将接收到正确的 limit 值');
    } else {
      console.log('❌ 模板将接收到错误的 limit 值');
    }
    
    // 模拟 sequence.ts 中的访问方式
    console.log('\n🔍 模拟 sequence.ts 中的访问:');
    const apiMeta = {
      options: {
        originalConfig: templateData.originalConfig
      }
    };
    
    const limit = parseInt(apiMeta.options.originalConfig.limit) || 1;
    console.log(`   解析后的 limit 值: ${limit}`);
    
    if (limit === 15) {
      console.log('✅ sequence.ts 将获得正确的 limit 值');
    } else {
      console.log('❌ sequence.ts 将获得错误的 limit 值');
    }
    
  }
} catch (error) {
  console.log('❌ 模板数据验证出错:', error.message);
}

console.log('\n🎉 测试完成!');
console.log('\n📝 总结:');
console.log('1. tapDataConfigAdapter.js 已修改，支持传递 limit 字段');
console.log('2. crudAPI.js 已修改，在 _generateModel 中包含 limit 字段');
console.log('3. originalConfig 现在应该包含正确的 limit 值');
console.log('4. 重新生成 API 代码后，限速功能应该正常工作');
