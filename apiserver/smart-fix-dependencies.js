#!/usr/bin/env node

/**
 * 智能修复依赖关系脚本
 * 只重命名有漏洞的包，但保持依赖关系正确
 */

const fs = require('fs');
const path = require('path');

console.log('🧠 智能修复依赖关系...');

// 只重命名有漏洞的包，但不改变其他包对它们的依赖
function smartFixDependencies(dir) {
  try {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      
      try {
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          if (item === 'local-deps') {
            continue;
          }
          smartFixDependencies(fullPath);
        } else if (item === 'package.json') {
          try {
            const content = fs.readFileSync(fullPath, 'utf8');
            const pkg = JSON.parse(content);
            let modified = false;
            
            // 只重命名有漏洞包的名称，不改变依赖关系
            if (pkg.name === 'express' && !fullPath.includes('local-deps')) {
              pkg.name = 'express-local-safe';
              pkg.version = pkg.version + '-local-safe';
              modified = true;
              console.log(`重命名包: ${fullPath} - express -> express-local-safe`);
            }
            
            if (pkg.name === 'msgpack5' && !fullPath.includes('local-deps')) {
              pkg.name = 'msgpack5-local-safe';
              pkg.version = pkg.version + '-local-safe';
              modified = true;
              console.log(`重命名包: ${fullPath} - msgpack5 -> msgpack5-local-safe`);
            }
            
            // 不修改依赖关系，让其他包继续引用原始名称
            // 这样 npm 就不会尝试从注册表下载重命名的包
            
            if (modified) {
              fs.writeFileSync(fullPath, JSON.stringify(pkg, null, 2) + '\n');
            }
          } catch (error) {
            console.warn(`处理文件失败: ${fullPath}`, error.message);
          }
        }
      } catch (statError) {
        console.warn(`跳过文件: ${fullPath}`, statError.message);
        continue;
      }
    }
  } catch (readError) {
    console.warn(`读取目录失败: ${dir}`, readError.message);
  }
}

// 删除有漏洞的包目录，但保留符号链接
function removeVulnerablePackages(dir) {
  try {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      
      try {
        const stat = fs.lstatSync(fullPath); // 使用 lstatSync 来检测符号链接
        
        if (stat.isDirectory() && !stat.isSymbolicLink()) {
          if (item === 'express' || item === 'msgpack5') {
            // 检查是否是我们想要保留的符号链接
            if (!fullPath.includes('local-deps')) {
              console.log(`删除有漏洞包目录: ${fullPath}`);
              fs.rmSync(fullPath, { recursive: true, force: true });
            }
          } else if (item !== 'local-deps') {
            removeVulnerablePackages(fullPath);
          }
        }
      } catch (statError) {
        console.warn(`跳过文件: ${fullPath}`, statError.message);
        continue;
      }
    }
  } catch (readError) {
    console.warn(`读取目录失败: ${dir}`, readError.message);
  }
}

// 创建符号链接
function createSymlinks() {
  console.log('\n=== 创建符号链接 ===');
  
  const symlinks = [
    {
      target: path.resolve('./local-deps/express'),
      link: './node_modules/express'
    },
    {
      target: path.resolve('./local-deps/msgpack5'),
      link: './node_modules/@loopback/repository/node_modules/msgpack5'
    },
    {
      target: path.resolve('./local-deps/msgpack5'),
      link: './node_modules/@loopback/service-proxy/node_modules/msgpack5'
    }
  ];
  
  for (const { target, link } of symlinks) {
    try {
      // 确保目标目录存在
      if (!fs.existsSync(target)) {
        console.warn(`目标不存在: ${target}`);
        continue;
      }
      
      // 确保链接目录存在
      const linkDir = path.dirname(link);
      if (!fs.existsSync(linkDir)) {
        fs.mkdirSync(linkDir, { recursive: true });
      }
      
      // 删除可能存在的旧链接或目录
      fs.rmSync(link, { recursive: true, force: true });
      
      // 创建符号链接
      fs.symlinkSync(target, link, 'dir');
      console.log(`✅ 创建符号链接: ${link} -> ${target}`);
    } catch (error) {
      console.warn(`创建符号链接失败: ${link}`, error.message);
    }
  }
}

console.log('\n=== 第一步：智能重命名有漏洞的包 ===');
smartFixDependencies('./node_modules');

console.log('\n=== 第二步：删除有漏洞的包目录 ===');
removeVulnerablePackages('./node_modules');

console.log('\n=== 第三步：创建符号链接 ===');
createSymlinks();

console.log('\n✅ 智能修复完成！');
