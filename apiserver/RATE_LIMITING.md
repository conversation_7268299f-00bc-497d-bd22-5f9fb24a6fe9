# API 速率限制功能

## 概述

API Server 现在支持对每个API端点进行速率限制，以防止过度使用和保护系统资源。当请求超过配置的限制时，系统将返回HTTP 429状态码。

## 功能特性

- **每秒请求限制**: 可以为每个API配置每秒最大请求数
- **独立限制**: 每个API端点有独立的速率限制计数器
- **内存存储**: 使用高效的内存存储跟踪请求计数
- **自动重置**: 时间窗口到期后自动重置计数器
- **429错误响应**: 超过限制时返回标准的HTTP 429状态码

## 配置方法

### 1. 在API定义中添加limit字段

在API定义的JSON配置中，在最外层添加 `limit` 字段：

```json
{
  "_id": "683761974cf9b840dba2cabc",
  "name": "xxx",
  "datasource": "67456a47cb9b0b2eb80ed48b",
  "tableName": "_tapdata_heartbeat_table",
  "apiVersion": "v1",
  "basePath": "x9thfsd41vy",
  "limit": 10,
  "status": "active",
  "paths": [
    // ... API路径配置
  ]
  // ... 其他配置
}
```

### 2. limit字段说明

- **类型**: 数字
- **单位**: 每秒请求数 (QPS - Queries Per Second)
- **配置规则**:
  - `limit > 0`: 启用限速，每秒允许指定数量的请求
  - `limit = 0`: 禁用限速，允许无限制访问
  - `limit < 0`: 禁用限速，允许无限制访问
  - 未配置或无效值: 默认为 1 QPS
- **示例**:
  - `"limit": 10` → 每秒最多允许10个请求
  - `"limit": 0` → 禁用限速，无限制访问
  - `"limit": -1` → 禁用限速，无限制访问
  - 不配置limit字段 → 默认1 QPS

## 错误响应

当请求超过速率限制时，API将返回：

### HTTP状态码
- **429 Too Many Requests**

### 错误响应格式

```json
{
  "error": {
    "statusCode": 429,
    "name": "TooManyRequestsError",
    "message": "Rate limit exceeded. Maximum 10 requests per second allowed."
  }
}
```

### 错误代码映射

如果启用了错误代码模式 (`ERROR_CODE=true`)，将返回：
- **错误代码**: `100429`

## 实现原理

### 1. 速率限制器 (RateLimiter)

- 使用单例模式确保全局一致性
- 基于固定时间窗口算法
- 内存存储，重启后重置
- **Worker 感知**: 自动检测 worker 数量并分配限制

### 2. 请求处理流程

```
请求到达 → 路由解析 → 身份认证 → 速率限制检查 → 业务逻辑处理
                                    ↓
                              超过限制 → 返回429错误
```

### 3. Worker 限制分配与动态时间窗口

- **检测 Worker 数量**: 从环境变量 `API_WORKER_COUNT` 或 CPU 核心数获取
- **限制分配**: 配置的限制 ÷ Worker 数量 = 每个 Worker 的精确限制
- **动态时间窗口**: 当每个 Worker 限制 < 1 QPS 时，自动调整时间窗口
- **手动设置**: 使用 `start.sh -p <数量>` 指定 Worker 数量
- **示例**:
  - 配置 10 QPS，4 个 Worker → 每个 Worker 2 QPS (1秒窗口，2个请求)
  - 配置 4 QPS，8 个 Worker → 每个 Worker 0.5 QPS (2秒窗口，1个请求)
  - 配置 1 QPS，4 个 Worker → 每个 Worker 0.25 QPS (4秒窗口，1个请求)

#### Worker 数量设置方法

```bash
# 使用默认数量 (CPU 核心数)
./start.sh

# 指定 4 个 worker
./start.sh -p 4

# 指定 1 个 worker (单进程模式)
./start.sh --workers 1

# 查看帮助
./start.sh -h
```

### 4. 动态时间窗口机制

#### 4.1 基本原理

- **标准窗口**: 当每个 Worker 限制 ≥ 1 QPS 时，使用 1 秒时间窗口
- **扩展窗口**: 当每个 Worker 限制 < 1 QPS 时，自动扩展时间窗口
- **计算公式**: 窗口大小 = 1 / (每个Worker的QPS) × 1000 毫秒

#### 4.2 工作示例

| 配置限制 | Worker数 | 每Worker限制 | 时间窗口 | 窗口内允许请求 |
|----------|----------|--------------|----------|----------------|
| 10 QPS   | 4        | 2.5 QPS      | 1000ms   | 2个请求        |
| 8 QPS    | 8        | 1.0 QPS      | 1000ms   | 1个请求        |
| 4 QPS    | 8        | 0.5 QPS      | 2000ms   | 1个请求        |
| 1 QPS    | 4        | 0.25 QPS     | 4000ms   | 1个请求        |

#### 4.3 重置机制

- **自动重置**: 时间窗口到期后自动重置计数器
- **精确控制**: 确保平均请求率不超过配置限制
- **清理机制**: 定期清理过期的记录

## 使用示例

### 示例1: 使用默认限制 (1 QPS)

```json
{
  "name": "user_api",
  "paths": [
    {
      "name": "findPage",
      "method": "GET"
    }
  ]
}
```
注意：没有配置limit字段，系统将自动应用默认的1 QPS限制。

### 示例2: 设置API每秒10个请求的限制

```json
{
  "name": "user_api",
  "limit": 10,
  "paths": [
    {
      "name": "findPage",
      "method": "GET"
    }
  ]
}
```

### 示例3: 高频API设置更高限制

```json
{
  "name": "metrics_api",
  "limit": 100,
  "paths": [
    {
      "name": "getMetrics",
      "method": "GET"
    }
  ]
}
```

### 示例4: 关键API设置较低限制

```json
{
  "name": "admin_api",
  "limit": 5,
  "paths": [
    {
      "name": "deleteUser",
      "method": "DELETE"
    }
  ]
}
```

### 示例5: 禁用限速（开发/测试环境）

```json
{
  "name": "dev_api",
  "limit": 0,
  "paths": [
    {
      "name": "testEndpoint",
      "method": "GET"
    }
  ]
}
```
注意：设置 `limit: 0` 将完全禁用限速，适用于开发环境或不需要限速的特殊API。

## 监控和调试

### 日志记录

当请求被速率限制阻止时，系统会记录警告日志：

```
Rate limit exceeded for API 683761974cf9b840dba2cabc, limit: 10 requests/second
```

### 测试速率限制

可以使用提供的测试脚本验证功能：

```bash
cd apiserver
# 基础功能测试
node test-rate-limit.js

# Worker 限制分配测试
node test-worker-rate-limit.js

# 不同 Worker 数量场景测试
node test-worker-scenarios.js

# 动态时间窗口测试
node test-dynamic-window.js

# 跳过限速逻辑测试
node test-skip-rate-limit.js
```

## 注意事项

1. **内存使用**: 速率限制器使用内存存储，重启服务器会重置所有计数器
2. **Worker 分配**: 系统会自动将配置的限制按 worker 数量精确分配，确保总限制不超过配置值
3. **动态窗口**: 当每个 worker 限制 < 1 QPS 时，自动扩展时间窗口以实现精确控制
4. **性能影响**: 速率限制检查的性能开销很小，不会显著影响API响应时间
5. **配置更新**: 修改limit配置后需要重新发布API才能生效
6. **时间精度**: 动态时间窗口的精度取决于系统时钟，建议在生产环境中确保时间同步

## 最佳实践

1. **合理设置限制**: 根据API的实际使用情况和系统容量设置合适的限制
2. **监控使用情况**: 定期检查API使用情况，调整速率限制配置
3. **错误处理**: 客户端应该正确处理429错误，实现重试机制
4. **渐进式限制**: 对于新API，可以先设置较高的限制，然后根据使用情况逐步调整

## 故障排除

### 问题1: 速率限制不生效
- 检查API定义中是否正确设置了limit字段
- 确认limit值大于0
- 验证API是否已重新发布

### 问题2: 误报429错误
- 检查limit设置是否过低
- 确认时间窗口计算是否正确
- 查看系统日志确认实际请求频率

### 问题3: 性能问题
- 监控内存使用情况
- 检查清理机制是否正常工作
- 考虑调整清理间隔
