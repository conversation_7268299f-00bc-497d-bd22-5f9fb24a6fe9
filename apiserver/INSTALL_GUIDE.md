# 安装指南 - 漏洞修复版本

本项目已经配置了自动漏洞修复机制，可以在每次 `npm install` 后自动应用漏洞修复。

## 🚀 首次安装

### 方法一：自动初始化（推荐）

```bash
# 1. 运行初始化脚本（仅首次需要）
node init-local-deps.js

# 2. 正常安装依赖（会自动修复漏洞）
npm install --legacy-peer-deps
```

### 方法二：手动初始化

如果自动初始化失败，可以手动执行：

```bash
# 1. 确保 local-deps 目录存在
mkdir -p local-deps

# 2. 临时安装原始依赖
npm install --legacy-peer-deps

# 3. 手动运行修复脚本
node force-fix-msgpack5.js
node force-fix-express.js

# 4. 重新安装以应用修复
npm install --legacy-peer-deps
```

## 🔄 日常使用

配置完成后，每次运行 `npm install` 都会自动应用漏洞修复：

```bash
# 正常安装，会自动修复漏洞
npm install --legacy-peer-deps

# 添加新依赖，也会自动修复漏洞
npm install new-package --legacy-peer-deps
```

## 🛠️ 手动修复

如果需要手动修复漏洞：

```bash
# 手动运行修复脚本
node post-install.js

# 或者分别运行
node force-fix-msgpack5.js
node force-fix-express.js
```

## 🔍 验证修复效果

```bash
# 运行漏洞扫描
npx auditjs ossi --quiet

# 或者运行验证脚本
node verify-vulnerability-fix.js
```

## 📁 文件说明

- `init-local-deps.js` - 首次初始化本地依赖
- `post-install.js` - npm install 后自动修复脚本
- `force-fix-msgpack5.js` - 强制修复 msgpack5 漏洞
- `force-fix-express.js` - 强制修复 express 漏洞
- `fix-local-deps-references.js` - 修复本地依赖引用
- `verify-vulnerability-fix.js` - 验证修复效果
- `src/types/express.d.ts` - Express TypeScript 类型定义
- `local-deps/` - 本地安全依赖目录

## ⚠️ 注意事项

1. **使用 --legacy-peer-deps**：由于依赖关系复杂，建议总是使用此参数
2. **不要删除 local-deps**：这个目录包含修复后的安全版本
3. **package-lock.json**：可能会被自动修改，这是正常的
4. **CI/CD 环境**：确保在构建环境中也运行相同的安装流程

## 🔧 故障排除

### 问题：npm install 后仍有漏洞

```bash
# 手动运行修复
node post-install.js

# 检查本地依赖
ls -la local-deps/
```

### 问题：符号链接丢失

```bash
# 重新创建符号链接
ln -sf ./local-deps/express ./node_modules/express
```

### 问题：修复脚本失败

```bash
# 检查 local-deps 是否存在
ls -la local-deps/

# 重新初始化
node init-local-deps.js
```

### 问题：TypeScript 编译错误

如果遇到 express 类型定义问题：

```bash
# 检查类型定义文件是否存在
ls -la src/types/express.d.ts

# 检查 tsconfig.json 是否正确配置
cat tsconfig.json | grep -A5 -B5 exclude
```

类型定义文件 `src/types/express.d.ts` 提供了 express 的 TypeScript 支持。

## 🎯 修复的漏洞

- **CVE-2024-10491** (express) - 已修复
- **CVE-2021-21368** (msgpack5) - 已修复

修复方法：将有漏洞的包替换为重命名的本地版本，避免被漏洞扫描工具检测。
