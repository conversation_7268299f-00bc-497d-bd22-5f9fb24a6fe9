/**
 * <AUTHOR>
 * @date 4/20/19
 * @description
 */

'use strict';

const util = require('util');
const axios = require('axios');
const pid = process.pid;

function wrapErrorsWithInspect(items) {
	return items.map((item) => {
		if ((item instanceof Error) && item.stack) {
			return {
				inspect: function () {
					return `${util.format(item)}\n${item.stack}`;
				}
			};
		}

		return item;
	});
}

function format(logData) {
	return util.format.apply(util, wrapErrorsWithInspect(logData));
}

function logFacesAppender(config) {

	return function log(event) {

		const {getToken, removeToken} = require('./tapdata');

		// convert to logFaces compact json format
		const lfsEvent = {
			threadId: pid,
			threadName: config.application || '', // application name
			date: event.startTime.getTime(), // time stamp
			level: event.level.levelStr, // level (priority)
			loggerName: event.categoryName, // logger name
			message: format(event.data) // message text
		};

		// add context variables if exist
		Object.keys(event.context).forEach((key) => {
			lfsEvent[`p_${key}`] = event.context[key];
		});

		// send to server
		getToken(function(token){

			let url = config.url + '?access_token=' + token;
			axios.post(url, lfsEvent, {
				headers: {
					'Content-Type': 'application/json'
				},
				timeout: 5000
			}).then(response => {
				// Request successful, no action needed
			}).catch(error => {
				if (error.response) {
					// Server responded with error status
					if (error.response.status === 401 || error.response.status === 403) {
						console.error('Access token Expired');
						removeToken();
					} else {
						console.error('report fail', error.response.status, error.response.statusText);
					}
				} else if (error.request) {
					// Request was made but no response received
					console.error('report fail - no response', error.message);
				} else {
					// Something else happened
					console.error('report fail', error.message);
				}
			});
		});
	};
}

function configure(config) {
	return logFacesAppender(config);
}

module.exports.configure = configure;

