/**
 * 测试速率限制功能
 */

const { RateLimiter } = require('./dist/src/rate-limiter');

// 测试基本速率限制功能
function testBasicRateLimit() {
    console.log('Testing basic rate limit functionality...');

    const rateLimiter = RateLimiter.getInstance();
    const apiId = 'test-api-1';
    const config = { limit: 3 }; // 每秒3个请求

    // 前3个请求应该被允许
    for (let i = 1; i <= 3; i++) {
        const allowed = rateLimiter.checkRateLimit(apiId, config);
        console.log(`Request ${i}: ${allowed ? 'ALLOWED' : 'BLOCKED'}`);
        if (!allowed) {
            console.error(`Expected request ${i} to be allowed`);
            return false;
        }
    }

    // 第4个请求应该被阻止
    const fourthRequest = rateLimiter.checkRateLimit(apiId, config);
    console.log(`Request 4: ${fourthRequest ? 'ALLOWED' : 'BLOCKED'}`);
    if (fourthRequest) {
        console.error('Expected request 4 to be blocked');
        return false;
    }

    console.log('Basic rate limit test PASSED');
    return true;
}

// 测试时间窗口重置
function testTimeWindowReset() {
    console.log('\nTesting time window reset...');

    const rateLimiter = RateLimiter.getInstance();
    const apiId = 'test-api-2';
    const config = { limit: 2, windowMs: 100 }; // 每100ms 2个请求

    // 使用完限额
    rateLimiter.checkRateLimit(apiId, config);
    rateLimiter.checkRateLimit(apiId, config);

    // 第3个请求应该被阻止
    const blocked = rateLimiter.checkRateLimit(apiId, config);
    console.log(`Request 3 (immediate): ${blocked ? 'ALLOWED' : 'BLOCKED'}`);
    if (blocked) {
        console.error('Expected request 3 to be blocked');
        return false;
    }

    // 等待时间窗口重置
    return new Promise((resolve) => {
        setTimeout(() => {
            const allowed = rateLimiter.checkRateLimit(apiId, config);
            console.log(`Request after reset: ${allowed ? 'ALLOWED' : 'BLOCKED'}`);
            if (!allowed) {
                console.error('Expected request after reset to be allowed');
                resolve(false);
            } else {
                console.log('Time window reset test PASSED');
                resolve(true);
            }
        }, 150); // 等待超过窗口时间
    });
}

// 测试不同API的独立限制
function testIndependentApiLimits() {
    console.log('\nTesting independent API limits...');

    const rateLimiter = RateLimiter.getInstance();
    const config = { limit: 1 };

    // API 1 使用完限额
    const api1Allowed1 = rateLimiter.checkRateLimit('api-1', config);
    const api1Blocked = rateLimiter.checkRateLimit('api-1', config);

    // API 2 应该仍然可用
    const api2Allowed = rateLimiter.checkRateLimit('api-2', config);

    console.log(`API 1 first request: ${api1Allowed1 ? 'ALLOWED' : 'BLOCKED'}`);
    console.log(`API 1 second request: ${api1Blocked ? 'ALLOWED' : 'BLOCKED'}`);
    console.log(`API 2 first request: ${api2Allowed ? 'ALLOWED' : 'BLOCKED'}`);

    if (!api1Allowed1 || api1Blocked || !api2Allowed) {
        console.error('Independent API limits test FAILED');
        return false;
    }

    console.log('Independent API limits test PASSED');
    return true;
}

// 运行所有测试
async function runTests() {
    console.log('Starting rate limiter tests...\n');

    const test1 = testBasicRateLimit();
    const test2 = await testTimeWindowReset();
    const test3 = testIndependentApiLimits();

    if (test1 && test2 && test3) {
        console.log('\n✅ All tests PASSED!');
    } else {
        console.log('\n❌ Some tests FAILED!');
        process.exit(1);
    }
}

// 如果直接运行此文件
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = { runTests };
