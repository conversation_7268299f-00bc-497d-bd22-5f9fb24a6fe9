/**
 * 测试默认速率限制功能
 * 验证当没有配置limit时，默认为1 QPS
 */

const { RateLimiter } = require('./dist/src/rate-limiter');

// 模拟没有配置limit的API元数据
function simulateApiMetaWithoutLimit() {
    return {
        originalConfig: {
            _id: "test-api-no-limit",
            name: "test_api",
            // 注意：这里没有limit字段
            apiVersion: "v1",
            basePath: "test"
        }
    };
}

// 模拟配置了limit为0的API元数据
function simulateApiMetaWithZeroLimit() {
    return {
        originalConfig: {
            _id: "test-api-zero-limit",
            name: "test_api",
            limit: 0, // 显式设置为0
            apiVersion: "v1",
            basePath: "test"
        }
    };
}

// 模拟配置了limit为空字符串的API元数据
function simulateApiMetaWithEmptyLimit() {
    return {
        originalConfig: {
            _id: "test-api-empty-limit",
            name: "test_api",
            limit: "", // 空字符串
            apiVersion: "v1",
            basePath: "test"
        }
    };
}

// 测试默认限制行为
function testDefaultLimit() {
    console.log('Testing default limit behavior (should be 1 QPS)...');
    
    const rateLimiter = RateLimiter.getInstance();
    const apiMeta = simulateApiMetaWithoutLimit();
    const apiId = apiMeta.originalConfig._id;
    
    // 模拟sequence.ts中的逻辑
    const limit = parseInt(apiMeta.originalConfig.limit) || 1;
    console.log(`Parsed limit: ${limit}`);
    
    if (limit !== 1) {
        console.error('Expected default limit to be 1');
        return false;
    }
    
    // 第一个请求应该被允许
    const firstRequest = rateLimiter.checkRateLimit(apiId, { limit });
    console.log(`First request: ${firstRequest ? 'ALLOWED' : 'BLOCKED'}`);
    
    if (!firstRequest) {
        console.error('Expected first request to be allowed');
        return false;
    }
    
    // 第二个请求应该被阻止（因为默认限制是1 QPS）
    const secondRequest = rateLimiter.checkRateLimit(apiId, { limit });
    console.log(`Second request: ${secondRequest ? 'ALLOWED' : 'BLOCKED'}`);
    
    if (secondRequest) {
        console.error('Expected second request to be blocked with default limit of 1');
        return false;
    }
    
    console.log('Default limit test PASSED');
    return true;
}

// 测试零值限制行为
function testZeroLimit() {
    console.log('\nTesting zero limit behavior (should default to 1 QPS)...');
    
    const rateLimiter = RateLimiter.getInstance();
    const apiMeta = simulateApiMetaWithZeroLimit();
    const apiId = apiMeta.originalConfig._id;
    
    // 模拟sequence.ts中的逻辑
    const limit = parseInt(apiMeta.originalConfig.limit) || 1;
    console.log(`Parsed limit: ${limit}`);
    
    if (limit !== 1) {
        console.error('Expected zero limit to default to 1');
        return false;
    }
    
    // 第一个请求应该被允许
    const firstRequest = rateLimiter.checkRateLimit(apiId, { limit });
    console.log(`First request: ${firstRequest ? 'ALLOWED' : 'BLOCKED'}`);
    
    // 第二个请求应该被阻止
    const secondRequest = rateLimiter.checkRateLimit(apiId, { limit });
    console.log(`Second request: ${secondRequest ? 'ALLOWED' : 'BLOCKED'}`);
    
    if (!firstRequest || secondRequest) {
        console.error('Zero limit should behave same as default limit (1 QPS)');
        return false;
    }
    
    console.log('Zero limit test PASSED');
    return true;
}

// 测试空字符串限制行为
function testEmptyStringLimit() {
    console.log('\nTesting empty string limit behavior (should default to 1 QPS)...');
    
    const rateLimiter = RateLimiter.getInstance();
    const apiMeta = simulateApiMetaWithEmptyLimit();
    const apiId = apiMeta.originalConfig._id;
    
    // 模拟sequence.ts中的逻辑
    const limit = parseInt(apiMeta.originalConfig.limit) || 1;
    console.log(`Parsed limit: ${limit}`);
    
    if (limit !== 1) {
        console.error('Expected empty string limit to default to 1');
        return false;
    }
    
    console.log('Empty string limit test PASSED');
    return true;
}

// 测试各种边界情况
function testEdgeCases() {
    console.log('\nTesting edge cases...');
    
    const testCases = [
        { input: undefined, expected: 1, description: 'undefined' },
        { input: null, expected: 1, description: 'null' },
        { input: '', expected: 1, description: 'empty string' },
        { input: '0', expected: 1, description: 'string "0"' },
        { input: 0, expected: 1, description: 'number 0' },
        { input: '5', expected: 5, description: 'string "5"' },
        { input: 10, expected: 10, description: 'number 10' },
        { input: 'invalid', expected: 1, description: 'invalid string' }
    ];
    
    for (const testCase of testCases) {
        const result = parseInt(testCase.input) || 1;
        console.log(`${testCase.description}: ${testCase.input} -> ${result}`);
        
        if (result !== testCase.expected) {
            console.error(`Expected ${testCase.expected}, got ${result}`);
            return false;
        }
    }
    
    console.log('Edge cases test PASSED');
    return true;
}

// 运行所有测试
function runTests() {
    console.log('Starting default limit tests...\n');
    
    const test1 = testDefaultLimit();
    const test2 = testZeroLimit();
    const test3 = testEmptyStringLimit();
    const test4 = testEdgeCases();
    
    if (test1 && test2 && test3 && test4) {
        console.log('\n✅ All default limit tests PASSED!');
        console.log('\n📋 Summary:');
        console.log('  • 没有配置limit字段 -> 默认1 QPS');
        console.log('  • limit设置为0 -> 默认1 QPS');
        console.log('  • limit设置为空字符串 -> 默认1 QPS');
        console.log('  • limit设置为无效值 -> 默认1 QPS');
        console.log('  • limit设置为有效数字 -> 使用配置值');
    } else {
        console.log('\n❌ Some default limit tests FAILED!');
        process.exit(1);
    }
}

// 如果直接运行此文件
if (require.main === module) {
    runTests();
}

module.exports = { runTests };
