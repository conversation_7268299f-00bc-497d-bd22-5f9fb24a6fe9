/**
 * 速率限制功能演示脚本
 *
 * 此脚本演示如何使用新的速率限制功能
 */

const { RateLimiter } = require('./dist/src/rate-limiter');

console.log('🚀 API 速率限制功能演示\n');

// 创建速率限制器实例
const rateLimiter = RateLimiter.getInstance();

// 演示场景0: 默认限制行为
console.log('📊 场景0: 默认限制行为 (没有配置limit，默认1 QPS)');
console.log('=' .repeat(50));

// 模拟没有配置limit的API
const apiConfigDefault = {}; // 没有limit字段
const apiIdDefault = 'demo-api-default';

for (let i = 1; i <= 3; i++) {
    // 模拟sequence.ts中的逻辑：parseInt(undefined) || 1 = 1
    const limit = parseInt(apiConfigDefault.limit) || 1;
    const allowed = rateLimiter.checkRateLimit(apiIdDefault, { limit });
    const status = allowed ? '✅ 允许' : '❌ 被限制';

    console.log(`请求 ${i}: ${status} (默认限制: ${limit} QPS)`);

    if (!allowed) {
        console.log('   💡 返回 HTTP 429 - Too Many Requests');
        console.log('   📝 错误信息: Rate limit exceeded. Maximum 1 requests per second allowed.');
    }
}

console.log('\n');

// 演示场景1: 正常使用情况
console.log('📊 场景1: 正常使用情况 (限制: 5 QPS)');
console.log('=' .repeat(50));

const apiConfig1 = { limit: 5 };
const apiId1 = 'demo-api-1';

for (let i = 1; i <= 7; i++) {
    const allowed = rateLimiter.checkRateLimit(apiId1, apiConfig1);
    const status = allowed ? '✅ 允许' : '❌ 被限制';
    const remaining = rateLimiter.getRemainingRequests(apiId1, apiConfig1);

    console.log(`请求 ${i}: ${status} (剩余: ${remaining})`);

    if (!allowed) {
        console.log('   💡 返回 HTTP 429 - Too Many Requests');
        console.log('   📝 错误信息: Rate limit exceeded. Maximum 5 requests per second allowed.');
    }
}

console.log('\n');

// 演示场景2: 不同API的独立限制
console.log('📊 场景2: 不同API的独立限制');
console.log('=' .repeat(50));

const apiConfig2 = { limit: 3 };
const apiConfig3 = { limit: 2 };

console.log('API-A (限制: 3 QPS):');
for (let i = 1; i <= 4; i++) {
    const allowed = rateLimiter.checkRateLimit('api-a', apiConfig2);
    const status = allowed ? '✅ 允许' : '❌ 被限制';
    console.log(`  请求 ${i}: ${status}`);
}

console.log('\nAPI-B (限制: 2 QPS):');
for (let i = 1; i <= 3; i++) {
    const allowed = rateLimiter.checkRateLimit('api-b', apiConfig3);
    const status = allowed ? '✅ 允许' : '❌ 被限制';
    console.log(`  请求 ${i}: ${status}`);
}

console.log('\n');

// 演示场景3: 时间窗口重置
console.log('📊 场景3: 时间窗口重置演示');
console.log('=' .repeat(50));

const apiConfig4 = { limit: 2, windowMs: 1000 };
const apiId4 = 'demo-api-reset';

console.log('初始请求:');
for (let i = 1; i <= 3; i++) {
    const allowed = rateLimiter.checkRateLimit(apiId4, apiConfig4);
    const status = allowed ? '✅ 允许' : '❌ 被限制';
    console.log(`  请求 ${i}: ${status}`);
}

console.log('\n⏰ 等待时间窗口重置 (1秒)...');

setTimeout(() => {
    console.log('\n时间窗口重置后:');
    for (let i = 1; i <= 2; i++) {
        const allowed = rateLimiter.checkRateLimit(apiId4, apiConfig4);
        const status = allowed ? '✅ 允许' : '❌ 被限制';
        console.log(`  请求 ${i}: ${status}`);
    }

    console.log('\n');

    // 演示场景4: 实际API配置示例
    console.log('📊 场景4: 实际API配置示例');
    console.log('=' .repeat(50));

    const exampleApiConfig = {
        "_id": "683761974cf9b840dba2cabc",
        "name": "heartbeat_api",
        "limit": 10,
        "apiVersion": "v1",
        "basePath": "x9thfsd41vy"
    };

    console.log('API配置示例:');
    console.log(JSON.stringify({
        name: exampleApiConfig.name,
        limit: exampleApiConfig.limit,
        basePath: exampleApiConfig.basePath
    }, null, 2));

    console.log('\n模拟请求处理:');

    // 模拟快速连续请求
    for (let i = 1; i <= 12; i++) {
        const allowed = rateLimiter.checkRateLimit(
            exampleApiConfig._id,
            { limit: exampleApiConfig.limit }
        );

        if (allowed) {
            console.log(`✅ 请求 ${i}: 成功处理`);
        } else {
            console.log(`❌ 请求 ${i}: 被限制 - 返回 HTTP 429`);
            console.log(`   📄 响应: {"error": {"statusCode": 429, "message": "Rate limit exceeded. Maximum ${exampleApiConfig.limit} requests per second allowed."}}`);
        }
    }

    console.log('\n');

    // 总结
    console.log('📋 功能总结');
    console.log('=' .repeat(50));
    console.log('✨ 已实现的功能:');
    console.log('  • 每秒请求数限制 (QPS)');
    console.log('  • 独立的API限制计数器');
    console.log('  • 自动时间窗口重置');
    console.log('  • HTTP 429 错误响应');
    console.log('  • 内存高效存储');
    console.log('  • 自动清理过期记录');

    console.log('\n🔧 使用方法:');
    console.log('  1. 在API定义JSON中添加 "limit": 10');
    console.log('  2. 重新发布API配置');
    console.log('  3. 客户端处理429错误响应');

    console.log('\n🎯 适用场景:');
    console.log('  • 防止API滥用');
    console.log('  • 保护系统资源');
    console.log('  • 确保服务质量');
    console.log('  • 公平使用控制');

    console.log('\n✅ 演示完成！');

}, 1100);

// 演示错误创建
console.log('📊 错误响应示例');
console.log('=' .repeat(50));

const error = rateLimiter.createRateLimitError(10);
console.log('HTTP状态码:', error.statusCode);
console.log('错误名称:', error.name);
console.log('错误信息:', error.message);
console.log('重试时间:', error.retryAfter, '秒');

console.log('\n');
