#!/usr/bin/env node

/**
 * 最终速率限制功能测试脚本
 * 
 * 测试场景：
 * 1. 验证默认1 QPS限制
 * 2. 验证HTTP 429错误响应
 * 3. 验证错误信息格式
 */

const http = require('http');

console.log('🎯 最终速率限制功能测试');
console.log('=' .repeat(50));

// 测试配置
const API_URL = 'http://127.0.0.1:13080/api/v1/xxx/x9thfsd41vy';
const TEST_COUNT = 5;

function makeRequest(url) {
    return new Promise((resolve, reject) => {
        const startTime = Date.now();
        
        const req = http.get(url, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                try {
                    const jsonData = JSON.parse(data);
                    resolve({
                        statusCode: res.statusCode,
                        responseTime,
                        data: jsonData,
                        headers: res.headers
                    });
                } catch (e) {
                    resolve({
                        statusCode: res.statusCode,
                        responseTime,
                        data: data,
                        headers: res.headers
                    });
                }
            });
        });
        
        req.on('error', (err) => {
            reject(err);
        });
        
        req.setTimeout(5000, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
    });
}

async function runTest() {
    console.log(`📡 测试API: ${API_URL}`);
    console.log(`🔄 发送 ${TEST_COUNT} 个连续请求...\n`);
    
    const results = [];
    
    for (let i = 1; i <= TEST_COUNT; i++) {
        try {
            console.log(`请求 ${i}:`);
            const result = await makeRequest(API_URL);
            
            results.push(result);
            
            // 分析结果
            if (result.statusCode === 200) {
                console.log(`  ✅ 成功 - HTTP ${result.statusCode} (${result.responseTime}ms)`);
                if (result.data && result.data.data) {
                    console.log(`  📊 返回 ${result.data.data.length} 条记录`);
                }
            } else if (result.statusCode === 429) {
                console.log(`  🚫 被限制 - HTTP ${result.statusCode} (${result.responseTime}ms)`);
                if (result.data && result.data.error) {
                    console.log(`  💬 错误信息: ${result.data.error.message}`);
                    console.log(`  🏷️  错误名称: ${result.data.error.name}`);
                }
            } else {
                console.log(`  ❓ 其他状态 - HTTP ${result.statusCode} (${result.responseTime}ms)`);
            }
            
            // 短暂延迟，避免请求过快
            if (i < TEST_COUNT) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
        } catch (error) {
            console.log(`  ❌ 错误: ${error.message}`);
            results.push({ error: error.message });
        }
        
        console.log('');
    }
    
    // 分析测试结果
    console.log('📈 测试结果分析:');
    console.log('=' .repeat(30));
    
    const successCount = results.filter(r => r.statusCode === 200).length;
    const rateLimitedCount = results.filter(r => r.statusCode === 429).length;
    const errorCount = results.filter(r => r.error).length;
    
    console.log(`✅ 成功请求: ${successCount}`);
    console.log(`🚫 被限制请求: ${rateLimitedCount}`);
    console.log(`❌ 错误请求: ${errorCount}`);
    
    // 验证速率限制是否正常工作
    console.log('\n🔍 功能验证:');
    
    if (successCount >= 1) {
        console.log('✅ API正常响应 - 通过');
    } else {
        console.log('❌ API无法正常响应 - 失败');
    }
    
    if (rateLimitedCount >= 1) {
        console.log('✅ 速率限制生效 - 通过');
    } else {
        console.log('❌ 速率限制未生效 - 失败');
    }
    
    // 检查429错误格式
    const rateLimitedResponse = results.find(r => r.statusCode === 429);
    if (rateLimitedResponse && rateLimitedResponse.data && rateLimitedResponse.data.error) {
        const error = rateLimitedResponse.data.error;
        if (error.statusCode === 429 && 
            error.name === 'TooManyRequestsError' && 
            error.message.includes('Rate limit exceeded')) {
            console.log('✅ 错误响应格式正确 - 通过');
        } else {
            console.log('❌ 错误响应格式不正确 - 失败');
        }
    }
    
    console.log('\n🎉 测试完成！');
    
    // 如果大部分功能正常，显示成功信息
    if (successCount >= 1 && rateLimitedCount >= 1) {
        console.log('\n🎊 速率限制功能正常工作！');
        console.log('📋 功能特点:');
        console.log('   • 默认1 QPS限制');
        console.log('   • HTTP 429状态码');
        console.log('   • 标准错误信息格式');
        console.log('   • 每个API独立限制');
    }
}

// 运行测试
runTest().catch(console.error);
