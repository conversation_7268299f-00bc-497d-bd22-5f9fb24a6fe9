/**
 * 测试限速值 <= 0 时跳过限速逻辑的功能
 * 验证当 limit 为 0 或负数时，不进行限速检查
 */

const { RateLimiter } = require('./dist/src/rate-limiter');

console.log('🚀 测试限速值 <= 0 时跳过限速逻辑\n');

// 创建限速器实例
const rateLimiter = RateLimiter.getInstance();

// 测试场景配置
const testScenarios = [
    {
        limit: 0,
        description: "限速值为 0 (禁用限速)",
        shouldSkip: true
    },
    {
        limit: -1,
        description: "限速值为 -1 (负数)",
        shouldSkip: true
    },
    {
        limit: -10,
        description: "限速值为 -10 (负数)",
        shouldSkip: true
    },
    {
        limit: 1,
        description: "限速值为 1 (正常限速)",
        shouldSkip: false
    },
    {
        limit: 5,
        description: "限速值为 5 (正常限速)",
        shouldSkip: false
    }
];

console.log('📊 限速跳过逻辑测试');
console.log('=' .repeat(70));
console.log('| 限速值 | 描述                    | 应该跳过 | 测试结果 |');
console.log('|--------|-------------------------|----------|----------|');

testScenarios.forEach(scenario => {
    const { limit, description, shouldSkip } = scenario;

    // 模拟 sequence.ts 中的正确逻辑
    const parsedLimit = parseInt(limit);
    const configuredLimit = isNaN(parsedLimit) ? 1 : parsedLimit;
    const willSkip = configuredLimit <= 0;

    const result = willSkip === shouldSkip ? '✅ 正确' : '❌ 错误';

    console.log(`| ${limit.toString().padEnd(6)} | ${description.padEnd(23)} | ${shouldSkip ? '是      ' : '否      '} | ${result.padEnd(8)} |`);
});

console.log('\n🧪 详细测试场景');
console.log('-' .repeat(50));

async function testSkipLogic(limit, description) {
    console.log(`\n📋 ${description}`);
    console.log(`   配置限速值: ${limit}`);

    // 模拟 sequence.ts 中的处理逻辑
    const parsedLimit = parseInt(limit);
    const configuredLimit = isNaN(parsedLimit) ? 1 : parsedLimit;
    console.log(`   解析后限速值: ${configuredLimit}`);

    if (configuredLimit <= 0) {
        console.log('   ✅ 跳过限速检查 - 限速已禁用');
        console.log('   📝 日志: Rate limiting disabled for API test-api');
        return true; // 表示跳过了限速
    } else {
        console.log('   🔍 执行限速检查');

        // 执行实际的限速检查
        const apiId = `test-api-${limit}`;
        const limitInfo = rateLimiter.getLimitCalculationInfo(configuredLimit);
        console.log(`   📊 限速信息: ${limitInfo.description}`);

        // 测试多个请求
        let allowedCount = 0;
        let blockedCount = 0;

        for (let i = 1; i <= configuredLimit + 2; i++) {
            const isAllowed = rateLimiter.checkRateLimit(apiId, { limit: configuredLimit });
            if (isAllowed) {
                allowedCount++;
            } else {
                blockedCount++;
            }
        }

        console.log(`   📈 测试结果: 允许 ${allowedCount} 个请求, 限制 ${blockedCount} 个请求`);
        return false; // 表示执行了限速
    }
}

// 运行详细测试
async function runDetailedTests() {
    for (const scenario of testScenarios) {
        const skipped = await testSkipLogic(scenario.limit, scenario.description);

        if (skipped === scenario.shouldSkip) {
            console.log('   ✅ 测试通过');
        } else {
            console.log('   ❌ 测试失败');
        }
    }
}

runDetailedTests().then(() => {
    console.log('\n🎉 所有测试完成!');

    console.log('\n📝 实现逻辑总结:');
    console.log('```typescript');
    console.log('const parsedLimit = parseInt(originalConfig.limit);');
    console.log('const configuredLimit = isNaN(parsedLimit) ? 1 : parsedLimit;');
    console.log('');
    console.log('if (configuredLimit <= 0) {');
    console.log('    log.app.debug(`Rate limiting disabled for API ${apiId}, limit: ${configuredLimit}`);');
    console.log('} else {');
    console.log('    // 执行正常的限速检查逻辑');
    console.log('    const isAllowed = rateLimiter.checkRateLimit(apiId, { limit: configuredLimit });');
    console.log('    // ...');
    console.log('}');
    console.log('```');

    console.log('\n🎯 功能特点:');
    console.log('✅ 当 limit = 0 时: 完全禁用限速');
    console.log('✅ 当 limit < 0 时: 完全禁用限速');
    console.log('✅ 当 limit > 0 时: 正常执行限速检查');
    console.log('✅ 当 limit 未配置时: 默认使用 1 QPS');

    console.log('\n🔧 使用场景:');
    console.log('1. 开发环境: 设置 limit = 0 禁用限速');
    console.log('2. 特殊 API: 设置 limit = -1 表示无限制');
    console.log('3. 临时禁用: 快速禁用某个 API 的限速');
    console.log('4. 测试环境: 避免限速影响自动化测试');

    console.log('\n📊 性能优势:');
    console.log('- 跳过限速检查可以减少不必要的计算开销');
    console.log('- 避免创建和维护限速记录');
    console.log('- 提高 API 响应速度');
}).catch(console.error);
