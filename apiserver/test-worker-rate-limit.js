/**
 * 测试 Worker 限速功能
 * 验证限速是否正确按 worker 数量分配
 */

const { RateLimiter } = require('./dist/src/rate-limiter');

console.log('🚀 Worker 限速功能测试\n');

// 创建速率限制器实例
const rateLimiter = RateLimiter.getInstance();

// 获取 worker 信息
const workerInfo = rateLimiter.getWorkerInfo();
console.log('📊 Worker 信息:');
console.log(`  Worker 数量: ${workerInfo.workerCount}`);
console.log(`  是否为 Worker 进程: ${workerInfo.isWorker}`);
console.log(`  进程 ID: ${workerInfo.processId}`);
console.log('');

// 测试场景1: 配置限制为 10，worker 数量为 4，每个 worker 应该得到 2.5 -> 2 的限制
console.log('📊 场景1: 测试限制分配逻辑');
console.log('=' .repeat(50));

const testConfigs = [
    { configured: 10, workers: 4, expected: 2 },
    { configured: 10, workers: 1, expected: 10 },
    { configured: 5, workers: 8, expected: 1 },  // 5/8 = 0.625 -> 1 (最小值)
    { configured: 1, workers: 4, expected: 1 },  // 1/4 = 0.25 -> 1 (最小值)
];

testConfigs.forEach((config, index) => {
    // 模拟不同的 worker 数量
    const testRateLimiter = new (require('./dist/src/rate-limiter').RateLimiter)();
    // 手动设置 worker 数量进行测试
    testRateLimiter.workerCount = config.workers;
    
    const effectiveLimit = testRateLimiter.calculateWorkerLimit(config.configured);
    const status = effectiveLimit === config.expected ? '✅' : '❌';
    
    console.log(`测试 ${index + 1}: ${status}`);
    console.log(`  配置限制: ${config.configured} QPS`);
    console.log(`  Worker 数量: ${config.workers}`);
    console.log(`  期望每个 Worker 限制: ${config.expected}`);
    console.log(`  实际每个 Worker 限制: ${effectiveLimit}`);
    console.log('');
});

// 测试场景2: 实际限速测试
console.log('📊 场景2: 实际限速测试');
console.log('=' .repeat(50));

const apiId = 'test-worker-api';
const configuredLimit = 10; // 配置 10 QPS

console.log(`配置限制: ${configuredLimit} QPS`);
console.log(`当前 Worker 数量: ${workerInfo.workerCount}`);

// 计算期望的每个 worker 限制
const expectedWorkerLimit = Math.max(1, Math.floor(configuredLimit / workerInfo.workerCount));
console.log(`期望每个 Worker 限制: ${expectedWorkerLimit} QPS`);
console.log('');

// 测试请求
console.log('发送测试请求:');
let allowedCount = 0;
let blockedCount = 0;

for (let i = 1; i <= configuredLimit + 5; i++) {
    const isAllowed = rateLimiter.checkRateLimit(apiId, { limit: configuredLimit });
    const status = isAllowed ? '✅ 允许' : '❌ 被限制';
    
    if (isAllowed) {
        allowedCount++;
    } else {
        blockedCount++;
    }
    
    console.log(`请求 ${i}: ${status}`);
    
    // 如果连续被限制，提前结束测试
    if (blockedCount >= 3) {
        console.log('连续被限制，结束测试');
        break;
    }
}

console.log('');
console.log('📈 测试结果统计:');
console.log(`  允许的请求: ${allowedCount}`);
console.log(`  被限制的请求: ${blockedCount}`);
console.log(`  期望允许的请求: ${expectedWorkerLimit}`);

if (allowedCount === expectedWorkerLimit) {
    console.log('✅ 测试通过: 实际限制与期望一致');
} else {
    console.log('❌ 测试失败: 实际限制与期望不一致');
}

console.log('');

// 测试场景3: 时间窗口重置测试
console.log('📊 场景3: 时间窗口重置测试');
console.log('=' .repeat(50));

const resetApiId = 'test-reset-api';
const resetConfig = { limit: 6, windowMs: 1000 }; // 6 QPS, 1秒窗口

console.log('使用完当前时间窗口的限额...');
let resetAllowedCount = 0;
for (let i = 1; i <= 10; i++) {
    const isAllowed = rateLimiter.checkRateLimit(resetApiId, resetConfig);
    if (isAllowed) {
        resetAllowedCount++;
        console.log(`请求 ${i}: ✅ 允许`);
    } else {
        console.log(`请求 ${i}: ❌ 被限制`);
        break;
    }
}

console.log(`在时间窗口内允许了 ${resetAllowedCount} 个请求`);

// 等待时间窗口重置
console.log('⏰ 等待时间窗口重置 (1.1秒)...');
setTimeout(() => {
    console.log('时间窗口重置后测试:');
    
    const isAllowedAfterReset = rateLimiter.checkRateLimit(resetApiId, resetConfig);
    if (isAllowedAfterReset) {
        console.log('✅ 时间窗口重置后第一个请求被允许');
    } else {
        console.log('❌ 时间窗口重置后第一个请求仍被限制');
    }
    
    console.log('\n🎉 Worker 限速功能测试完成!');
}, 1100);
