var mongoAtlasConnection = {
    "name": "mongo-atlas",
    "connection_type": "target",
    "database_type": "mongodb",
    "database_host": "",
    "database_username": "",
    "database_port": 0,
    "database_uri": "mongodb+srv://tapdata:<EMAIL>/sample_airbnb?retryWrites=true&w=majority",
    "database_name": "",
    "database_password": "",
    "retry": 0,
    "nextRetry": null,
    "user_id": "5cb045769edfcdb92d295daf",
    "ssl": false,
    "fill": "uri",
    "plain_password": "",
    "auth_db": "",
    "schema": {
        "tables": [
            {
                "table_name": "listingsAndReviews",
                "fields": [
                    {
                        "field_name": "_id",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 1,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": "PRI",
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "listing_url",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "name",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "summary",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "space",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "description",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "neighborhood_overview",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "notes",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "transit",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "access",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "interaction",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "house_rules",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "property_type",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "room_type",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "bed_type",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "minimum_nights",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "maximum_nights",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "cancellation_policy",
                        "table_name": "listingsAndReviews",
                        "data_type": "String",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "last_scraped",
                        "table_name": "listingsAndReviews",
                        "data_type": "Date",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "calendar_last_scraped",
                        "table_name": "listingsAndReviews",
                        "data_type": "Date",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "first_review",
                        "table_name": "listingsAndReviews",
                        "data_type": "Date",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "last_review",
                        "table_name": "listingsAndReviews",
                        "data_type": "Date",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "accommodates",
                        "table_name": "listingsAndReviews",
                        "data_type": "Integer",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "bedrooms",
                        "table_name": "listingsAndReviews",
                        "data_type": "Integer",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "beds",
                        "table_name": "listingsAndReviews",
                        "data_type": "Integer",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "number_of_reviews",
                        "table_name": "listingsAndReviews",
                        "data_type": "Integer",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "bathrooms",
                        "table_name": "listingsAndReviews",
                        "data_type": "Decimal128",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "amenities",
                        "table_name": "listingsAndReviews",
                        "data_type": "ArrayList",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "price",
                        "table_name": "listingsAndReviews",
                        "data_type": "Decimal128",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "weekly_price",
                        "table_name": "listingsAndReviews",
                        "data_type": "Decimal128",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "monthly_price",
                        "table_name": "listingsAndReviews",
                        "data_type": "Decimal128",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "security_deposit",
                        "table_name": "listingsAndReviews",
                        "data_type": "Decimal128",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "cleaning_fee",
                        "table_name": "listingsAndReviews",
                        "data_type": "Decimal128",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "extra_people",
                        "table_name": "listingsAndReviews",
                        "data_type": "Decimal128",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "guests_included",
                        "table_name": "listingsAndReviews",
                        "data_type": "Decimal128",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "images",
                        "table_name": "listingsAndReviews",
                        "data_type": "Document",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "host",
                        "table_name": "listingsAndReviews",
                        "data_type": "Document",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "address",
                        "table_name": "listingsAndReviews",
                        "data_type": "Document",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "availability",
                        "table_name": "listingsAndReviews",
                        "data_type": "Document",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "review_scores",
                        "table_name": "listingsAndReviews",
                        "data_type": "Document",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    },
                    {
                        "field_name": "reviews",
                        "table_name": "listingsAndReviews",
                        "data_type": "ArrayList",
                        "primary_key_position": 0,
                        "foreign_key_table": null,
                        "foreign_key_column": null,
                        "key": null,
                        "dataType": 0,
                        "is_nullable": null,
                        "precision": null,
                        "scale": null,
                        "default_value": null
                    }
                ],
                "cdc_enabled": null,
                "meta_data": null,
                "tableId": null
            }
        ]
    },
    "status": "ready",
    "id": ObjectId("5cf3e4317930cd42aec0a2d2"),
    "db_version": 4
};

var apiModule = {
    "_id" : ObjectId("5cf499f57930cd42aec0dfb5"),
    "datasource" : "5cf3e4317930cd42aec0a2d2",
    "tablename" : "listingsAndReviews",
    "apiVersion" : "v1",
    "basePath" : "listingsAndReviews",
    "description" : "",
    "status" : "active",
    "paths" : [
        {
            "path" : "/api/v1/listingsAndReviews",
            "method" : "POST",
            "description" : "create a new record",
            "name" : "create",
            "result" : "Document",
            "type" : "preset",
            "acl" : [
                "5b9a0a383fcba02649524bf1"
            ]
        },
        {
            "path" : "/api/v1/listingsAndReviews/{id}",
            "method" : "GET",
            "description" : "get record by id",
            "name" : "findById",
            "params" : [
                {
                    "name" : "id",
                    "type" : "string",
                    "defaultvalue" : 1,
                    "description" : "document id"
                }
            ],
            "result" : "Document",
            "type" : "preset",
            "acl" : [
                "5b9a0a383fcba02649524bf1"
            ]
        },
        {
            "path" : "/api/v1/listingsAndReviews/{id}",
            "method" : "PATCH",
            "name" : "updateById",
            "params" : [
                {
                    "name" : "id",
                    "type" : "string",
                    "defaultvalue" : 1,
                    "description" : "document id"
                }
            ],
            "description" : "update record by id",
            "result" : "Document",
            "type" : "preset",
            "acl" : [
                "5b9a0a383fcba02649524bf1"
            ]
        },
        {
            "path" : "/api/v1/listingsAndReviews/{id}",
            "method" : "DELETE",
            "name" : "deleteById",
            "params" : [
                {
                    "name" : "id",
                    "type" : "string",
                    "description" : "document id"
                }
            ],
            "description" : "delete record by id",
            "type" : "preset",
            "acl" : [
                "5b9a0a383fcba02649524bf1"
            ]
        },
        {
            "path" : "/api/v1/listingsAndReviews",
            "method" : "GET",
            "name" : "findPage",
            "params" : [
                {
                    "name" : "page",
                    "type" : "int",
                    "defaultvalue" : 1,
                    "description" : "page number"
                },
                {
                    "name" : "limit",
                    "type" : "int",
                    "defaultvalue" : 20,
                    "description" : "max records per page"
                },
                {
                    "name" : "sort",
                    "type" : "object",
                    "description" : "sort setting,Array ,format like [{'propertyName':'ASC'}]"
                },
                {
                    "name" : "filter",
                    "type" : "object",
                    "description" : "search filter object,Array"
                }
            ],
            "description" : "get record list by page and limit",
            "result" : "Page<Document>",
            "type" : "preset",
            "acl" : [
                "5b9a0a383fcba02649524bf1"
            ]
        }
    ],
    "user_id" : "5cb045769edfcdb92d295daf",
    "fields" : [
        {
            "field_name" : "_id",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 1,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : "PRI",
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "listing_url",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "name",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "summary",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "space",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "description",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "neighborhood_overview",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "notes",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "transit",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "access",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "interaction",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "house_rules",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "property_type",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "room_type",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "bed_type",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "minimum_nights",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "maximum_nights",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "cancellation_policy",
            "table_name" : "listingsAndReviews",
            "data_type" : "String",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "last_scraped",
            "table_name" : "listingsAndReviews",
            "data_type" : "Date",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "calendar_last_scraped",
            "table_name" : "listingsAndReviews",
            "data_type" : "Date",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "first_review",
            "table_name" : "listingsAndReviews",
            "data_type" : "Date",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "last_review",
            "table_name" : "listingsAndReviews",
            "data_type" : "Date",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "accommodates",
            "table_name" : "listingsAndReviews",
            "data_type" : "Integer",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "bedrooms",
            "table_name" : "listingsAndReviews",
            "data_type" : "Integer",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "beds",
            "table_name" : "listingsAndReviews",
            "data_type" : "Integer",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "number_of_reviews",
            "table_name" : "listingsAndReviews",
            "data_type" : "Integer",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "bathrooms",
            "table_name" : "listingsAndReviews",
            "data_type" : "Decimal128",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "amenities",
            "table_name" : "listingsAndReviews",
            "data_type" : "ArrayList",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "price",
            "table_name" : "listingsAndReviews",
            "data_type" : "Decimal128",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "weekly_price",
            "table_name" : "listingsAndReviews",
            "data_type" : "Decimal128",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "monthly_price",
            "table_name" : "listingsAndReviews",
            "data_type" : "Decimal128",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "security_deposit",
            "table_name" : "listingsAndReviews",
            "data_type" : "Decimal128",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "cleaning_fee",
            "table_name" : "listingsAndReviews",
            "data_type" : "Decimal128",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "extra_people",
            "table_name" : "listingsAndReviews",
            "data_type" : "Decimal128",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "guests_included",
            "table_name" : "listingsAndReviews",
            "data_type" : "Decimal128",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "images",
            "table_name" : "listingsAndReviews",
            "data_type" : "Document",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "host",
            "table_name" : "listingsAndReviews",
            "data_type" : "Document",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "address",
            "table_name" : "listingsAndReviews",
            "data_type" : "Document",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "availability",
            "table_name" : "listingsAndReviews",
            "data_type" : "Document",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "review_scores",
            "table_name" : "listingsAndReviews",
            "data_type" : "Document",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        },
        {
            "field_name" : "reviews",
            "table_name" : "listingsAndReviews",
            "data_type" : "ArrayList",
            "primary_key_position" : 0,
            "foreign_key_table" : null,
            "foreign_key_column" : null,
            "key" : null,
            "dataType" : 0,
            "is_nullable" : null,
            "precision" : null,
            "scale" : null,
            "default_value" : null
        }
    ],
    "connection" : ObjectId("5cf3e4317930cd42aec0a2d2"),
    "id" : ObjectId("5cf499f57930cd42aec0dfb5")
}

function autoInitialUser() {

    var userWatch = db.user.watch([{
        $match: {
            operationType: "insert"
        }
    }], {fullDocument: "updateLookup"});

    print("Start listening create user.");
    while (true) {

        try {
            if (userWatch.hasNext()) {
                var changeEvent = userWatch.next();
                var userDoc = changeEvent.fullDocument;
                print("Found new user " + JSON.stringify(userDoc));

                mongoAtlasConnection._id = new ObjectId();
                mongoAtlasConnection.id = mongoAtlasConnection._id;
                mongoAtlasConnection.user_id = userDoc._id.valueOf();

                db.Connections.insert(mongoAtlasConnection);

                apiModule._id = new ObjectId();
                apiModule.id = apiModule._id;
                apiModule.datasource = mongoAtlasConnection._id.valueOf();
                apiModule.connection = mongoAtlasConnection._id;
                apiModule.user_id = userDoc._id.valueOf();
                db.Modules.insert(apiModule);
            } else {
                sleep(300);
            }
        } catch (e) {
            print("Auto initial new user connection or api module failed " + e);
        } finally {
            if (userWatch.isExhausted()) {
                userWatch = db.user.watch([{
                    $match: {
                        operationType: "insert"
                    }
                }], {fullDocument: "updateLookup"});
            }
        }
    }
}

autoInitialUser();