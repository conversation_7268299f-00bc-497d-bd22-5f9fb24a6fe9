const reportApiCallStats = require("./reportApiCallStats");
const log = require('./dist').log.audit;
const Conf = require('conf');
const config = new Conf();
let timer;
const systemFields = ["limit", "page", "filter.fields", "filter.sort", "filter.order", "sort", "order"];

process.on('message', function(msg){
  if(msg === 'ping'){
    if(timer){
      try {
        clearTimeout(timer);
        timer = null;
      }catch (e) {}
    }
  }else if(msg.type === 'start'){
    reportApiCallStats.start();
  }else if(msg.type === 'stop'){
    reportApiCallStats.stop();
  }else if(msg.type === 'apiCell'){
    if (config.get("audioLogEncryption")) {
      const printData = JSON.parse(JSON.stringify(msg.data))
      printData.query = parseIfNeed(printData.query);
      printData.body = parseIfNeed(printData.body);
      printData.req_params = "[******]";
      log.info(JSON.stringify(printData));
    } else {
      log.info(JSON.stringify(msg.data));
    }
    reportApiCallStats.put(msg.data);
  }
});

function parseIfNeed(json) {
  if (!json) {
    return json;
  }
  try {
    return JSON.stringify(encryptionIfNeed(JSON.parse(json)));
  } catch (e) {
    return json;
  }
}

function encryptionIfNeed(data, parent) {
  if (Array.isArray(data)) {
    return data.map(v => encryptionIfNeed(v, parent));
  } else if (data !== null && typeof data === 'object') {
    Object.keys(data).forEach(key => {
      const fullPath = parent ? `${parent}.${key}` : key;
      if (systemFields.indexOf(fullPath) !== -1) {
        return;
      }
      let value = data[key];
      if (Array.isArray(value)) {
        data[key] = value.map(v => encryptionIfNeed(v, fullPath));
      } else if (value !== null && typeof value === 'object') {
        data[key] = encryptionIfNeed(value, fullPath);
      } else if (typeof value === 'string') {
        data[key] = parseAsJson(value, fullPath);
      } else {
        data[key] = '******';
      }
    })
    return data;
  } else {
    return '******';
  }
}

function parseAsJson(body, path) {
  let jsonBody = null;
  try {
    jsonBody = JSON.parse(body);
  } catch (e) {
    return '******';
  }
  return encryptionIfNeed(jsonBody, path);
}

setInterval(()=>{
  try {
    process.send('ping');
  }catch (e) {
    reportApiCallStats.stop(true);
  }
  timer = setTimeout(()=>{
    reportApiCallStats.stop(true);
  },5000);
},10000)
