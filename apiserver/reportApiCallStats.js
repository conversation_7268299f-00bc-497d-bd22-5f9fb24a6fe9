const tapdata = require('./tapdata');
const axios = require('axios');
const Conf = require('conf');
const config = new Conf();

let apiCellCache = [];
let enableReportApiCall = config.get("apiStatsBatchReport.enableApiStatsBatchReport") === 'true' ||
	config.get("apiStatsBatchReport.enableApiStatsBatchReport") === true;
let sizeOfTriggerApiStatsBatchReport =
	Number(config.get("apiStatsBatchReport.sizeOfTriggerApiStatsBatchReport")) || 100;
let timeSpanOfTriggerApiStatsBatchReport =
	Number(config.get('apiStatsBatchReport.timeSpanOfTriggerApiStatsBatchReport')) || 60000;
let intervalId;

const start = exports.start = function(){
	if( intervalId )
		clearInterval(intervalId);

	intervalId = setInterval(() => {
		reportApiCallStats();
	}, timeSpanOfTriggerApiStatsBatchReport);
};
const stop = exports.stop = function(){
	clearInterval(intervalId);
	reportApiCallStats(true);
};

exports.put = function(apiCell){
	if( enableReportApiCall ){
		apiCellCache.push(apiCell);
		if( apiCellCache.length >= sizeOfTriggerApiStatsBatchReport )
			reportApiCallStats();
	}

};

tapdata.on('apiStatsBatchReport.sizeOfTriggerApiStatsBatchReport:changed', (newVal, oldValue) => {
	sizeOfTriggerApiStatsBatchReport = Number(newVal) || 100;
});
tapdata.on('apiStatsBatchReport.timeSpanOfTriggerApiStatsBatchReport:changed', (newVal, oldValue) => {
	timeSpanOfTriggerApiStatsBatchReport = Number(newVal) || 60000;
	start();
});
tapdata.on('apiStatsBatchReport.enableApiStatsBatchReport:changed', (newVal, oldValue) => {
	if( newVal === 'true' || newVal === true) {
		enableReportApiCall = true;
		start();
	} else {
		enableReportApiCall = false;
		stop();
	}
});



function reportApiCallStats(isKill) {

	let apiAuditLogs = apiCellCache;//apiCellCache.splice(0, apiCellCache.length);
	apiCellCache = [];
	if (apiAuditLogs.length > 0) {
		// 限制每次汇报的条数不超过 100 条，超过的丢弃
		if (apiAuditLogs.length > 100) {
			console.log(`API call stats batch size ${apiAuditLogs.length} exceeds limit 100, discarding ${apiAuditLogs.length - 100} records`);
			apiAuditLogs = apiAuditLogs.slice(0, 100);
		}

		apiAuditLogs.forEach((apicall) => {
			apicall.report_time = new Date().getTime();
		});
		tapdata.getToken(function (token) {
			let url = config.get("tapDataServer.url") + '/api/ApiCalls?access_token=' + token;
			axios.post(url, apiAuditLogs, {
				headers: {
					'Content-Type': 'application/json'
				},
				timeout: 5000
			}).then(response => {
				//let reported = Array.isArray(response.data) ? response.data : [response.data];
				if(isKill){
					process.exit(0)
				}
			}).catch(error => {
				if (error.response) {
					if (error.response.status === 401 || error.response.status === 403) {
						apiCellCache.push(...apiAuditLogs);
						console.error('Access token Expired');
						tapdata.removeToken();
					} else {
						console.error('<EMAIL>:47:', error.response.data);
					}
				} else {
					console.error('<EMAIL>:47:', error.message);
				}
				if(isKill){
					process.exit(0)
				}
			});
		});

	}
}
