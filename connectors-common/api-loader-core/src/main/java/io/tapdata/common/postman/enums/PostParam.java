package io.tapdata.common.postman.enums;

public class PostParam {
    public static final String VARIABLE = "variable";

    public static final String INFO = "info";
    public static final String _POSTMAN_ID = "_postman_id";
    public static final String NAME = "name";
    public static final String DESCRIPTION = "description";
    public static final String SCHEMA = "schema";

    public static final String ITEM = "item";
    public static final String ID = "id";
    public static final String REQUEST = "request";
    public static final String METHOD = "method";
    public static final String MODE = "mode";
    public static final String URL = "url";
    public static final String RAW = "raw";
    public static final String HOST = "host";
    public static final String PATH = "path";
    public static final String QUERY = "query";
    public static final String HEADER = "header";
    public static final String AUTH = "auth";
    public static final String RESPONSE = "response";
    public static final String DISABLED = "disabled";
    public static final String OPTIONS = "options";
    public static final String BODY = "body";
    public static final String GRAPHQL = "graphql";
    public static final String VARIABLES = "variables";

    public static final String EVENT = "event";
    public static final String LISTEN = "listen";
    public static final String SCRIPT = "script";
    public static final String TYPE = "type";
    public static final String EXEC = "exec";

    public static final String KEY = "key";
    public static final String VALUE = "value";

}
