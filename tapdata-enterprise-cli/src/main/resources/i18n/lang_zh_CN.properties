CONNECTOR_CAPABILITIES_START= 的扫描结果显示，数据源实现了以下能力方法:%s
TOTAL_CAPABILITIES_OF=%s总共具备的能力有%s个。%s

ERROR.CASE=测试失败的用例：\n
WARN.CASE=质量警告用例：\n
SUCCEED.CASE=测试成功的用例：\n

TEST_SUCCEED_END=%s(✔)非常棒! %s 的所有测试用例全部通过测试流程!
TEST_ERROR_END=%s(╳) Oops, PDK %s 未通过所有测试，请解决以上问题，然后重试。
SUCCEED_WITH_WARN=但是部分测试结果显示为警告，请检查测试用例。
SUMMARY_END= ▣ 针对 %s 数据源进行能力质量检测，已覆盖用例总计 %s 条。\n \t◉ 累计执行用例 %s 条： 成功用例 [%s] 条 | 告警用例 [%s] 条 | 失败用例 [%s] 条。\n \t◈ 累计跳过用例 %s 条。\n

ONCE_HISTORY=%s结果：%s， 通过用例：%s， 警告用例：%s， 错误用例：%s， 跳过用例：%s

#NOT_SUPPORT_FUNCTION=检测到数据源存在未实现的方法，终止了当前用例的测试流程，未实现的方法为：%s
TEST_RESULT_SUCCEED=测试成功
TEST_RESULT_ERROR=测试失败
TEST_RESULT_WARN=测试告警
base.jumpCase.list=跳过的测试用例列表：
SUCCEED_COUNT_LABEL=通过用例：
WARN_COUNT_LABEL=告警用例：
ERROR_COUNT_LABEL=错误用例：
HAS_WARN_COUNT=但是其中有%s条用例发生了警告。
TEST_OF_SUCCEED=成功的断言
TEST_OF_WARN=告警的断言
TEST_OF_ERROR=失败的断言

test.writeRecordTest=WriteRecordFunction 写数据
test.writeRecordTest.case.sourceTest1=用例1，增删改数量返回正确
test.writeRecordTest.case.sourceTest2=用例2，多次插入相同主键的数据，插入修改数量应该正确
test.writeRecordTest.case.sourceTest3=用例3，删除不存在的数据时，删除数量应该正确
test.writeRecordTest.case.sourceTest4=用例4，修改不存在的数据，插入修改数量应该正确

Test.TestNotImplementFunErr=测试一个没有实现方法的测试类（示例）
Test.TestNotImplementFunErr.case.sourceTest=没有实现方法的测试类的测试用例（示例）

test.queryByFilterTest=QueryByFilterFunction基于匹配字段查询（依赖WriteRecordFunction）
test.queryByFilterTest.insertWithQuery=用例1，插入数据能正常查询并进行值比对
queryByFilter.insert.error=使用WriteRecordFunction插入了%s条全类型（覆盖TapType的11中类型数据）数据，目标表名称为%s。
test.queryByFilterTest.queryWithLotTapFilter=用例2，查询数据时，指定多个TapFilter，需要返回多个FilterResult，做一一对应
lotFilter.notEquals=使用WriteRecordFunction插入一条全类型数据后，使用QueryByFilter指定%s个TapFilter进行匹配，分别为：通过主键作为匹配参数生成一个TapFilter（%s=%s），在生成一个一定匹配不上的TapFilter（%s=%s），这样执行之后， 应该返回%s个FilterResult，一个是成功返回数据， 一个是失败返回空结果，实际返回结果不符合要求。
lotFilter.equals.succeed=使用WriteRecordFunction插入一条全类型数据后，使用QueryByFilter指定%s个TapFilter进行匹配，分别为：通过主键作为匹配参数生成一个TapFilter（%s=%s），在生成一个一定匹配不上的TapFilter（%s=%s），这样执行之后， 应该返回%s个FilterResult，一个是成功返回数据， 一个是失败返回空结果，实际返回结果符合要求。
lotFilter.notEquals.numError=使用WriteRecordFunction插入一条全类型数据后，使用QueryByFilter指定%s个TapFilter进行匹配，分别为：通过主键作为匹配参数生成一个TapFilter（%s=%s），在生成一个一定匹配不上的TapFilter（%s=%s），这样执行之后， 应该返回%s个FilterResult，实际返回%s个FilterResult，不符合预期。

queryByAdvanced=QueryByAdvancedFilterFunction 基于匹配字段高级查询（依赖WriteRecordFunction）
test.byAdvance.sourceTest=用例1，插入数据能正常查询并进行值比对
test.byAdvance.sourceTest2=用例2，查询数据时，通过使用TapAdvanceFilter的各种参数进行查询
queryByAdvanced.operator.succeed=通过TapAdvanceFilter的功能进行匹配，使用‘%s’操作符测试，操作%s %s %s，已查出查出结果，符合预期，测试表：%s。
queryByAdvanced.operator.error=通过TapAdvanceFilter的功能进行匹配，使用‘%s’操作符测试，操作%s %s %s，查询无结果，实际记录已插入，但未查出，不符合预期，测试表：%s。
queryByAdvanced.sort.error=通过TapAdvanceFilter的功能进行匹配，使用排序测试，更具%s字段进行%s规则排序，排序无结果，不符合预期，测试表：%s。
queryByAdvanced.sort.succeed=通过TapAdvanceFilter的功能进行匹配，使用排序测试，更具%s字段进行%s规则排序，排序有结果，符合预期，测试表：%s。
queryByAdvanced.projection.succeed=通过TapAdvanceFilter的功能进行匹配，使用Projection进行，查询结果按照%s字段输出，输出有结果，符合预期，测试表：%s。
queryByAdvanced.projection.error=通过TapAdvanceFilter的功能进行匹配，使用Projection进行，查询结果按照%s字段输出，输出无结果，符合预期，测试表：%s。
byAdvance.query.error=使用QueryByAdvanceFilterFunction，读出所有数据，已读取到%s条，不符合符合预期。
byAdvance.query.succeed=使用QueryByAdvanceFilterFunction，读出所有数据，已读取到%s条，符合预期。

recordEventExecute.insert.assert.error=您插入了%s条记录，但是插入操作未正常执行，插入失败了。
recordEventExecute.insert.assert.succeed=您插入了%s条记录，并且插入操作是成功的。
recordEventExecute.update.assert.error=您修改了%s条记录，但是修改操作失败了。
recordEventExecute.update.assert.succeed=您修改了%s条记录，并且修改操作是成功的。
recordEventExecute.delete.assert.error=您删除了%s条记录，但是删除操作失败了。
recordEventExecute.delete.assert.succeed=您删除了%s条记录，并且删除操作是成功的。
recordEventExecute.drop.table.error=删除表操作错误，删除时捕获到异常。
recordEventExecute.drop.notCatch.thrower=删除表操作过程未检出异常。
recordEventExecute.drop.error.not.support.function=请实现名为Drop Table function的函数。
recordEventExecute.drop.table.succeed=删除表操作成功，表名称为：%s 。

writeRecordTest.sourceTest2.verify.firstInsert=插入策略是update_on_exists（存在时更新），执行首次插入操作时，待插入的记录数应该是%s个,但是实际上返回记录数结果插入%s、修改%s、删除%s，插入失败。
writeRecordTest.sourceTest2.verify.firstInsert.succeed=插入策略是update_on_exists（存在时更新），执行首次插入操作时，新插入的记录数应该是%s个,实际上操作返回记录数结果也为插入%s、修改%s、删除%s，符合预期，插入成功。
wr.test2.insertAfter.notEquals=插入策略是update_on_exists（存在时更新），再次使用相同主键数据执行插入操作时，插入%s条数据，预期插入数%s、更新数%s、删除数%s。但是结果显示插入数和修改数之和不等于%s，插入%s、修改%s、删除%s，不符合预期，插入失败。
wr.test2.insertAfter.warnInsert=插入策略是update_on_exists（存在时更新），再次使用相同主键数据执行插入操作时，插入%s条数据，预期插入数%s、更新数%s、删除数%s，但是结果显示操作返回插入%s、修改%s、删除%s，不符合预期，可观测性数据可能不准确。
wr.test2.insertAfter.warnUpdate=插入策略是update_on_exists（存在时更新），再次使用相同主键数据执行插入操作时，插入%s条数据，预期插入数%s、更新数%s、删除数%s，但是结果插入操作返回插入%s、修改%s、删除%s，不符合预期，结果显示不正确。
wr.test2.insertAfter.errorOther=插入策略是update_on_exists（存在时更新），再次使用相同主键数据执行插入操作时，插入%s条数据，预期插入数%s、更新数%s、删除数%s，但是实际返回结果显示插入%s、修改%s、删除%s，不符合预期，操作失败。
wr.test2.insertAfter.succeed=插入策略是update_on_exists（存在时更新），再次使用相同主键数据执行插入操作时，执行操作插入%s条数据，预期插入数%s、更新数%s、删除数%s，结果显示插入%s、修改%s、删除%s，效果符合预期，插入成功。

wr.test2.IOE.insertAfter.succeed=插入策略是ignore_on_exists（存在时忽略），再次使用相同主键数据执行插入操作时，预期插入操作返回插入数%s、更新数%s、删除数%s，实际结果返回插入数%s、更新数%s、删除数%s。结果显示符合预期，插入操作成功。
wr.test2.IOE.insertAfter.error=插入策略是ignore_on_exists（存在时忽略），再次使用相同主键数据执行插入操作时，预期插入操作返回插入数%s、更新数%s、删除数%s，实际结果返回插入数%s、更新数%s、删除数%s，插入操作不符合预期。

wr.test3.deleteNotExist.error=删除%s条不存在的数据，且返回给引擎的插入、修改和删除都应该为0，但实际上插入%s，修改%s，删除%s。结果不在预期内，删除失败。
wr.test3.deleteNotExist.succeed=删除%s条不存在的数据，且返回给引擎的插入、修改和删除数量都为0，删除按预期成功执行.
wr.test3.deleteNotExist.catchThrowable=删除%s条不存在的数据，此时不应该报错。但实际上报错了，删除失败。
wr.test3.deleteNotExist.notThrowable=删除%s条不存在的数据，删除过程中未检出异常。

wr.test4.insertOnNotExists.error=修改策略是insert_on_nonexists（不存在时插入），修改%s条不存在的数据，此时预期插入%s、修改%s、删除%s，但实际插入%s，修改%s，删除%s，修改不符合预期。
wr.test4.insertOnNotExists.succeed=修改策略是insert_on_nonexists（不存在时插入），修改%s条不存在的数据，此时预期插入%s、修改%s、删除%s，实际插入%s，修改%s，删除%s，修改符合预期。
wr.test4.insertOnNotExists.throwable=修改策略是insert_on_nonexists（不存在时插入），修改%s条不存在的数据，此时从数据源捕获到了异常: %s,修改失败。
wr.test4.ignoreOnNotExists.error=修改策略是ignore_on_nonexists（不存在时忽略），修改%s条不存在的数据，此时预期插入%s、修改%s、删除%s，但实际为插入%s，修改%s，删除%s，修改失败。
wr.test4.ignoreOnNotExists.succeed=修改策略是ignore_on_nonexists（不存在时忽略），修改%s条不存在的数据，此时预期插入%s、修改%s、删除%s,实际插入%s，修改%s，删除%s,符合预期，修改成功。
wr.test4.ignoreOnNotExists.throwable=修改策略是ignore_on_nonexists（不存在时忽略），修改%s条不存在的数据，此时从数据源捕获到了异常: %s,修改失败。

#writeRecordWithQueryTest.sourceTest.insert.error.null=插入操作后，使用QueryByAdvanceFilter的查询结果不应为空，请检查操作是否有效。
#writeRecordWithQueryTest.sourceTest.insert.error.nullResult=插入操作后，使用QueryByAdvanceFilter的查询结果不应为空，请检查查询条件或插入是否生效。
#writeRecordWithQueryTest.sourceTest.insert.error.notEquals=插入记录后，使用QueryByAdvanceFilter的查询结果显示，查询到的记录与插入计入不一致，插入记录不成功，请检查插入记录是否生效。
#writeRecordWithQueryTest.sourceTest.insert.succeed.equals=成功插入记录，并比较插入的记录后显示插入前后记录保持了一致。

#writeRecordWithQueryTest.sourceTest.update.error.null=修改插入的记录，并对比插入前后的结果是否一致。返回为NULL。
#writeRecordWithQueryTest.sourceTest.update.error.nullResult=修改插入的记录，并对比插入前后的结果是否一致。返回结果为空。
#writeRecordWithQueryTest.sourceTest.update.error.notEquals=修改插入的记录，并对比插入前后的结果是否一致。前后结果不一致。
#writeRecordWithQueryTest.sourceTest.update.succeed.equals=修改插入的记录，并对比插入前后的结果是否一致。前后结果保持一致。

#writeRecordWithQueryTest.sourceTest.delete.error.null=删除插入的记录，并对比插入前后的结果是否一致。返回为NULL。
#writeRecordWithQueryTest.sourceTest.delete.error.notNullResult=删除插入的记录，并对比插入前后的结果是否一致。返回结果不为空,删除失败。
#writeRecordWithQueryTest.sourceTest.delete.succeed.nullResult=删除插入的记录，并对比插入前后的结果是否一致。返回结果空，删除成功。

memory_fetcher_function=MemoryFetcherFunction：此方法用于逻辑内存导出方法。
memory_fetcher_function_v2=MemoryFetcherFunctionV2：此方法用于逻辑内存导出方法（v2版）。
connection_check_function=ConnectionCheckFunction：此方法用于连接检测。
get_charsets_function=GetCharsetsFunction：此方法用于获取字符集。
command_callback_function=CommandCallbackFunction：在连接页面和节点页面由用户点击发送Command获取数据源相关数据的方法。
release_external_function=ReleaseExternalFunction：任务重置释放外部资源的方法。
query_by_filter_function=QueryByFilterFunction：此方法用于通过构建Filter查询记录。
create_table_function=CreateTableFunction：此方法用于创建表。
create_table_v2_function=CreateTableV2Function：此方法在创建表后返回创建结果。
clear_table_function=ClearTableFunction：此方法用于清空表数据但不删除表。
control_function=ControlFunction：控制事件的接收方法。
delete_index_function=DeleteIndexFunction：此方法用于删除索引。
query_indexes_function=QueryIndexesFunction：此方法用于查询索引列表。
alter_database_time_zone_function=AlterDatabaseTimeZoneFunction：基于DDL修改数据库时区方法。
alter_field_attributes_function=AlterFieldAttributesFunction：此方法用于修改表字段属性。
alter_field_name_function=AlterFieldNameFunction：此方法用于修改表字段名称。
alter_table_charset_function=AlterTableCharsetFunction：此方法用于修改表的字符集。
drop_field_function=DropFieldFunction：此方法用于删除字段属性。
new_field_function=NewFieldFunction：此方法用于新增字段属性。
raw_data_callback_filter_function=RawDataCallbackFilterFunction：Saas平台WebHook回调的方法，单个接收。
raw_data_callback_filter_function_v2=RawDataCallbackFilterFunctionV2：Saas平台WebHook回调的方法，批量接收。
process_record_function=ProcessRecordFunction：处理器处理数据方法。
batch_read_function=BatchReadFunction：此方法支持全量读取数据。
stream_read_function=StreamReadFunction：此方法支持增量读取数据。
batch_count_function=BatchCountFunction：此方法用于获取数据记录数。
timestamp_to_stream_offset_function=TimestampToStreamOffsetFunction：此方法用于通过时间戳获得增量断点。
write_record_function=WriteRecordFunction：此方法用于批量写入数据。
query_by_advance_filter_function=QueryByAdvanceFilterFunction：此方法可根据预先条件筛选查询结果。
drop_table_function=DropTableFunction：此方法用于删除表。
create_index_function=CreateIndexFunction：实现此方法后数据源可以对字段创建索引。
get_table_names_function=GetTableNamesFunction：此方法用于获取表名称。
error_handle_function=ErrorHandleFunction：出错重试的处理方法，实现这个能力可以指定相应的异常进行重试。

function.inNeed=对于这个测试而言，%s 是一个必要的方法,请在registerCapabilities方法中实现这个方法。
functions.anyOneNeed=当前测试需要依赖%s 中的任何一个方法，请保证上述方法中至少一个已在当前数据源中被实现。

#please_support_create_table_function=请支持创建表函数。
#null_after_create_table=执行建表操作失败，请检查创建表函数。
#create_table_table_not_exists=执行创建表表后，该表已存在。

notFunctions=数据源加载出现异常，为找到任何能力的实现，ConnectorFunctions为NULL。


connectionTest.test=连接测试，必测方法
connectionTest.testConnectionTest=用例1，返回恰当的测试结果
connectionTest.testConnectionTest.errorVCL=Version， Connection， Login的TestItem项没有上报。
connectionTest.testConnectionTest.succeedVCL=Version， Connection， Login的TestItem项上报成功。
connectionTest.testConnectionTest.errorBatchRead=已经实现了BatchReadFunction，但是Read没有上报时。
connectionTest.testConnectionTest.succeedBatchRead=已经实现了BatchReadFunction，Read上报成功。
connectionTest.testConnectionTest.errorStreamRead=已经实现了StreamReadFunction，但是Read log没有上报时。
connectionTest.testConnectionTest.succeedStreamRead=已经实现了StreamReadFunction，Read log上报成功。
connectionTest.testConnectionTest.errorWriteRecord=已经实现了WriteRecordFunction，但是Write没有上报时。
connectionTest.testConnectionTest.succeedWriteRecord=已经实现了StreamReadFunction，Write上报成功。

tableCount.test=tableCount表数量，必测方法
tableCount.findTableCount=用例1，查询表数量
tableCount.findTableCount.errorFun=TableCountFunction能力校验，通过调用tableCount方法之后返回表数量大于1为正确,tableCount方法调用失败，查询失败：%s。
tableCount.findTableCount.error=TableCountFunction能力校验，通过调用tableCount方法之后返回表数量大于1为正确,此时返回的表数量为%s,测试不符合预期。
tableCount.findTableCount.succeed=TableCountFunction能力校验，通过调用tableCount方法之后返回表数量大于1为正确,此时返回的表数量为%s,测试符合预期。
tableCount.findTableCountAfterNewTable=用例2，新建表之后查询表数量
tableCount.findTableCountAfterNewTable.newTable.createTableV2Function.error=数据源实现了createTableV2Function，但是调用此方法创建表失败，表名称：%s。
tableCount.findTableCountAfterNewTable.newTable.createTableV2Function.succeed=数据源实现了createTableV2Function，成功调用此方法创建表，表名称：%s。
tableCount.findTableCountAfterNewTable.newTable.createTableFunction.error=数据源实现了createTableFunction，但是调用此方法创建表失败，表名称：%s。
tableCount.findTableCountAfterNewTable.newTable.createTableFunction.succeed=数据源实现了createTableFunction，成功调用此方法创建表，表名称：%s。
tableCount.findTableCountAfterNewTable.newTable.insertForCreateTable.error=数据源未实现createTableFunction，也未实现createTableV2Function，但是通过插入数据自动建表，当前插入数据%s条，但是插入失败导致创建表失败，表名称：%s。
tableCount.findTableCountAfterNewTable.newTable.insertForCreateTable.succeed=数据源未实现createTableFunction，也未实现createTableV2Function，但是通过插入数据自动建表，当前插入数据%s条，调用此方法创建表成功，表名称：%s。
tableCount.findTableCountAfterNewTable.afterNewTable.error=创建表前获取表数量为%s,创建表后表数量为%s，创建前表数目应该比创建表后表数目小1，结果不符合预期，请检查后重试。
tableCount.findTableCountAfterNewTable.afterNewTable.succeed=创建表前获取表数量为%s,创建表后表数量为%s，创建前表数目比创建表后表数目小1，结果符合预期。
tableCount.findTableCountAfterNewTable.newTable.error=数据源不支持建表，无法进行下一步操作，测试终止。
table.create.succeed=建表操作后执行DiscoverSchema获取表成功，建表操作通过验证，建表成功，表名称：%s。
table.create.error=建表操作后执行DiscoverSchema获取表失败，建表操作未通过验证，无法获取到新建的表，建表失败，表名称：%s。

discoverSchema.test=discoverSchema发现表，必测方法
discoverSchema.discover=用例1，发现表
discoverSchema.discoverAfterCreate=用例2，建表之后能发现表（依赖CreateTableFunction）
discoverSchema.discoverByTableName1=用例3，通过指定表明加载特定表（依赖已经存在多表）
discoverSchema.discoverByTableName2=用例4，通过指定表名加载特定表（依赖CreateTableFunction）
discoverSchema.discoverByTableCount1=用例5，通过指定表数量加载固定数量的表（依赖已经存在多表）
discoverSchema.discoverByTableCount2=用例6，通过指定表数量加载固定数量的表（依赖CreateTableFunction）

discover.notAnyTable=执行discoverSchema之后，至少返回一张表,但实际结果并未返回任何一张表，测试不通过。
discover.succeed=执行discoverSchema之后，至少返回一张表,实际结果返回%s张表，测试通过。
discover.nullTable=执行discoverSchema之后，发现返回结果中存在空表，测试不通过。
discover.emptyTableName=执行discoverSchema之后，发现返回结果中存在空表名的表，测试不通过。
discover.emptyTFields=执行discoverSchema之后，存在表名称为%s的表里没有字段描述。
discover.hasWarnFields=执行discoverSchema之后，获取到的表里存在字段，但是某些字段的name或者dataType为空，具体如下：%s
discover.notWarnFields=执行discoverSchema之后，获取到的表里存在字段，没有name或者dataType为空的字段，符合预期结果。
discoverAfterCreate.notFindTargetTable=表列表里不包含随机创建的表，表名称：%s，本次DiscoverSchema耗时%sms。
discoverAfterCreate.fundTargetTable=表列表里包含随机创建的表，表名称：%s，本次DiscoverSchema耗时%sms。
discoverAfterCreate.exitsNullFiledMap=新创建的表没有任何字段信息，创建表%s失败。
discoverAfterCreate.fieldsNotEqualsCount=新创建表中字段数目与目标创建表字段数目不符合预期结果，预期查出来的表字段数应大于原表字段数，查出来的表字段数为%s，原表字段数目为%s。
discoverAfterCreate.fieldsEqualsCount=查出来的表字段数为%s，目标表字段数目为%s。字段数目符合预期。
discoverAfterCreate.allFieldNotEquals=表名称%s，创建后查出来的表中存在字段与待创建表不匹配，不匹配的内容为：待创建表中%s 与 查出来的表中%s 不匹配。
discoverAfterCreate.allFieldEquals=表名称%s，建表前后字段匹配一致，建表测试成功。

discoverByTableName1.notAnyTable=执行discoverSchema之后，至少返回一张表,但是实际未返回任何表，本次DiscoverSchema耗时%sms。
discoverByTableName1.succeed=执行discoverSchema之后，至少返回一张表,返回数目%s，获取成功，本次DiscoverSchema耗时%sms。
discoverByTableName1.notAnyTableAfter=通过指定第一张表之后的任意一张表名，当前指定第%s张表，表名称为%s，但未得到查询结果，不符合预期，本次DiscoverSchema耗时%sms。
discoverByTableName1.succeedAfter=通过指定第一张表之后的任意一张表名，当前指定第%s张表，表名称为%s，查询结果有%s张表，查询数目在预期内，本次DiscoverSchema耗时%sms。
#discoverByTableName1.notTable=通过指定第一张表之后的任意一张表名，当前指定第%s张表，表名称为%s，查询结果有%s张表，数目大于1，查询数目在预期内。
#discoverByTableName1.succeedTable=通过指定第一张表之后的任意一张表名，当前指定第%s张表，表名称为%s，查询结果有%s张表，数目等于1，测试通过。

discoverByTableName2.notAnyTable=创建的表%s与查询后的结果对比发现不符合预期，查询结果显示表数目为%s，本次DiscoverSchema耗时%sms。
discoverByTableName2.succeed=创建的表%s与查询后的结果对比发现符合预期，查询结果显示表数目为%s，本次DiscoverSchema耗时%sms。
discoverByTableName2.notEqualsTable=创建的表%s与查询后的结果对比发现不符合预期，查询结果表名称为%,名称不相同，本次DiscoverSchema耗时%sms。
discoverByTableName2.equalsTable=创建的表%s与查询后的结果对比发现符合预期，查询结果表名称为%，名称一致，测试通过，本次DiscoverSchema耗时%sms。

discoverByTableCount1.notAnyTable=执行discoverSchema之后，发现有少于1张表的返回，不符合预期，本次DiscoverSchema耗时%sms。
discoverByTableCount1.succeed=执行discoverSchema之后，发现有大于1张表的返回，符合预期，本次DiscoverSchema耗时%sms。
discoverByTableCount1.notTable=通过int tableSize参数指定为%s，再次执行discoverSchema之后，返回表数目应为%s，实际发现返回表数目为%s，不符合预期，本次DiscoverSchema耗时%sms。
discoverByTableCount1.succeedTable=通过int tableSize参数指定为%s，再次执行discoverSchema之后，发现返回表数目也为%s，符合预期，本次DiscoverSchema耗时%sms。
discoverByTableCount1.consumer.error=执行discoverSchema之后，发现有大于1张表的返回，总计返回表数%s，通过int tableSize参数指定为%s，预期每批最多接收%s个表，通过Consumer<List<TapTable>> consumer每次返最多回了%s张表为成功。实际结果显示不符合预期，第%s批返回了%s张表，本次DiscoverSchema耗时%sms。
discoverByTableCount1.consumer.succeed=执行discoverSchema之后，发现有大于1张表的返回，总计返回表数%s，通过int tableSize参数指定为%s，预期每批最多接收%s个表，通过Consumer<List<TapTable>> consumer每次最多返回了%s张表为成功。实际结果显示符合预期，合计接收%s批，每批均为%s张表，本次DiscoverSchema耗时%sms。


discoverByTableCount2.consumer.error=通过CreateTableFunction另外创建%s张表，表名称：%s，通过int tableSize参数指定为%s，则通过Consumer<List<TapTable>> consumer每批返回了%s张表为成功。实际结果显示不符合预期，虽然总计返回%s张表，但是其中第%s批返回了%s张表，本次DiscoverSchema耗时%sms。
discoverByTableCount2.consumer.succeed=通过CreateTableFunction另外创建%s张表，表名称：%s，通过int tableSize参数指定为%s，则通过Consumer<List<TapTable>> consumer每批返回了%s张表为成功。实际结果显示符合预期，合计接收%s批，每批均为%s张表，本次DiscoverSchema耗时%sms。
discoverByTableCount2.error=通过CreateTableFunction另外创建一张表（表ID：%s）后，通过int tableSize参数指定为%s，执行discoverSchema之后，发现返回表数目不为%s，不符合预期，本次DiscoverSchema耗时%sms。
discoverByTableCount2.succeed=通过CreateTableFunction另外创建一张表（表ID：%s）后，通过int tableSize参数指定为%s，执行discoverSchema之后，发现返回表数目为%s，符合预期，本次DiscoverSchema耗时%sms。
#discoverByTableCount2.canNotStop=通过CreateTableFunction另外创建一张表（表ID：%s）后，通过int tableSize参数指定为%s，执行discoverSchema之后，发现返回表数目为%s,但是discoverSchema还在继续，错误的结果。

getTableNames.test=GetTableNamesFunction获得表名列表
getTableNames.discover=用例1，发现表
getTableNames.afterCreate=用例2，建表之后能发现表（依赖CreateTableFunction）
getTableNames.byCount=用例3，通过指定表数量加载固定数量的表（依赖已经存在多表）
getTableNames.discover.notAnyTable=指定GetTableNamesFunction方法之后，至少返回一张表，但实际未返回任何表，不符合预期。
getTableNames.discover.succeed=指定GetTableNamesFunction方法之后，至少返回一张表，但实际返回表%s张，符合预期。

timestamp.test=TimestampToStreamOffsetFunction基于时间戳返回增量断点
timestamp.backStreamOffset=用例1，通过时间戳能返回增量断点
timestamp.backStreamOffsetWithNull.error=方法参数Long time传null的时候不能返回当前时间的增量断点，返回为空。
timestamp.backStreamOffsetWithNull.succeed=方法参数Long time传null的时候能返回当前时间的增量断点，返回的增量时间断点为：%s，符合预期。
timestamp.backStreamOffsetWith.succeed=方法参数Long time传距离当前时间%s个小时前的时候能返回那个时间的增量断点，返回的增量时间断点为：%s，符合预期。
timestamp.backStreamOffsetWith.error=方法参数Long time传距离当前时间%s个小时前的时候不能能返回那个时间的增量断点，返回为空。
timestamp.backStreamOffsetWith.throwable=方法参数Long time传距离当前时间%s个小时前的时候未能返回那个时间的增量断点，执行过程捕获到了异常，请检查方法实现过程，异常线索为：%s。


createTableTest.test=CreateTableFunction/CreateTableV2Function建表
createTableV2=用例1，CreateTableFunction已过期，应使用CreateTableV2Function
allTapType=用例2，使用TapType全类型11个类型推演建表测试
addIndex=用例3，建表时增加索引信息进行测试
tableIfExist=用例4，建表时表是否存在的测试
createTable.null=数据源未实现CreateTableFunction，无法通过CreateTableFunction建表，待建表名称为：%s。
createTable.notNull=数据源实现了CreateTableFunction，可以通过CreateTableFunction建表，待建表名称为：%s。
createTable.v2Null=数据源未实现CreateTableV2Function，无法通过CreateTableV2Function建表，待建表名称为：%s。
createTable.v2NotNull=数据源实现了CreateTableFunction，可以通过CreateTableV2Function建表，待建表名称为：%s。
verifyTableIsCreated.error=使用DiscoverSchema指定表名称%s查询表，查询结果%s，预期大于0，不符合预期值。通过%s方法建表失败，需要创建表的表名称为：%s。
verifyTableIsCreated.succeed=使用DiscoverSchema指定表名称%s查询表，查询结果%s，预期大于0，符合预期值。通过%s方法建表成功，已创建表的表名称为：%s。
tableIfExists.error=随机生成表名%s，调用CreateTableV2Function方法进行建表，返回的CreateTableOptions#tableExists应该是为false，但实际不为false。
tableIfExists.succeed=随机生成表名%s，调用CreateTableV2Function方法进行建表，返回的CreateTableOptions#tableExists应该是为false，且为false。
tableIfExists.again.error=再次使用相同表名%s进行建表，返回的CreateTableOptions#tableExists应该为true，但实际不为true。
tableIfExists.again.succeed=再次使用相同表名%s进行建表，返回的CreateTableOptions#tableExists应该为true，且为true。
createIndex.notFieldMap=模型推演失败，未生成表名称为%s表的TapTable，字段集合为空。
createIndex.notSuchField=模型推演失败，未在表%s中找到属性%s，推演结果不符合预期。
createIndex.noiImplement.createIndexFun=数据源未实现CreateIndexFunction，测试流程无法继续，测试已终止。
createIndex.succeed=索引创建已执行成功（%s），目标表：%s，下一步检查索引是否匹配。
createIndex.error=索引创建执行失败（%s），执行过程抛出异常，请检查数据源CreateIndexFunction的实现步骤重新测试，目标表：%s。
createIndex.discoverSchema.error=discoverSchema执行失败，未检查到创建表的表信息，待创建表为%s。
createIndex.discoverSchema.tooMany.error=discoverSchema执行失败，需要获取%s张表，实际获取%s张表，获取结果不一致，待创建表为%s。
base.indexCreate.error=检查索引是否匹配：使用DiscoverSchema获取表结构后分析，索引创建失败，失败的索引（%s），匹配不相等的索引（%s），目标表：%s。
base.succeed.createIndex=检查索引是否匹配：使用DiscoverSchema获取表结构后分析，索引匹配一致，目标表%s的索引创建成功。
base.checkIndex.after.error=检查索引是否匹配：使用DiscoverSchema获取表结构后分析，表%s创建后未获取到索引列表，请检查discoverSchema是否返回了索引列表。
base.field.contrast.error=检查字段属性：使用DiscoverSchema获取表结构后分析，模型推演结果与建表后的结果不一致，源表经过模型推演后的字段属性%s，与通过DiscoverSchema获取到的目标表中的字段属性%s。存在不一致问题，建表不符合预期，测试未通过，表名称：%s。
base.field.contrast.succeed=检查字段属性：使用DiscoverSchema获取表结构后分析，模型推演结果与建表后的结果一致，测试通过，测试表%s。
base.target.fieldDataType.null=检查字段属性：发现discoverSchema后获取的的%s表的字段%s的数据类型未知，建表过程有误或模型推演不正确！请检查操作流程。
base.source.fieldDataType.null=检查字段属性：使用DiscoverSchema获取表结构后分析，发现模型推演后的%s表的字段%s的数据类型未知，模型推演失败或者表属性定义失败！请检查操作流程。
base.targetSource.countNotEquals=检查字段属性：使用DiscoverSchema获取表结构后分析，源表字段%s个，生成的表字段%s个，源表字段数应该小于等于生成的表，前后字段数量不符合预期，表名称：%s。
base.targetFields.empty=检查字段属性：discoverSchema查询结果显示，生成的表字段列表为空，不符合预期，表名称：%s。
base.sourceFields.empty=检查字段属性：源表字段列表为空，不符合预期，表名称：%s。
createTable.allTapType.discoverSchema.error=采用随机表名建表，建表成功之后，获取表失败，discoverSchema无法获取到创建的表%s。
createTable.allTapType.discoverSchema.succeed=采用随机表名建表，建表成功之后，获取表成功，discoverSchema无法获取到创建的表%s。
base.notSupportDropTable=数据源不支持DropTableFunction，删除表操作未执行，请手动删除测试表，表名称：%s。

batchCountTest=BatchCountFunction全量记录数（依赖WriteRecordFunction）
batchCountTest.afterInsert=用例1，插入数据查询记录数
batchCountTest.insert.error=使用WriteRecordFunction写入%s条数据,实际插入%s条数据，插入不符合预期结果，操作失败。
batchCountTest.insert=使用WriteRecordFunction写入%s条数据，实际插入%s条数据，插入符合预期结果，插入成功。
batchCount.afterInsert.error=使用WriteRecordFunction写入%s条数据后，使用BatchCountFunction查询记录数为%s，返回结果不一致。
batchCount.afterInsert.succeed=使用WriteRecordFunction写入2条数据，使用BatchCountFunction查询记录数为%s，返回结果一致，测试通过。

dropTable=DropTableFunction删除表（依赖CreateTableFunction或者WriteRecordFunction）
dropTable.test=用例1，删除表测试
dropTable.error=表名称为：%s 的表移除失败。
dropTable.succeed=表名称为：%s 的表已移除成功。

clearTable=ClearTableFunction清除表数据
clearTable.test=用例1，写入数据之后再清除表
clearTable.insert.error=使用ClearTableFunction清除表数据，先WriteRecordFunction写入%s条数据，但是实际上写入了%s条记录，写入不符合预期。
clearTable.insert.succeed=使用ClearTableFunction清除表数据，先WriteRecordFunction写入%s条数据，写入符合预期。
clearTable.clean=先WriteRecordFunction写入%s条数据后，执行了一次ClearTableFunction清除表数据。
clearTable.verifyBatchCountFunction.error=数据源实现了BatchCountFunction方法，调用BatchCountFunction方法查看该表是否为%s,结果显示为%s，表明清除表数据失败；
clearTable.verifyBatchCountFunction.succeed=数据源实现了BatchCountFunction方法，调用BatchCountFunction方法查看该表是否为0,结果显示为%s，表明清除表数据成功；
clearTable.verifyQueryByAdvanceFilterFunction.succeed=实现了QueryByAdvanceFilter，查询插入的%s条数据，应该查询不到的,查询不到的结果，符合预期。
clearTable.verifyQueryByAdvanceFilterFunction.error=实现了QueryByAdvanceFilter，查询插入的%s条数据，但实际上却查到了，不符合预期。
clearTable.verifyQueryByFilterFunction.succeed=实现了QueryByFilter，查询插入的%s条数据，查询不到的结果，符合预期。
clearTable.verifyQueryByFilterFunction.error=实现了QueryByFilter，查询插入的%s条数据，但实际上却查到了，不符合预期。


batchRead=BatchReadFunction全量读数据（依赖WriteRecordFunction）
batchRead.afterInsert=用例1，写入1条数据再读出1条数据
clearTable.byBatch=用例2，写入3条数据验证分批限制是有效的
batchRead.insert.error=使用WriteRecordFunction插入%s条全类型（覆盖TapType的11中类型数据）数据，实际插入%s条，不符合预期。
batchRead.insert.succeed=使用WriteRecordFunction插入%s条全类型（覆盖TapType的11中类型数据）数据，实际插入%s条，符合预期。
batchRead.batchRead.succeed=使用WriteRecordFunction插入%s条全类型（覆盖TapType的11中类型数据）后，此时使用BatchReadFunction获取数据（batchSize为%s读出所有数据），数据条目数需要等于%s，只要能查出来数据就算是正确。已读取到%s条，符合预期。
batchRead.batchRead.error=使用WriteRecordFunction插入%s条全类型（覆盖TapType的11中类型数据）后，此时使用BatchReadFunction获取数据（batchSize为%s读出所有数据），数据条目数需要等于%s，只要能查出来数据就算是正确。但是已读取到%s条，不符合符合预期。
exact.match.filter.error=Error occurred while queryByFilter %s error %s.
exact.match.filter.result.null=Result should not be null，as the record has been inserted。
exact.match.filter.null=The filter %s can not get any result。Please make sure writeRecord method update record correctly and queryByFilter/queryByAdvanceFilter can query it out for verification.
exact.equals.failed=使用WriteRecordFunction插入的%s条全类型数据已通过BatchReadFunction方法查出，但是未通过数值匹配,匹配结果（Left指BatchRead获取前的结果，Right指BatchRead获取后的结果）：\n\t%s
exact.equals.succeed=使用WriteRecordFunction插入的%s条全类型数据已通过BatchReadFunction方法查出，已通过数值匹配，测试通过。
exact.match.failed=使用WriteRecordFunction插入的%s条全类型数据已通过BatchReadFunction方法查出，既未通过精确匹配又未通过模糊匹配，测试不符合预期。\n精确匹配结果如下（Left指BatchRead获取前的结果，Right指BatchRead获取后的结果）：\n\t%s。
exact.match.succeed=使用WriteRecordFunction插入的%s条全类型数据已通过BatchReadFunction方法查出，未通过精确匹配但可以通过模糊匹配，测试符合预期。精确匹配结果如下（Left指BatchRead获取前的结果，Right指BatchRead获取后的结果）：\n\t%s。
batchRead.tapInsertRecordEvent.null=读出的TapInsertRecordEvent不能为空，不符合预期。
batchRead.tapInsertRecordEvent.notNull=读出的TapInsertRecordEvent不为空，符合预期。
batchRead.table.null=读出的table不能为空，不符合预期。
batchRead.table.notNull=读出的table不为空，符合预期。
batchRead.time.null=读出的time不能为空，不符合预期。
batchRead.time.notNull=读出的time不为空，符合预期。
batchRead.after.null=读出的after不能为空，不符合预期。
batchRead.after.notNull=读出的after不为空，符合预期。

batchRead.batchCount.error=使用WriteRecordFunction插入%s条，然后使用BatchReadFunction（batchSize为%s）读出数据，返回数据条目数第%s批应该为%s，但实际上为%s，不符合预期。
batchRead.batchCount.succeed=使用WriteRecordFunction插入%s条，然后使用BatchReadFunction（batchSize为%s）读出数据，返回数据条目数第%s批预期为%s，实际结果为%s，符合预期。
batchRead.final.error=使用WriteRecordFunction插入的%s条全类型数据已通过BatchReadFunction方法查出，但是插入的%s条数据未保持顺序或主键相同。
batchRead.final.succeed=使用WriteRecordFunction插入的%s条全类型数据已通过BatchReadFunction方法查出，插入的%s条数据保持顺序并且主键相同。


