CONNECTOR_CAPABILITIES_START= 的扫描结果显示，数据源实现了以下能力方法:
TOTAL_CAPABILITIES_OF=total Capabilities of

TEST_SUCCEED_PRE=非常棒!
TEST_SUCCEED_SUF=的所有测试用例全部通过测试流程!
SUCCEED_WITH_WARN=but with some warn,please check your tests.
TEST_ERROR_SUF=未通过所有测试，请解决以上问题，然后重试。

NOT_SUPPORT_FUNCTION=检测到数据源存在未实现的方法，终止了当前用例的测试流程，未实现的方法为：
TEST_RESULT_SUCCEED=测试成功
TEST_RESULT_ERROR=测试失败
SUCCEED_COUNT_LABEL=通过用例：
WARN_COUNT_LABEL=告警用例：
ERROR_COUNT_LABEL=错误用例：
HAS_WARN_COUNT=，但是发生质量警告次数
TEST_OF_SUCCEED=成功的记录
TEST_OF_WARN=告警的记录
TEST_OF_ERROR=失败的记录


error_insert=插入操作失败，
record_into_mongodb=个记录在MongoDB执行时发生。
succeed_insert=插入操作成功，

error_update=更新操作失败，
record_on_mongodb=个记录在MongoDB执行时发生。
succeed_update=更新操作成功，

error_delete=删除操作失败，
succeed_delete=删除后操作成功，

please_support_create_table_function=请支持创建表函数。

null_after_create_table=执行建表操作失败，请检查创建表函数。
create_table_table_not_exists=执行创建表表后，该表已存在。

drop_table_error=删除表操作错误，删除时捕获到异常。
drop_not_catch_thrower=删除表操作过程未检出异常。
drop_error_not_support_function=请实现名为Drop Table function的函数。
drop_table_succeed=删除錶成功，錶名稱為