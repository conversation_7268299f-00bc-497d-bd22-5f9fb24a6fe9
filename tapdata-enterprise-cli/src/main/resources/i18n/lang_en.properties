CONNECTOR_CAPABILITIES_START='s The scanning results show that the data source implements the following functions:%s
TOTAL_CAPABILITIES_OF=%s have %s capabilities be implemented.%s
error.insert.record.into.mongodb=Write ${number}$ records failed, because ${message}$

ERROR.CASE=Case of ERROR:\n
WARN.CASE=Case of WARN:\n
SUCCEED.CASE=Case of SUCCEED:\n

TEST_SUCCEED_END=%s(✔)Congratulations! %s has passed all tests!
TEST_ERROR_END=%s(╳) Oops, PDK %s didn't pass all tests, please resolve above issue(s) and try again.
SUCCEED_WITH_WARN=but with some warn,please check your tests.
ONCE_HISTORY=%s结果：%s， 通过用例：%s， 警告用例：%s， 错误用例：%s， 跳过用例：%s

NOT_SUPPORT_FUNCTION=It is detected that there is an unimplemented method in the data source, \nand the test process of the current case is terminated. \nThe unimplemented method is:%s
TEST_RESULT_SUCCEED=Test succeed
TEST_RESULT_ERROR=Test error
SUCCEED_COUNT_LABEL=succeed:
WARN_COUNT_LABEL=warn:
ERROR_COUNT_LABEL=error:
HAS_WARN_COUNT=However, %s warning occurred in one of the use cases.
TEST_OF_SUCCEED=Test of succeed
TEST_OF_WARN=Test of warn
TEST_OF_ERROR=Test of error

Test.WriteRecordTestV2=Test about CreateTableFunctionV2
Test.WriteRecordTestV2.case.sourceTest=Test case 1 about CreateTableFunctionV2
Test.WriteRecordTest=Test about CreateTableFunction
Test.WriteRecordTest.case.sourceTest=Test case 1 about CreateTableFunction
Test.WriteRecordTest.case.sourceTest2=Test case 2 about CreateTableFunction
Test.WriteRecordWithQueryTest=Test about WriteRecordWithQueryTest
Test.WriteRecordWithQueryTest.case.sourceTest=Test case 1 about WriteRecordWithQueryTest
Test.WriteRecordWithQueryTest.case.sourceTest2=Test case 2 about WriteRecordWithQueryTest

Test.TestNotImplementFunErr=测试一个没有实现方法的测试类（示例）
Test.TestNotImplementFunErr.case.sourceTest=没有实现方法的测试类的测试用例（示例）

test.queryByFilterTest=QueryByFilterFunction基于匹配字段查询（依赖WriteRecordFunction）
test.queryByFilterTest.insertWithQuery=用例1，插入数据能正常查询并进行值比对
test.queryByFilterTest.queryWithLotTapFilter=用例2，查询数据时，指定多个TapFilter，需要返回多个FilterResult，做一一对应

RecordEventExecute.insert.assert.error=You inserted %s record, but the insertion operation failed.
RecordEventExecute.insert.assert.succeed=You inserted %s record, and the insertion operation succeed.
RecordEventExecute.update.assert.error=You modified %s records, but the modification failed.
RecordEventExecute.update.assert.succeed=You have modified %s records, and the modification is successful.
RecordEventExecute.delete.assert.error=You deleted %s records, but the deletion operation failed.
RecordEventExecute.delete.assert.succeed=You have deleted %s records, and the deletion operation is successful.
RecordEventExecute.drop.table.error=Error in deleting table. Exception caught during deletion.
RecordEventExecute.drop.notCatch.thrower=No exception was detected during the table deletion operation.
RecordEventExecute.drop.error.not.support.function=Please implement a function named Drop Table function.
RecordEventExecute.drop.table.succeed=Successfully deleted table with name: %s .

WriteRecordTest.sourceTest2.verify.firstInsert=插入策略是update_on_exists，新插入应该是插入%s个,但是实际上返回结果为%s，插入失败。
WriteRecordTest.sourceTest2.verify.firstInsert.succeed=插入策略是update_on_exists，新插入是%s个,实际上操作总数也为%s，插入成功。
WriteRecordTest.sourceTest2.verify.insertAfter.NotEquals=插入策略是update_on_exists，插入%s条数据，再次插入相同主键的%s条数据.插入策略是ignore_on_exists,但是结果显示插入数和修改数之和不等于%s，插入失败。
WriteRecordTest.sourceTest2.verify.insertAfter.WarnInsert=插入策略是update_on_exists，但是插入操作返回插入数是%s,此时插入数应为0但不为0，可观测性数据可能不准确。
WriteRecordTest.sourceTest2.verify.insertAfter.WarnUpdate=插入策略是update_on_exists，但是插入操作返回更新数是%s,此时更新数应为%s但不为%s，结果显示不正确。
WriteRecordTest.sourceTest2.verify.insertAfter.ErrorOther=插入策略是update_on_exists，插入操作返回更新数是%s,插入数为%s,此时更新数应为%s,插入数应为0，操作失败。
WriteRecordTest.sourceTest2.verify.insertAfter.Succeed=插入策略是update_on_exists，插入%s,结果显示更新%s条，插入%s条。效果符合预期，插入成功。

WriteRecordTest.sourceTest2.IOE.verify.insertAfter.succeed=插入策略是ignore_on_exists，插入操作返回插入数是%s,更新数为%s,实际结果应返回插入数0，更新数0。结果显示符合预期，插入操作成功。
WriteRecordTest.sourceTest2.IOE.verify.insertAfter.error=插入策略是ignore_on_exists，但是插入操作返回插入数是%s,更新数为%s,实际结果应返回插入数0，更新数0，插入操作失败。


WriteRecordWithQueryTest.sourceTest.insert.error.null=Query results should be not null.
WriteRecordWithQueryTest.sourceTest.insert.succeed.notNull=Succeed query by advance when insert record,the filter Results not null.
WriteRecordWithQueryTest.sourceTest.insert.error.nullResult=Query results should be not null.
WriteRecordWithQueryTest.sourceTest.insert.succeed.notNullResult=Succeed query by advance when insert record,the filter Results not empty results.
WriteRecordWithQueryTest.sourceTest.insert.error.notEquals=Succeed insert record,However, the query results show that the inserted data is inconsistent.
WriteRecordWithQueryTest.sourceTest.insert.succeed.equals=Succeed insert record, and the inserted record was compared successfully.

WriteRecordWithQueryTest.sourceTest.update.error.null=Modify the inserted record and compare the results before and after the insertion. Discovery query returned null.
WriteRecordWithQueryTest.sourceTest.update.succeed.notNull=Modify the inserted record and compare the results before and after the insertion. The query return is not null.
WriteRecordWithQueryTest.sourceTest.update.error.nullResult=Modify the inserted record and compare the results before and after the insertion. The query result returned is null.
WriteRecordWithQueryTest.sourceTest.update.succeed.notNullResult=Modify the inserted record and compare the results before and after the insertion. The query result returned is not null.
WriteRecordWithQueryTest.sourceTest.update.error.notEquals=Modify the inserted record and compare the results before and after the insertion. Inconsistent results.
WriteRecordWithQueryTest.sourceTest.update.succeed.equals=Modify the inserted record and compare the results before and after the insertion. Consistent results.

WriteRecordWithQueryTest.sourceTest.delete.error.null=Deleted the inserted record and compare the results before and after the insertion. Discovery query returned null.
WriteRecordWithQueryTest.sourceTest.delete.succeed.notNull=Deleted the inserted record and compare the results before and after the insertion. The query return is not null.
WriteRecordWithQueryTest.sourceTest.delete.succeed.nullResult=Deleted the inserted record and compare the results before and after the insertion. The query result returned is null and deleted is succeed.
WriteRecordWithQueryTest.sourceTest.delete.error.notNullResult=Deleted the inserted record and compare the results before and after the insertion. The query result returned is not null,but deleted is not succeed.

batch_read_function=%s%s.batchReadFunction:This method supports reading data in batches.%s
stream_read_function=%s%s.streamReadFunction:This method supports incremental data reading.%s
batch_count_function=%s%s.batchCountFunction:This method supports obtaining the total number of data records.%s
timestamp_to_stream_offset_function=%s%s.timestampToStreamOffsetFunction:%s
write_record_function=%s%s.writeRecordFunction:This method supports batch writing.%s
query_by_advance_filter_function=%s%s.queryByAdvanceFilterFunction:This method support query data by advance filter.%s
drop_table_function=%s%s.dropTableFunction:This method support drop table.%s
create_index_function=%s%s.createIndexFunction:This method support create index for cloumn.%s
get_table_names_function=%s%s.getTableNamesFunction:This methos support get table name.%s
error_handle_function=%s%s.errorHandleFunction:This method support handle error and retry.%s

please_support_create_table_function=Please support create table function.

null_after_create_table=Exec create table table function error,please check the create table function.
create_table_table_not_exists=After exec create table table ,the table is exists.

connectionTest.test=连接测试，必测方法
connectionTest.testConnectionTest=用例1，返回恰当的测试结果
connectionTest.testConnectionTest.errorVCL=Version， Connection， Login的TestItem项没有上报。
connectionTest.testConnectionTest.succeedVCL=Version， Connection， Login的TestItem项上报成功。
connectionTest.testConnectionTest.errorBatchRead=已经实现了BatchReadFunction，但是Read没有上报时。
connectionTest.testConnectionTest.succeedBatchRead=已经实现了BatchReadFunction，Read上报成功。
connectionTest.testConnectionTest.errorStreamRead=已经实现了StreamReadFunction，但是Read log没有上报时。
connectionTest.testConnectionTest.succeedStreamRead=已经实现了StreamReadFunction，Read log上报成功。
connectionTest.testConnectionTest.errorWriteRecord=已经实现了WriteRecordFunction，但是Write没有上报时。
connectionTest.testConnectionTest.succeedWriteRecord=已经实现了StreamReadFunction，Write上报成功。