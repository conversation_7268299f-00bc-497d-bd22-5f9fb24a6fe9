CONNECTOR_CAPABILITIES_START= 的掃描結果顯示數據源實現了如下方法:%s
TOTAL_CAPABILITIES_OF=%s總計具备的能力有%s个。%s

ERROR.CASE=測試失敗的用例：\n
WARN.CASE=質量警告用例：\n
SUCCEED.CASE=测试成功的用例：\n

TEST_SUCCEED_END=%s(✔)非常棒! %s 的所有測試用例全部通過測試流程！
TEST_ERROR_END=%s(╳) Oops, PDK %s 未通過所有測試，請解决以上問題，然後重試。
SUCCEED_WITH_WARN=但是部分測試結果顯示為警告，請檢查測試用例。

ONCE_HISTORY=%s结果：%s， 通过用例：%s， 警告用例：%s， 错误用例：%s， 跳过用例：%s

NOT_SUPPORT_FUNCTION=檢測到資料來源存在未實現的方法，終止了當前用例的測試流程，未實現的方法為：%s
TEST_RESULT_SUCCEED=測試成功
TEST_RESULT_ERROR=測試失敗
SUCCEED_COUNT_LABEL=通過用例：
WARN_COUNT_LABEL=告警用例：
ERROR_COUNT_LABEL=錯誤用例：
HAS_WARN_COUNT=但是其中有%s條用例發生了警告。
TEST_OF_SUCCEED=成功的記錄
TEST_OF_WARN=告警的記錄
TEST_OF_ERROR=失敗的記錄

Test.WriteRecordTest=对CreateTableFunction进行测试
Test.WriteRecordTest.case.sourceTest=对 CreateTableFunction 进行的测试用例一
Test.WriteRecordTest.case.sourceTest2=对 CreateTableFunction 进行的测试用例二
Test.WriteRecordWithQueryTest=对WriteRecordWithQueryTest进行测试
Test.WriteRecordWithQueryTest.case.sourceTest=对 WriteRecordWithQueryTest 进行的测试用例一
Test.WriteRecordWithQueryTest.case.sourceTest2=对 WriteRecordWithQueryTest 进行的测试用例二

Test.TestNotImplementFunErr=测试一个没有实现方法的测试类（示例）
Test.TestNotImplementFunErr.case.sourceTest=没有实现方法的测试类的测试用例（示例）

test.queryByFilterTest=QueryByFilterFunction基于匹配字段查询（依赖WriteRecordFunction）
test.queryByFilterTest.insertWithQuery=用例1，插入数据能正常查询并进行值比对
test.queryByFilterTest.queryWithLotTapFilter=用例2，查询数据时，指定多个TapFilter，需要返回多个FilterResult，做一一对应

RecordEventExecute.insert.assert.error=您插入了%s條記錄，但是插入操作失敗了。
RecordEventExecute.insert.assert.succeed=您插入了%s條記錄，并且插入操作是成功的。
RecordEventExecute.update.assert.error=您修改了%s条记录，但是修改操作失败了。
RecordEventExecute.update.assert.succeed=您修改了%s条记录，并且修改操作是成功的。
RecordEventExecute.delete.assert.error=您删除了%s条记录，但是删除操作失败了。
RecordEventExecute.delete.assert.succeed=您删除了%s条记录，并且删除操作是成功的。
RecordEventExecute.drop.table.error=删除表操作错误，删除时捕获到异常。
RecordEventExecute.drop.notCatch.thrower=删除表操作过程未检出异常。
RecordEventExecute.drop.error.not.support.function=请实现名为Drop Table function的函数。
RecordEventExecute.drop.table.succeed=删除錶成功，錶名稱為：%s 。

WriteRecordTest.sourceTest2.verify.firstInsert=插入策略是update_on_exists，新插入应该是插入%s个,但是实际上返回结果为%s，插入失败。
WriteRecordTest.sourceTest2.verify.firstInsert.succeed=插入策略是update_on_exists，新插入是%s个,实际上操作总数也为%s，插入成功。
WriteRecordTest.sourceTest2.verify.insertAfter.NotEquals=插入策略是update_on_exists，插入%s条数据，再次插入相同主键的%s条数据.插入策略是ignore_on_exists,但是结果显示插入数和修改数之和不等于%s，插入失败。
WriteRecordTest.sourceTest2.verify.insertAfter.WarnInsert=插入策略是update_on_exists，但是插入操作返回插入数是%s,此时插入数应为0但不为0，可观测性数据可能不准确。
WriteRecordTest.sourceTest2.verify.insertAfter.WarnUpdate=插入策略是update_on_exists，但是插入操作返回更新数是%s,此时更新数应为%s但不为%s，结果显示不正确。
WriteRecordTest.sourceTest2.verify.insertAfter.ErrorOther=插入策略是update_on_exists，插入操作返回更新数是%s,插入数为%s,此时更新数应为%s,插入数应为0，操作失败。
WriteRecordTest.sourceTest2.verify.insertAfter.Succeed=插入策略是update_on_exists，插入%s,结果显示更新%s条，插入%s条。效果符合预期，插入成功。

WriteRecordTest.sourceTest2.IOE.verify.insertAfter.succeed=插入策略是ignore_on_exists，插入操作返回插入数是%s,更新数为%s,实际结果应返回插入数0，更新数0。结果显示符合预期，插入操作成功。
WriteRecordTest.sourceTest2.IOE.verify.insertAfter.error=插入策略是ignore_on_exists，但是插入操作返回插入数是%s,更新数为%s,实际结果应返回插入数0，更新数0，插入操作失败。

WriteRecordWithQueryTest.sourceTest.insert.error.null=查詢結果不應為空。
WriteRecordWithQueryTest.sourceTest.insert.succeed.notNull=插入記錄時提前査詢成功，篩檢程式結果不為空。
WriteRecordWithQueryTest.sourceTest.insert.error.nullResult=查詢結果不應為空。
WriteRecordWithQueryTest.sourceTest.insert.succeed.notNullResult=插入記錄時提前査詢成功，篩檢程式結果不為空。
WriteRecordWithQueryTest.sourceTest.insert.error.notEquals=插入記錄不成功。
WriteRecordWithQueryTest.sourceTest.insert.succeed.equals=成功插入記錄，並成功比較插入的記錄。

WriteRecordWithQueryTest.sourceTest.update.error.null=修改插入的记录，并对比插入前后的结果是否一致。返回为NULL.
WriteRecordWithQueryTest.sourceTest.update.succeed.notNull=修改插入的记录，并对比插入前后的结果是否一致。返回不为NULL.
WriteRecordWithQueryTest.sourceTest.update.error.nullResult=修改插入的记录，并对比插入前后的结果是否一致。返回结果为空。
WriteRecordWithQueryTest.sourceTest.update.succeed.notNullResult=修改插入的记录，并对比插入前后的结果是否一致。返回结果不为空。
WriteRecordWithQueryTest.sourceTest.update.error.notEquals=修改插入的记录，并对比插入前后的结果是否一致。前后结果不一致。
WriteRecordWithQueryTest.sourceTest.update.succeed.equals=修改插入的记录，并对比插入前后的结果是否一致。前后结果保持一致。

WriteRecordWithQueryTest.sourceTest.delete.error.null=删除插入的记录，并对比插入前后的结果是否一致。返回为NULL.
WriteRecordWithQueryTest.sourceTest.delete.succeed.notNull=删除插入的记录，并对比插入前后的结果是否一致。返回不为NULL.
WriteRecordWithQueryTest.sourceTest.delete.error.notNullResult=删除插入的记录，并对比插入前后的结果是否一致。返回结果不为空，删除失败。
WriteRecordWithQueryTest.sourceTest.delete.succeed.nullResult=删除插入的记录，并对比插入前后的结果是否一致。返回结果为空，删除成功。

batch_read_function=%s%s.batchReadFunction：此方法支持全量读取数据.%s
stream_read_function=%s%s.streamReadFunction：此方法支持增量读取数据.%s
batch_count_function=%s%s.batchCountFunction：此方法用于获取数据记录数.%s
timestamp_to_stream_offset_function=%s%s.timestampToStreamOffsetFunction：%s
write_record_function=%s%s.writeRecordFunction：此方法用于批量写入数据.%s
query_by_advance_filter_function=%s%s.queryByAdvanceFilterFunction：此方法可根据预先条件筛选查询结果.%s
drop_table_function=%s%s.dropTableFunction：此方法用于删除表.%s
create_index_function=%s%s.createIndexFunction：实现此方法后数据源可以对字段创建索引.%s
get_table_names_function=%s%s.getTableNamesFunction：此方法用于获取表名称.%s
error_handle_function=%s%s.errorHandleFunction：出错重试的处理方法，实现这个能力可以指定相应的异常进行重试.%s

please_support_create_table_function=請支持創建錶函數。

null_after_create_table=執行建錶操作失敗，請檢查創建錶函數。
create_table_table_not_exists=執行創建錶錶後，該錶已存在。

connectionTest.test=连接测试，必测方法
connectionTest.testConnectionTest=用例1，返回恰当的测试结果
connectionTest.testConnectionTest.errorVCL=Version， Connection， Login的TestItem项没有上报。
connectionTest.testConnectionTest.succeedVCL=Version， Connection， Login的TestItem项上报成功。
connectionTest.testConnectionTest.errorBatchRead=已经实现了BatchReadFunction，但是Read没有上报时。
connectionTest.testConnectionTest.succeedBatchRead=已经实现了BatchReadFunction，Read上报成功。
connectionTest.testConnectionTest.errorStreamRead=已经实现了StreamReadFunction，但是Read log没有上报时。
connectionTest.testConnectionTest.succeedStreamRead=已经实现了StreamReadFunction，Read log上报成功。
connectionTest.testConnectionTest.errorWriteRecord=已经实现了WriteRecordFunction，但是Write没有上报时。
connectionTest.testConnectionTest.succeedWriteRecord=已经实现了StreamReadFunction，Write上报成功。