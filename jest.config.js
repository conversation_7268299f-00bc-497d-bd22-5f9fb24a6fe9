module.exports = {
  roots: ['<rootDir>'],
  testMatch: ['**/__tests__/**/*.+(ts|js)', '**/?(*.)+(spec|test).+(ts|js)'],
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
  },
  coverageDirectory: '<rootDir>/coverage',
  collectCoverageFrom: [
    './**/*.{ts,js}',
    '!./**/*.d.ts', // Exclude declaration files
  ],
  coveragePathIgnorePatterns: [
    '/node_modules/',
  ],
  collectCoverage: true,
};
