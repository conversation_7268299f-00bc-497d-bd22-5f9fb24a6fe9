import io.tapdata.connector.iris.IrisJdbcContext;
import io.tapdata.connector.iris.config.IrisConfig;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.kit.NumberKit;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class TestBytes {
    void test() throws Exception {
        TapTable tapTable = new TapTable("test");
        tapTable.add(new TapField("w_id", "integer"));
        tapTable.add(new TapField("w_ytd", "numeric(12,2)"));
        tapTable.add(new TapField("w_tax", "numeric(4,4)"));
        tapTable.add(new TapField("w_name", "varchar(10)"));
        tapTable.add(new TapField("w_street_1", "varchar(20)"));
        tapTable.add(new TapField("w_street_2", "varchar(20)"));
        tapTable.add(new TapField("w_city", "varchar(20)"));
        tapTable.add(new TapField("w_state", "varchar(2)"));
        tapTable.add(new TapField("w_zip", "varchar(9)"));
        IrisConfig irisConfig = new IrisConfig();
        irisConfig.setHost("localhost");
        irisConfig.setPort(1972);
        irisConfig.setDatabase("jarad");
        irisConfig.setSchema("SQLUser");
        irisConfig.setUser("_SYSTEM");
        irisConfig.setPassword("Gjgj!234");
        try (IrisJdbcContext irisJdbcContext = new IrisJdbcContext(irisConfig)) {
            irisJdbcContext.query("select * from \"%SYS_Journal\".Record_List('/usr/irissys/mgr/journal/20241121.001') where address=9441384", rs -> {
                if (rs.next()) {
                    parseValue(tapTable, rs.getBytes("NewValue"));
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Map<String, Object> parseValue(TapTable table, byte[] value) {
        Map<String, Object> map = new HashMap<>();
        if (value == null) {
            return map;
        }
        int i = 0;
        Iterator<Map.Entry<String, TapField>> iterator = table.getNameFieldMap().entrySet().iterator();
        while (i < value.length && iterator.hasNext()) {
            int len = value[i];
            if (len == 1) {
                i += len;
                map.put(iterator.next().getKey(), null);
                continue;
            }
            if (i + len > value.length) {
                return null;
            }
            int type = value[i + 1];
            byte[] data = new byte[len - 2];
            System.arraycopy(value, i + 2, data, 0, len - 2);
            // 这里需要用 schema 覆盖一下 key
            Map.Entry<String, TapField> next = iterator.next();
            map.put(next.getKey(), parseTypeValue(type, data, next.getValue().getDataType()));
            i += len;
        }
        return map;
    }

    private Object parseTypeValue(int type, byte[] data, String dataType) {
        Object typeValue = null;
        switch (type) {
            // 整数, 时间一类的也是 4, 需要按照 schema 对解析出的整数做额外处理
            case 4:
                int result = 0;
                for (int i = 0; i < data.length; i++) {
                    int unsignedByte = data[i] & 0xFF;
                    result += unsignedByte << (8 * i);
                }
                typeValue = result;
                break;
            // 字符串
            case 1:
                typeValue = new String(data);
                if (dataType.startsWith("timestamp")) {
                    typeValue = LocalDateTime.parse(typeValue.toString().replace(" ", "T"));
                }
                break;
            // decimal
            case 6:
                NumberKit.reverseBytes(data);
                byte[] des = new byte[data.length - 1];
                System.arraycopy(data, 0, des, 0, data.length - 1);
                int scale = data[data.length - 1];
                if (scale >= 0) {
                    typeValue = new BigDecimal(new BigInteger(des)).multiply(BigDecimal.TEN.pow(scale));
                } else {
                    typeValue = new BigDecimal(new BigInteger(des)).divide(BigDecimal.TEN.pow(-1 * scale));
                }
                break;
            // float, double, real
            case 8:
                ByteBuffer buffer2 = ByteBuffer.wrap(data);
                buffer2.order(ByteOrder.LITTLE_ENDIAN);

                // 解析为双精度浮点数
                double number = buffer2.getDouble();
                typeValue = number;
                break;
        }
        return typeValue;
    }
}
