package io.tapdata.connector.iris.bean;

import io.tapdata.common.CommonColumn;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.kit.StringKit;

public class IrisColumn extends CommonColumn {

    public IrisColumn(DataMap dataMap) {
        super(dataMap);
        this.dataType = getDataType(dataMap); //'dataType' with precision and scale (oracle has no function)
    }

    @Override
    public TapField getTapField() {
        return new TapField(this.columnName, this.dataType)
                .pureDataType(StringKit.removeParentheses(this.pureDataType))
                .length(this.dataLength)
                .precision(this.dataPrecision)
                .scale(this.dataScale)
                .nullable(this.isNullable())
                .defaultValue(columnDefaultValue)
                .comment(this.remarks);
    }

    @Override
    protected Boolean isNullable() {
        return "YES".equals(this.nullable);
    }

    private String getDataType(DataMap dataMap) {
        String dataType = dataMap.getString("dataType");
        String dataLength = dataMap.getString("dataLength");
        String dataPrecision = dataMap.getString("dataPrecision");
        String dataScale = dataMap.getString("dataScale");
        if (dataType.contains("(")) {
            return dataType;
        } else {
            switch (dataType) {
                case "varchar":
                case "varbinary":
                    return dataType + "(" + dataLength + ")";
                case "numeric":
                    return "numeric(" + dataPrecision + "," + dataScale + ")";
                default:
                    return dataType;
            }
        }
    }
}
