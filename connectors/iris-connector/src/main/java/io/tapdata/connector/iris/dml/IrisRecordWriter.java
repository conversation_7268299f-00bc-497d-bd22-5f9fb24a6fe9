package io.tapdata.connector.iris.dml;

import io.tapdata.common.JdbcContext;
import io.tapdata.common.dml.NormalRecordWriter;
import io.tapdata.common.exception.AbstractExceptionCollector;
import io.tapdata.entity.schema.TapTable;

import java.sql.SQLException;

public class IrisR<PERSON>ordWriter extends NormalRecordWriter {

    public IrisRecordWriter(JdbcContext jdbcContext, TapTable tapTable) throws SQLException {
        super(jdbcContext, tapTable, true);
        exceptionCollector = new AbstractExceptionCollector() {
        };
        insertRecorder = new IrisWriteRecorder(connection, tapTable, jdbcContext.getConfig().getSchema());
        updateRecorder = new IrisWriteRecorder(connection, tapTable, jdbcContext.getConfig().getSchema());
        deleteRecorder = new IrisWriteRecorder(connection, tapTable, jdbcContext.getConfig().getSchema());
    }

}
