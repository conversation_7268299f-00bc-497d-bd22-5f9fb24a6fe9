package io.tapdata.connector.iris;

import io.tapdata.common.CommonDbConnector;
import io.tapdata.common.CommonSqlMaker;
import io.tapdata.connector.iris.bean.IrisColumn;
import io.tapdata.connector.iris.cdc.IrisLogMiner;
import io.tapdata.connector.iris.config.IrisConfig;
import io.tapdata.connector.iris.dml.IrisRecordWriter;
import io.tapdata.entity.codec.TapCodecsRegistry;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.dml.TapInsertRecordEvent;
import io.tapdata.entity.event.dml.TapRecordEvent;
import io.tapdata.entity.event.dml.TapUpdateRecordEvent;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.schema.value.*;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.ErrorKit;
import io.tapdata.pdk.apis.annotations.TapConnectorClass;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import io.tapdata.pdk.apis.context.TapConnectionContext;
import io.tapdata.pdk.apis.context.TapConnectorContext;
import io.tapdata.pdk.apis.entity.ConnectionOptions;
import io.tapdata.pdk.apis.entity.TestItem;
import io.tapdata.pdk.apis.entity.WriteListResult;
import io.tapdata.pdk.apis.functions.ConnectorFunctions;
import io.tapdata.pdk.apis.functions.PDKMethod;
import io.tapdata.pdk.apis.functions.connection.RetryOptions;
import io.tapdata.pdk.apis.functions.connection.TableInfo;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.sql.Date;
import java.sql.*;
import java.time.Instant;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;


@TapConnectorClass("iris-spec.json")
public class IrisConnector extends CommonDbConnector {

    protected IrisConfig irisConfig;
    protected IrisJdbcContext irisJdbcContext;
    protected IrisLogMiner irisLogMiner;
    private Map<String, String> objectLocation = new HashMap<>();
    private TimeZone timeZone;

    private void initJdbcContext(TapConnectionContext tapConnectionContext) {
        irisConfig = new IrisConfig().load(tapConnectionContext.getConnectionConfig());
        irisConfig.load(tapConnectionContext.getNodeConfig());
        commonDbConfig = irisConfig;
        commonSqlMaker = new CommonSqlMaker();
        tapLogger = tapConnectionContext.getLog();
        irisJdbcContext = new IrisJdbcContext(irisConfig);
        jdbcContext = irisJdbcContext;
        this.timeZone = TimeZone.getTimeZone("GMT" + irisConfig.getTimezone());
    }

    @Override
    public void onStart(TapConnectionContext tapConnectionContext) {
        initJdbcContext(tapConnectionContext);
    }

    @Override
    public int tableCount(TapConnectionContext connectionContext) throws SQLException {
        return jdbcContext.queryAllTables(null).size();
    }

    @Override
    public void registerCapabilities(ConnectorFunctions connectorFunctions, TapCodecsRegistry codecRegistry) {
        connectorFunctions.supportCreateTableV2(this::createTableV3);
        connectorFunctions.supportDropTable(this::dropTable);
        connectorFunctions.supportClearTable(this::clearTable);
        connectorFunctions.supportBatchCount(this::batchCount);
        connectorFunctions.supportBatchRead(this::batchReadWithoutOffset);
        connectorFunctions.supportQueryByAdvanceFilter(this::queryByAdvanceFilterWithOffset);
        connectorFunctions.supportStreamRead(this::streamRead);
        connectorFunctions.supportTimestampToStreamOffset(this::timestampToStreamOffset);
        connectorFunctions.supportWriteRecord(this::writeRecord);
        connectorFunctions.supportCreateIndex(this::createIndex);
        connectorFunctions.supportGetTableNamesFunction(this::getTableNames);
        connectorFunctions.supportGetTableInfoFunction(this::getTableInfo);
        codecRegistry.registerFromTapValue(TapDateTimeValue.class, tapDateTimeValue -> {
            if (EmptyKit.isNotNull(tapDateTimeValue.getValue().getTimeZone())) {
                tapDateTimeValue.getValue().setTimeZone(TimeZone.getTimeZone("UTC"));
            } else {
                tapDateTimeValue.getValue().setTimeZone(timeZone);
            }
            return tapDateTimeValue.getValue().isContainsIllegal() ? tapDateTimeValue.getValue().getIllegalDate() : formatTapDateTime(tapDateTimeValue.getValue(), "yyyy-MM-dd HH:mm:ss.SSSSSS");
        });
        //date类型通过jdbc读取时，会自动转换为当前时区的时间，所以设置为当前时区
        codecRegistry.registerFromTapValue(TapDateValue.class, tapDateValue -> tapDateValue.getValue().isContainsIllegal() ? tapDateValue.getValue().getIllegalDate() : tapDateValue.getValue().toInstant());
        codecRegistry.registerFromTapValue(TapTimeValue.class, tapTimeValue -> tapTimeValue.getValue().toTimeStr());
        codecRegistry.registerFromTapValue(TapYearValue.class, "smallint", TapValue::getOriginValue);
    }

    @Override
    public void getTableNames(TapConnectionContext tapConnectionContext, int batchSize, Consumer<List<String>> listConsumer) throws SQLException {
        jdbcContext.queryAllTables(list(), batchSize, listConsumer);
    }

    protected TapField makeTapField(DataMap dataMap) {
        return new IrisColumn(dataMap).getTapField();
    }

    protected RetryOptions errorHandle(TapConnectionContext tapConnectionContext, PDKMethod pdkMethod, Throwable throwable) {
        RetryOptions retryOptions = RetryOptions.create();
        return retryOptions;
    }

    @Override
    public void onStop(TapConnectionContext connectionContext) {
        ErrorKit.ignoreAnyError(() -> {
            if (EmptyKit.isNotNull(irisLogMiner)) irisLogMiner.stopMiner();
        });
        EmptyKit.closeQuietly(irisJdbcContext);
    }

    private void writeRecord(TapConnectorContext tapConnectorContext, List<TapRecordEvent> tapRecordEvents, TapTable tapTable, Consumer<WriteListResult<TapRecordEvent>> consumer) throws Throwable {
        String insertDmlPolicy = tapConnectorContext.getConnectorCapabilities().getCapabilityAlternative(ConnectionOptions.DML_INSERT_POLICY);
        if (insertDmlPolicy == null) {
            insertDmlPolicy = ConnectionOptions.DML_INSERT_POLICY_UPDATE_ON_EXISTS;
        }
        String updateDmlPolicy = tapConnectorContext.getConnectorCapabilities().getCapabilityAlternative(ConnectionOptions.DML_UPDATE_POLICY);
        if (updateDmlPolicy == null) {
            updateDmlPolicy = ConnectionOptions.DML_UPDATE_POLICY_IGNORE_ON_NON_EXISTS;
        }
        new IrisRecordWriter(irisJdbcContext, tapTable)
                .setInsertPolicy(insertDmlPolicy)
                .setUpdatePolicy(updateDmlPolicy)
                .setTapLogger(tapLogger)
                .write(tapRecordEvents, consumer, this::isAlive);
    }

    private List<String> getLogFileList() {
        String logListSql = "SELECT * FROM \"%SYS_Journal\".File_ByTimeReverseOrder() ORDER BY \"Creation Time\" DESC";
        List<String> logFileList = new ArrayList<>();
        try {
            jdbcContext.normalQuery(logListSql, resultSet -> {
                while (resultSet.next()) {
                    String name = resultSet.getString("Name");
                    logFileList.add(name);
                }
            });
        } catch (SQLException e) {
            tapLogger.error("get iris log list error", e);
        }
        return logFileList;
    }

    private String mapGlobalNodeToTable(String globalNode) {
        String globalNodeKey = globalNode.split("\\(")[0];
        if (objectLocation.containsKey(globalNodeKey)) {
            return objectLocation.get(globalNodeKey);
        }
        return null;
    }

    private List<TapEvent> parseTrans(ResultSet resultSet) throws SQLException {
        List<TapEvent> events = new ArrayList<>();
        while (true) {
            Integer type = resultSet.getInt("Type");
            if (type == 4) {
                if (!resultSet.next()) {
                    break;
                }
                continue;
            }
            if (type != 6) {
                if (!resultSet.next()) {
                    break;
                }
                continue;
            }
            if (type == 5) {
                break;
            }
            String globalNode = resultSet.getString("GlobalNode");
            String table = mapGlobalNodeToTable(globalNode);
            if (table == null) {
                continue;
            }
            Map<String, Object> oldValue = new HashMap<>();
            Map<String, Object> newValue = new HashMap<>();
            oldValue = parseValue(table, resultSet.getBytes("OldValue"));
            newValue = parseValue(table, resultSet.getBytes("NewValue"));

            if (oldValue == null && newValue != null) {
                TapInsertRecordEvent event = TapInsertRecordEvent.create();
                event.setTableId(table);
                event.setAfter(newValue);
                events.add(event);
                continue;
            }

            if (oldValue != null && newValue != null) {
                TapUpdateRecordEvent event = TapUpdateRecordEvent.create();
                event.setTableId(table);
                event.setBefore(oldValue);
                event.setAfter(newValue);
                events.add(event);
                continue;
            }
        }
        return events;
    }

    private Object parseTypeValue(int type, byte[] data) {
        Object typeValue = null;
        switch (type) {
            // 整数, 时间一类的也是 4, 需要按照 schema 对解析出的整数做额外处理
            case 4:
                int result = 0;
                for (int i = 0; i < data.length; i++) {
                    int unsignedByte = data[i] & 0xFF;
                    result += unsignedByte << (8 * i);
                }
                typeValue = result;
                break;
            // 字符串
            case 1:
                typeValue = new String(data);
                break;
            // decimal
            case 6:
                if (data.length != 4) {
                    typeValue = 0;
                }

                ByteBuffer buffer = ByteBuffer.wrap(data);
                buffer.order(ByteOrder.LITTLE_ENDIAN); // 设置为小端序
                typeValue = buffer.getFloat();
                break;
            // float, double, real
            case 8:
                ByteBuffer buffer2 = ByteBuffer.wrap(data);
                buffer2.order(ByteOrder.LITTLE_ENDIAN);

                // 解析为双精度浮点数
                double number = buffer2.getDouble();
                typeValue = number;
                break;
        }
        return typeValue;
    }

    private Map<String, Object> parseValue(String table, byte[] value) {
        Map<String, Object> map = new HashMap<>();
        if (value == null) {
            return map;
        }
        int i = 0;
        int j = 0;
        while (true) {
            if (i >= value.length) {
                break;
            }
            int len = value[i];
            int type = value[i + 1];
            byte[] data = new byte[len - 2];
            System.arraycopy(value, i + 2, data, 0, len - 2);
            // 这里需要用 schema 覆盖一下 key
            map.put("field_" + j, parseTypeValue(type, data));
            i += len;
        }
        return map;
    }

    private String getParentLocation(String parent) throws SQLException {
        if (objectLocation.containsKey(parent)) {
            return objectLocation.get(parent);
        }

        String sql = "select * FROM %Dictionary.StorageDefinition where parent='" + parent + "'";
        AtomicReference<String> location = new AtomicReference<>();
        jdbcContext.normalQuery(sql, resultSet -> {
            while (resultSet.next()) {
                location.set(resultSet.getString("DataLocation"));
            }
        });
        objectLocation.put(parent, location.get());
        return location.get();
    }

    private Long parseLogFile(String fileName, Long address, int batchSize, StreamReadConsumer consumer) throws SQLException {
        String logFileSql = "SELECT * FROM \"%SYS_Journal\".Record_List(" + fileName + ")";
        jdbcContext.normalQuery(logFileSql, resultSet -> {
            int count = 0;
            while (resultSet.next()) {
                if (resultSet.getInt("Address") < address) {
                    continue;
                }
                List<TapEvent> events = parseTrans(resultSet);
                consumer.accept(events, new HashMap<>());
            }
        });
        return 0L;
    }


    private void streamRead(TapConnectorContext tapConnectorContext, List<String> tables, Object offset, int batchSize, StreamReadConsumer consumer) throws Throwable {
        irisLogMiner = new IrisLogMiner(irisJdbcContext, tapLogger);
        irisLogMiner.init(tables, tapConnectorContext.getTableMap(), offset, batchSize, consumer);
        irisLogMiner.startMiner();
    }


    @Override
    public ConnectionOptions connectionTest(TapConnectionContext connectionContext, Consumer<TestItem> consumer) throws SQLException {
        initJdbcContext(connectionContext);
        ConnectionOptions connectionOptions = ConnectionOptions.create();
        connectionOptions.connectionString(irisConfig.getConnectionString());
        return connectionOptions;
    }

    private Object timestampToStreamOffset(TapConnectorContext tapConnectorContext, Long startTime) {
        long timestamp;
        if (EmptyKit.isNull(startTime)) {
            timestamp = System.currentTimeMillis();
        } else {
            timestamp = startTime;
        }
        StringBuilder offset = new StringBuilder();
        String logListSql = "SELECT * FROM \"%SYS_Journal\".File_ByTimeReverseOrder() WHERE \"Creation Time\"<=?  ORDER BY \"Creation Time\" DESC";
        String logOffsetSql = "SELECT Address FROM \"%SYS_Journal\".Record_List(?) WHERE TO_TIMESTAMP(TimeStamp, 'MM/DD/YYYY HH24:MI:SS')<=? ORDER BY Address DESC";
        try (
                Connection connection = irisJdbcContext.getConnection();
                PreparedStatement preparedStatementFile = connection.prepareStatement(logListSql);
                PreparedStatement preparedStatementAddress = connection.prepareStatement(logOffsetSql)
        ) {
            preparedStatementFile.setTimestamp(1, new Timestamp(timestamp - TimeZone.getDefault().getRawOffset()));
            try (ResultSet resultSetFile = preparedStatementFile.executeQuery()) {
                if (resultSetFile.next()) {
                    String fileName = resultSetFile.getString("Name");
                    offset.append(fileName);
                    preparedStatementAddress.setString(1, fileName);
                    preparedStatementAddress.setTimestamp(2, new Timestamp(timestamp - TimeZone.getDefault().getRawOffset()));
                    try (ResultSet resultSetAddress = preparedStatementAddress.executeQuery()) {
                        if (resultSetAddress.next()) {
                            offset.append(",").append(resultSetAddress.getLong("Address"));
                        } else {
                            offset.append(",0");
                        }
                    }
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return offset.toString();
    }


    private TableInfo getTableInfo(TapConnectionContext tapConnectorContext, String tableName) {
        TableInfo tableInfo = TableInfo.create();
        return tableInfo;
    }

    @Override
    protected void processDataMap(DataMap dataMap, TapTable tapTable) {
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof Timestamp) {
                if (!tapTable.getNameFieldMap().containsKey(entry.getKey())) {
                    continue;
                }
                entry.setValue(((Timestamp) value).toLocalDateTime().minusHours(irisConfig.getZoneOffsetHour()));
            } else if (value instanceof Date) {
                entry.setValue(Instant.ofEpochMilli(((Date) value).getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime());
            } else if (value instanceof Time) {
                if (!tapTable.getNameFieldMap().containsKey(entry.getKey())) {
                    continue;
                }
                entry.setValue(Instant.ofEpochMilli(((Time) value).getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime().minusHours(irisConfig.getZoneOffsetHour()));
            }
        }
    }
}
