package io.tapdata.connector.iris.cdc;

import io.tapdata.common.cdc.NormalLogMiner;
import io.tapdata.common.cdc.NormalRedo;
import io.tapdata.common.cdc.NormalTransaction;
import io.tapdata.common.ddl.DDLFactory;
import io.tapdata.connector.iris.IrisJdbcContext;
import io.tapdata.connector.iris.config.IrisConfig;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.ddl.TapDDLEvent;
import io.tapdata.entity.event.ddl.TapDDLUnknownEvent;
import io.tapdata.entity.event.dml.TapDeleteRecordEvent;
import io.tapdata.entity.event.dml.TapInsertRecordEvent;
import io.tapdata.entity.event.dml.TapRecordEvent;
import io.tapdata.entity.event.dml.TapUpdateRecordEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.NumberKit;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

public class IrisLogMiner extends NormalLogMiner {

    private final IrisJdbcContext irisJdbcContext;
    private final IrisConfig irisConfig;
    private String logFileName;
    private Long address;
    private Connection connection;
    private PreparedStatement preparedStatementFile;
    private PreparedStatement preparedStatementLog;
    private Map<String, SchemaAndTable> objectLocation = new HashMap<>();
    private long lastEventTimestamp = 0L;

    public IrisLogMiner(IrisJdbcContext irisJdbcContext, Log tapLogger) {
        this.irisJdbcContext = irisJdbcContext;
        this.tapLogger = tapLogger;
        this.irisConfig = (IrisConfig) irisJdbcContext.getConfig();
    }

    @Override
    public void init(List<String> tableList, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.init(tableList, tableMap, offsetState, recordSize, consumer);
        makeOffset(offsetState);
        makeObjectLocation();
        isRunning.set(true);
        initRedoLogQueueAndThread();
    }

    private void makeOffset(Object offsetState) {
        if (offsetState instanceof String) {
            String[] offset = ((String) offsetState).split(",");
            if (offset.length == 2) {
                logFileName = offset[0];
                address = Long.parseLong(offset[1]);
            }
        }
    }

    private void makeObjectLocation() throws SQLException {
        irisJdbcContext.normalQuery("select b.TABLE_SCHEMA,b.TABLE_NAME,a.DataLocation FROM \"%Dictionary\".StorageDefinition a, INFORMATION_SCHEMA.TABLES b " +
                "WHERE a.parent=b.CLASSNAME and b.TABLE_SCHEMA='" + irisConfig.getSchema() + "' and b.TABLE_NAME in ('" + String.join("','", tableList) + "')", resultSet -> {
            while (resultSet.next()) {
                SchemaAndTable schemaAndTable = new SchemaAndTable(resultSet.getString("TABLE_SCHEMA"), resultSet.getString("TABLE_NAME"));
                objectLocation.put(resultSet.getString("DataLocation"), schemaAndTable);
            }
        });
    }

    protected void initRedoLogQueueAndThread() {
        if (redoLogConsumerThreadPool == null) {
            redoLogConsumerThreadPool = new ThreadPoolExecutor(2, 2, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
            redoLogConsumerThreadPool.submit(() -> {
                NormalRedo normalRedo = null;
                int commitCount = 0;
                long lastTimestamp = System.currentTimeMillis();
                while (isRunning.get()) {
                    while (ddlStop.get()) {
                        TapSimplify.sleep(1000);
                    }
                    try {
                        normalRedo = logQueue.poll(1, TimeUnit.SECONDS);
                        if (normalRedo == null) {
                            continue;
                        }
                        //commit事件过多导致延迟增加，每100个非追踪commit事件才会放行一次心跳
                        if ("COMMIT".equals(normalRedo.getOperation()) && !transactionBucket.containsKey(normalRedo.getTransactionId())) {
                            commitCount++;
                            if (commitCount > 100 || (System.currentTimeMillis() - lastTimestamp >= 1000 * 3)) {
                                commitCount = 0;
                                lastTimestamp = System.currentTimeMillis();
                            } else {
                                continue;
                            }
                        }
                    } catch (Exception e) {
                        threadException.set(e);
                        return;
                    }
                    try {
                        if (parse(normalRedo)) {
                            processOrBuffRedo(normalRedo, this::sendTransaction);
                        }

                    } catch (Throwable e) {
                        threadException.set(new RuntimeException("errorAddr: " + normalRedo.getCdcSequenceStr(), e));
                        consumer.streamReadEnded();
                        return;
                    }
                }
            });
            redoLogConsumerThreadPool.submit(() -> {
                try {
                    while (isRunning.get()) {
                        Iterator<String> iterator = transactionBucket.keySet().iterator();
                        while (iterator.hasNext()) {
                            String xid = iterator.next();
                            NormalTransaction transaction = transactionBucket.get(xid);
                            if (lastEventTimestamp - transaction.getFirstTimestamp() < irisConfig.getTransactionAliveMinutes() * 60 * 1000L) {
                                break;
                            } else {
                                tapLogger.warn("Uncommitted transaction {} with {} events will be dropped", xid, transaction.getSize());
                                transaction.clearRedoes();
                                iterator.remove();
                            }
                        }
                        int sleep = 60;
                        try {
                            while (isRunning.get() && (sleep-- > 0)) {
                                TapSimplify.sleep(1000);
                            }
                        } catch (Exception ignore) {
                        }
                    }
                } catch (Exception e) {
                    threadException.set(e);
                }
            });
        }
    }

    @Override
    public void startMiner() throws Throwable {
        connection = irisJdbcContext.getConnection();
        preparedStatementFile = connection.prepareStatement(CHECK_LOG_FILE);
        preparedStatementLog = connection.prepareStatement(CHECK_LOG_CONTENT);
        boolean hasNext;
        String nextLogFileName = logFileName;
        while (isRunning.get()) {
            if (threadException.get() != null) {
                throw threadException.get();
            }
            preparedStatementFile.clearParameters();
            preparedStatementFile.setString(1, logFileName);
            try (ResultSet resultSet = preparedStatementFile.executeQuery()) {
                if (resultSet.next()) {
                    hasNext = true;
                    nextLogFileName = resultSet.getString("Name");
                } else {
                    hasNext = false;
                }
            }
            preparedStatementLog.clearParameters();
            preparedStatementLog.setString(1, logFileName);
            preparedStatementLog.setLong(2, address);
            try (ResultSet resultSet = preparedStatementLog.executeQuery()) {
                while (isRunning.get() && resultSet.next()) {
                    address = resultSet.getLong("Address");
                    NormalRedo normalRedo = new NormalRedo();
                    int type = resultSet.getInt("Type");
                    if (type == 6 || type == 7 || type == 9) {
                        String globalNode = resultSet.getString("GlobalNode");
                        if (EmptyKit.isNull(globalNode)) {
                            continue;
                        }
                        SchemaAndTable schemaAndTable = objectLocation.get(globalNode.split("\\(")[0]);
                        if (schemaAndTable == null) {
                            continue;
                        }
                        normalRedo.setNameSpace(schemaAndTable.getSchema());
                        normalRedo.setTableName(schemaAndTable.getTable());
                    }
                    switch (type) {
                        case 4:
                            normalRedo.setOperation("BEGIN");
                            break;
                        case 5:
                            normalRedo.setOperation("COMMIT");
                            break;
                        case 6:
                            byte[] redoObj = resultSet.getBytes("NewValue");
                            byte[] undoObj = resultSet.getBytes("OldValue");
                            if (EmptyKit.isNull(redoObj) && EmptyKit.isNull(undoObj)) {
                                continue;
                            }
                            normalRedo.setRedoObj(redoObj);
                            if (EmptyKit.isNotNull(undoObj)) {
                                normalRedo.setUndoObj(undoObj);
                                normalRedo.setOperation("UPDATE");
                            } else {
                                normalRedo.setOperation("INSERT");
                            }
                            break;
                        case 7:
                            undoObj = resultSet.getBytes("OldValue");
                            normalRedo.setUndoObj(undoObj);
                            normalRedo.setOperation("DELETE");
                            break;
                        case 9:
                            redoObj = resultSet.getBytes("NewValue");
                            undoObj = resultSet.getBytes("OldValue");
                            if (EmptyKit.isNull(redoObj) && EmptyKit.isNull(undoObj)) {
                                continue;
                            }
                            normalRedo.setOperation("ROLLBACK");
                            normalRedo.setRedoObj(redoObj);
                            normalRedo.setUndoObj(undoObj);
                            break;
                        default:
                            continue;
                    }
                    normalRedo.setCdcSequenceStr(logFileName + "," + address);
                    normalRedo.setTimestamp(resultSet.getTimestamp("ts").getTime() + TimeZone.getDefault().getRawOffset());
                    normalRedo.setTransactionId(resultSet.getString("ProcessID"));
                    enqueueRedoLogContent(normalRedo);
                }
            }
            if (hasNext) {
                tapLogger.info("Switch to next log file: {}", nextLogFileName);
                logFileName = nextLogFileName;
                address = 0L;
            } else {
                TapSimplify.sleep(1000);
            }
        }
        EmptyKit.closeQuietly(preparedStatementLog);
        EmptyKit.closeQuietly(preparedStatementFile);
        EmptyKit.closeQuietly(connection);
    }

    private Object parseTypeValue(int type, byte[] data, String dataType) {
        Object typeValue = null;
        switch (type) {
            // 整数, 时间一类的也是 4, 需要按照 schema 对解析出的整数做额外处理
            case 4:
            case 5:
                NumberKit.reverseBytes(data);
                if (dataType.startsWith("timestamp")) {
                    byte[] des = new byte[data.length];
                    System.arraycopy(data, 1, des, 1, data.length - 1);
                    des[0] = (byte) ((type == 4) ? 0 : -1);
                    long micro = new BigInteger(des).longValue();
                    typeValue = LocalDateTime.ofEpochSecond(micro / 1000 / 1000, (int) (micro % 1000000L) * 1000, ZoneOffset.UTC);
                } else {
                    byte[] des = new byte[data.length + 1];
                    System.arraycopy(data, 0, des, 1, data.length);
                    des[0] = (byte) ((type == 4) ? 0 : -1);
                    typeValue = new BigDecimal(new BigInteger(des));
                }
                break;
            // 字符串
            case 1:
                typeValue = new String(data);
                if (dataType.startsWith("timestamp")) {
                    typeValue = LocalDateTime.parse(typeValue.toString().replace(" ", "T"));
                }
                break;
            // decimal
            case 6:
            case 7:
                NumberKit.reverseBytes(data);
                byte[] des = new byte[data.length];
                System.arraycopy(data, 0, des, 1, data.length - 1);
                des[0] = (byte) ((type == 6) ? 0 : -1);
                int scale = data[data.length - 1];
                if (scale >= 0) {
                    typeValue = new BigDecimal(new BigInteger(des)).multiply(BigDecimal.TEN.pow(scale));
                } else {
                    typeValue = new BigDecimal(new BigInteger(des)).divide(BigDecimal.TEN.pow(-1 * scale));
                }
                break;
            // float, double, real
            case 8:
                ByteBuffer buffer2 = ByteBuffer.wrap(data);
                buffer2.order(ByteOrder.LITTLE_ENDIAN);

                // 解析为双精度浮点数
                double number = buffer2.getDouble();
                typeValue = number;
                break;
        }
        return typeValue;
    }

    private Map<String, Object> parseValue(TapTable table, byte[] value) {
        Map<String, Object> map = new HashMap<>();
        if (value == null) {
            return map;
        }
        int i = 0;
        Iterator<Map.Entry<String, TapField>> iterator = table.getNameFieldMap().entrySet().iterator();
        while (i < value.length && iterator.hasNext()) {
            int len = value[i];
            if (len == 1) {
                i += len;
                map.put(iterator.next().getKey(), null);
                continue;
            }
            if (i + len > value.length) {
                return null;
            }
            int type = value[i + 1];
            byte[] data = new byte[len - 2];
            System.arraycopy(value, i + 2, data, 0, len - 2);
            // 这里需要用 schema 覆盖一下 key
            Map.Entry<String, TapField> next = iterator.next();
            map.put(next.getKey(), parseTypeValue(type, data, next.getValue().getDataType()));
            i += len;
        }
        return map;
    }

    private boolean parse(NormalRedo normalRedo) {
        switch (normalRedo.getOperation()) {
            case "BEGIN":
            case "COMMIT":
                return true;
            case "ROLLBACK":
                if (EmptyKit.isNotNull(normalRedo.getUndoObj())) {
                    Map<String, Object> undoRecord = parseValue(tableMap.get(normalRedo.getTableName()), (byte[]) normalRedo.getUndoObj());
                    if (EmptyKit.isNull(undoRecord)) {
                        return false;
                    }
                    normalRedo.setUndoRecord(undoRecord);
                    normalRedo.setOperation("DELETE");
                } else if (EmptyKit.isNotNull(normalRedo.getRedoObj())) {
                    Map<String, Object> redoRecord = parseValue(tableMap.get(normalRedo.getTableName()), (byte[]) normalRedo.getRedoObj());
                    if (EmptyKit.isNull(redoRecord)) {
                        return false;
                    }
                    normalRedo.setRedoRecord(redoRecord);
                    normalRedo.setOperation("INSERT");
                }
                return true;
            case "INSERT":
                if (EmptyKit.isNull(normalRedo.getRedoObj())) {
                    return false;
                }
                normalRedo.setRedoRecord(parseValue(tableMap.get(normalRedo.getTableName()), (byte[]) normalRedo.getRedoObj()));
                return true;
            case "UPDATE":
                if (EmptyKit.isNull(normalRedo.getRedoObj())) {
                    return false;
                }
                normalRedo.setRedoRecord(parseValue(tableMap.get(normalRedo.getTableName()), (byte[]) normalRedo.getRedoObj()));
                if (EmptyKit.isNull(normalRedo.getUndoObj())) {
                    return true;
                }
                normalRedo.setUndoRecord(parseValue(tableMap.get(normalRedo.getTableName()), (byte[]) normalRedo.getUndoObj()));
                return true;
            case "DELETE":
                if (EmptyKit.isNull(normalRedo.getUndoObj())) {
                    return false;
                }
                normalRedo.setUndoRecord(parseValue(tableMap.get(normalRedo.getTableName()), (byte[]) normalRedo.getUndoObj()));
                return true;
            default:
                tapLogger.info("Unknown operation: {}", normalRedo.getOperation());
        }
        return false;
    }

    @Override
    protected void ddlFlush() throws Throwable {

    }

    @Override
    protected void createEvent(NormalRedo redo, AtomicReference<List<TapEvent>> eventList, long timestamp) {
        if ("DDL".equals(Objects.requireNonNull(redo).getOperation())) {
            try {
                long referenceTime = redo.getTimestamp();
                DDLFactory.ddlToTapDDLEvent(ddlParserType, redo.getSqlRedo(),
                        DDL_WRAPPER_CONFIG,
                        tableMap,
                        tapDDLEvent -> {
                            if (!tableList.contains(tapDDLEvent.getTableId())) {
                                return;
                            }
                            tapDDLEvent.setTime(System.currentTimeMillis());
                            tapDDLEvent.setReferenceTime(referenceTime);
                            tapDDLEvent.setOriginDDL(redo.getSqlRedo());
                            eventList.get().add(tapDDLEvent);
                        });
                submitEvent(redo, eventList.get());
                ddlFlush();
            } catch (Throwable e) {
                TapDDLEvent tapDDLEvent = new TapDDLUnknownEvent();
                tapDDLEvent.setTime(System.currentTimeMillis());
                tapDDLEvent.setReferenceTime(redo.getTimestamp());
                tapDDLEvent.setOriginDDL(redo.getSqlRedo());
                eventList.get().add(tapDDLEvent);
                tapLogger.warn("DDL parse failed, [{}]", redo.getSqlRedo());
            } finally {
                ddlStop.set(false);
            }
        } else {
            Map<String, Object> streamOffset = new HashMap<>();
            streamOffset.put("scn", redo.getCdcSequenceStr());
            streamOffset.put("commitTime", redo.getTimestamp());
            TapRecordEvent recordEvent;
            switch (Objects.requireNonNull(redo).getOperation()) {
                case "INSERT": {
                    recordEvent = new TapInsertRecordEvent().init()
                            .table(redo.getTableName())
                            .after(redo.getRedoRecord());
                    break;
                }
                case "UPDATE": {
                    recordEvent = new TapUpdateRecordEvent().init()
                            .table(redo.getTableName())
                            .after(redo.getRedoRecord())
                            .before(redo.getUndoRecord());
                    break;
                }
                case "DELETE": {
                    recordEvent = new TapDeleteRecordEvent().init()
                            .table(redo.getTableName())
                            .before(redo.getUndoRecord());
                    break;
                }
                default:
                    return;
            }
            recordEvent.setReferenceTime(timestamp);
            recordEvent.addInfo("streamOffset", streamOffset);
            recordEvent.addInfo("XID", redo.getTransactionId());
            eventList.get().add(recordEvent);
        }
    }

    @Override
    protected void submitEvent(NormalRedo normalRedo, List<TapEvent> list) {
        assert normalRedo != null;
        Iterator<NormalTransaction> iterator = transactionBucket.values().iterator();
        String offset;
        if (iterator.hasNext()) {
            offset = iterator.next().getCdcSequenceStr();
        } else {
            offset = normalRedo.getCdcSequenceStr();
        }
        if (list.size() > 0) {
            consumer.accept(list, offset);
        }
    }

    @Override
    public void stopMiner() throws Throwable {
        super.stopMiner();
        TapSimplify.sleep(2000);
    }

    private static final String CHECK_LOG_FILE = "SELECT * FROM \"%SYS_Journal\".File_ByTimeReverseOrder() WHERE \"Creation Time\" > (SELECT \"Creation Time\" FROM \"%SYS_Journal\".File_ByTimeReverseOrder() WHERE Name=?) ORDER BY \"Creation Time\"";
    private static final String CHECK_LOG_CONTENT = "SELECT a.*, TO_TIMESTAMP(TimeStamp, 'MM/DD/YYYY HH24:MI:SS') AS ts FROM \"%SYS_Journal\".Record_List(?) a WHERE Type in (4,5,6,7,9) AND InTransaction = 1 AND Address > ? ORDER BY Address";

    static class SchemaAndTable {
        private String schema;
        private String table;

        public SchemaAndTable(String schema, String table) {
            this.schema = schema;
            this.table = table;
        }

        public String getSchema() {
            return schema;
        }

        public String getTable() {
            return table;
        }
    }
}
