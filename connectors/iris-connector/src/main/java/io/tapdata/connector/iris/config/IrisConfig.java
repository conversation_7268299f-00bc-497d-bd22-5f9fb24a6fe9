package io.tapdata.connector.iris.config;

import io.tapdata.common.CommonDbConfig;
import io.tapdata.kit.EmptyKit;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class IrisConfig extends CommonDbConfig {

    private Integer transactionAliveMinutes = 720;

    public IrisConfig() {
        setDbType("IRIS");
        setJdbcDriver("com.intersystems.jdbc.IRISDriver");
    }

    private static final Map<String, String> DEFAULT_PROPERTIES = new HashMap<String, String>() {{
    }};

    @Override
    public IrisConfig load(Map<String, Object> map) {
        IrisConfig config = (IrisConfig) super.load(map);
        if (EmptyKit.isNotEmpty(map)) {
            setUser(EmptyKit.isBlank(getUser()) ? (String) map.get("username") : getUser());
        }
        return config;
    }

    @Override
    public String getConnectionString() {
        return getHost() + ":" + getPort() + "/" + getDatabase();
    }

    @Override
    public String getDatabaseUrl() {
        String additionalString = getExtParams();
        additionalString = null == additionalString ? "" : additionalString.trim();
        if (additionalString.startsWith("?")) {
            additionalString = additionalString.substring(1);
        }
        StringBuilder sbURL = new StringBuilder("jdbc:").append(getDbType()).append("://").append(getHost()).append(":").append(getPort()).append("/").append(getDatabase());

        Map<String, String> properties = new HashMap<>();
        if (StringUtils.isNotBlank(additionalString)) {
            String[] additionalStringSplit = additionalString.split("&");
            for (String s : additionalStringSplit) {
                String[] split = s.split("=");
                if (split.length == 2) {
                    properties.put(split[0], split[1]);
                }
            }
        }
        for (String defaultKey : DEFAULT_PROPERTIES.keySet()) {
            if (properties.containsKey(defaultKey)) {
                continue;
            }
            properties.put(defaultKey, DEFAULT_PROPERTIES.get(defaultKey));
        }

        StringBuilder propertiesString = new StringBuilder();
        properties.forEach((k, v) -> propertiesString.append("&").append(k).append("=").append(v));

        if (propertiesString.length() > 0) {
            additionalString = StringUtils.removeStart(propertiesString.toString(), "&");
            sbURL.append("?").append(additionalString);
        }

        return sbURL.toString();
    }

    public Integer getTransactionAliveMinutes() {
        return transactionAliveMinutes;
    }

    public void setTransactionAliveMinutes(Integer transactionAliveMinutes) {
        this.transactionAliveMinutes = transactionAliveMinutes;
    }
}
