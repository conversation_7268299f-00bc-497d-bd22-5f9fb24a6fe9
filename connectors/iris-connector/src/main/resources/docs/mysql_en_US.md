## **Connection configuration help**
### **1.  MYSQL installation instructions**
Please follow the instructions below to ensure that the MySQL database is successfully added and used in Tapdata.
### **2.  Supported versions**
MySQL 5.0, 5.1, 5.5, 5.6, 5.7, 8.x
- Explanation: If using MySQL middleware connection (including but not limited to ProxySQL), the `useCursorfetch=false` attribute needs to be added to the connection parameters in the advanced settings
### **3.  Prerequisites (as source)**
#### **3.1 Enable Binlog**
-The binlog of MySQL must be enabled for Tapdata to complete the synchronization normally.
-Cascade deletion (CASCADE DELETE). This kind of deletion generated by the database will not be recorded in the binlog, so it is not supported.
Modify ` $MYSQL_ HOME/mysql. cnf `, for example:
```
server_ id         = 223344
log_ bin           = mysql-bin
expire_ logs_ days  = 1
binlog_ format     = row
binlog_ row_ image  = full
```
Configuration explanation:<br>
Server id: Each server and replication client in MySQL must be unique<br>
binlog_ Format: must be set to row or ROW<br>
binlog_ row_ Image: must be set to full<br>
expire_ logs_ Days: The number of days the binary log file is retained. It will be automatically deleted when it expires<br>
log_ Bin: basic name of binlog sequence file<br>
#### **3.2 Restart MySQL**
```
/etc/inint.d/mysqld restart
```
Verify that binlog is enabled, execute the following command in the mysql shell
```
show variables like 'binlog_ format';
```
In the output result, format value should be "ROW"
Verify binlog_ row_ Whether the value of the image parameter is full:
```
show variables like 'binlog_ row_ image';
```
In the output result, binlog_ row_ Image value should be "FULL"
#### **3.3 Create MySQL account**
After MySQL 8, the password encryption methods are different. Please pay attention to using the corresponding version and setting the password. Otherwise, incremental synchronization will not be possible
##### **3.3.1 Version 5. x**
```
create user 'username'@'localhost' identified by 'password';
```
##### **3.3.2 Version 8. x**
```
//Create user
create user 'username'@'localhost' identified with mysql_ native_ password by 'password';
//Change password
alter user 'username'@'localhost' identified with mysql_ native_ password by 'password';
```
#### **3.4 Authorization of tapdata account**
Assign select permission to a database
```
GRANT SELECT, SHOW VIEW, CREATE ROUTINE, LOCK TABLES ON <DATABASE_ NAME>.< TABLE_ NAME> TO 'tapdata' IDENTIFIED BY 'password';
```
Permissions for global
```
GRANT RELOAD, SHOW DATABASES, REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'tapdata' IDENTIFIED BY 'password';
```
#### **3.5 Constraint description**
```
When synchronizing from MySQL to other heterogeneous databases, if the source MySQL has table cascading settings, the data updates and deletions generated by the cascading trigger will not be delivered to the target. If you need to build a cascade processing capability at the target end, you can achieve this type of data synchronization through triggers and other means depending on the target situation.
```
###  **4.  Prerequisites (as targets)**
Assign all permissions to a database
```
GRANT ALL PRIVILEGES ON <DATABASE_ NAME>.< TABLE_ NAME> TO 'tapdata' IDENTIFIED BY 'password';
```
Permissions for global
```
GRANT PROCESS ON *.* TO 'tapdata' IDENTIFIED BY 'password';
```
###  **5.  Common errors**
Unknown error 1044
If the permission has been granted, but the connection cannot be tested through tapdata, you can check and repair it through the following steps
```
SELECT host,user,Grant_ priv,Super_ priv FROM mysql.user where user='username';
//View Grant_ Whether the value of priv field is Y
//If not, execute the following command
UPDATE mysql.user SET Grant_ priv='Y' WHERE user='username';
FLUSH PRIVILEGES;
```