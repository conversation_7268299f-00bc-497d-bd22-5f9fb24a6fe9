{"properties": {"name": "Iris", "icon": "icons/iris.png", "id": "iris", "doc": "${doc}", "tags": ["Database"]}, "configOptions": {"capabilities": [{"id": "dml_insert_policy", "alternatives": ["update_on_exists", "ignore_on_exists", "just_insert"]}, {"id": "dml_update_policy", "alternatives": ["ignore_on_nonexists", "insert_on_nonexists"]}, {"id": "api_server_supported"}, {"id": "source_incremental_update_event_have_before"}, {"id": "illegal_date_acceptable"}, {"id": "batch_read_hash_split"}], "supportDDL": {"events": ["new_field_event", "alter_field_name_event", "alter_field_attributes_event", "drop_field_event"]}, "connection": {"type": "object", "properties": {"host": {"type": "string", "title": "${host}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_host", "x-decorator-props": {"tooltip": "${hostTip}"}, "x-index": 10, "required": true}, "port": {"type": "string", "title": "${port}", "x-decorator": "FormItem", "x-component": "InputNumber", "default": 3306, "apiServerKey": "database_port", "x-decorator-props": {"tooltip": "${portTip}"}, "x-index": 20, "required": true}, "database": {"type": "string", "title": "${database}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_name", "x-decorator-props": {"tooltip": "${databaseTip}"}, "x-index": 30, "required": true}, "schema": {"type": "string", "title": "${schema}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_schema", "x-decorator-props": {"tooltip": "${databaseSchema}"}, "x-index": 31, "required": true}, "username": {"type": "string", "title": "${username}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_username", "x-index": 40, "required": true}, "password": {"type": "string", "title": "${password}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "database_password", "x-index": 50}, "OPTIONAL_FIELDS": {"type": "void", "properties": {"addtionalString": {"type": "string", "title": "${addtionalString}", "x-decorator": "FormItem", "x-component": "Input", "default": "useUnicode=yes&characterEncoding=UTF-8", "apiServerKey": "addtionalString", "x-decorator-props": {"tooltip": "${extParamsTip}"}, "x-index": 60}, "timezone": {"type": "string", "title": "${timezone}", "default": "", "x-decorator": "FormItem", "x-component": "Select", "x-decorator-props": {"tooltip": "${timezoneTip}"}, "x-index": 70, "enum": [{"label": "", "value": ""}, {"label": "UTC -11", "value": "-11:00"}, {"label": "UTC -10", "value": "-10:00"}, {"label": "UTC -09", "value": "-09:00"}, {"label": "UTC -08", "value": "-08:00"}, {"label": "UTC -07", "value": "-07:00"}, {"label": "UTC -06", "value": "-06:00"}, {"label": "UTC -05", "value": "-05:00"}, {"label": "UTC -04", "value": "-04:00"}, {"label": "UTC -03", "value": "-03:00"}, {"label": "UTC -02", "value": "-02:00"}, {"label": "UTC -01", "value": "-01:00"}, {"label": "UTC", "value": "+00:00"}, {"label": "UTC +01", "value": "+01:00"}, {"label": "UTC +02", "value": "+02:00"}, {"label": "UTC +03", "value": "+03:00"}, {"label": "UTC +04", "value": "+04:00"}, {"label": "UTC +05", "value": "+05:00"}, {"label": "UTC +06", "value": "+06:00"}, {"label": "UTC +07", "value": "+07:00"}, {"label": "UTC +08", "value": "+08:00"}, {"label": "UTC +09", "value": "+09:00"}, {"label": "UTC +10", "value": "+10:00"}, {"label": "UTC +11", "value": "+11:00"}, {"label": "UTC +12", "value": "+12:00"}, {"label": "UTC +13", "value": "+13:00"}, {"label": "UTC +14", "value": "+14:00"}]}}}}}}, "messages": {"default": "en_US", "en_US": {"host": "Host", "hostTip": "The address of the database, it can be an IP address or a domain name, for example: *************", "port": "Port", "portTip": "The port number of the database, the default port of Mysql is 3306", "database": "database", "databaseTip": "The name of the database, you can list all Mysql databases with the \\\\\"show databases\\\\\" command, case sensitive", "username": "username", "password": "password", "schema": "schema", "addtionalString": "Connection Parameter String", "extParamsTip": "Additional connection parameters in URI, you can write according to personalized scenarios", "timezone": "timezone", "timezoneTip": "Specify the time zone, otherwise the database time zone will be used by default", "doc": "docs/mysql_en_US.md", "syncIndex": "Sync Index", "syncIndexTip": "Enabling this capability will automatically synchronize the source index to the target. This action may impact the target database, so please enable it with caution.", "deploymentMode": "Deployment mode", "standalone": "Single machine deployment", "master-slave": "Master-Slave Architecture", "master-slaveAddress": "Server address", "master-slaveAddressTip": "The address port group that the master-slave architecture needs to fill in, the address port of the master database needs to be filled in the first line, for example: *************:3306, *************:3307", "Address": "Please enter the server address", "serverPort": "Server port", "prompt": "Add", "hashSplit": "Hash split", "hashSplitTooltip": "When the switch is turned on, it can be sharded according to the hash value, suitable for large table full-stage sharded synchronization", "maxSplit": "Maximum number of splits", "batchReadThreadSize": "Batch read thread size", "maximumQueueSize": "Maximum queue size", "maximumQueueSizeTip": "The queue size for reading incremental data in MySQL. If the downstream synchronization is slow or individual records in the table are too large, please lower this setting."}, "zh_CN": {"host": "地址", "hostTip": "数据库的地址，它可以是IP地址或者域名，例如：*************", "port": "端口", "portTip": "数据库的端口号，Mysql默认端口3306", "database": "数据库", "databaseTip": "数据库名称，可以通过 show databases 命令列出Mysql所有数据库，区分大小写", "username": "账号", "password": "密码", "schema": "模式", "addtionalString": "连接参数", "extParamsTip": "URI额外的连接参数，可以根据个性化场景书写", "timezone": "时区", "timezoneTip": "指定时区，否则默认使用数据库时区", "doc": "docs/mysql_zh_CN.md", "syncIndex": "同步索引", "syncIndexTip": "开启该能力后，会自动将源的索引同步到目标，该行为可能会对目标数据库造成影响，请谨慎开启", "deploymentMode": "部署模式", "standalone": "单机部署", "master-slave": "主从架构", "master-slaveAddress": "服务器地址", "master-slaveAddressTip": "主从架构需要填写的地址端口组，主库的地址端口需填写在第一行，例如：*************:3306, *************:3306", "Address": "服务器地址", "serverPort": "服务器端口", "prompt": "添加", "hashSplit": "哈希分片", "hashSplitTooltip": "开关打开时，可以根据哈希值进行分片，适用于大表全量阶段分片同步", "maxSplit": "最大分片数", "batchReadThreadSize": "批量读取线程数", "maximumQueueSize": "最大队列大小", "maximumQueueSizeTip": "MySql读取增量数据队列大小，如果下游同步较慢或表的单条数据过大，请调低此配置。"}, "zh_TW": {"host": "地址", "hostTip": "數據庫的地址，它可以是IP地址或者域名，例如：*************", "port": "端口", "portTip": "數據庫的端口號，Mysql默認端口3306", "database": "數據庫", "databaseTip": "數據庫名稱，可以通過 show databases 命令列出Mysql所有數據庫，區分大小寫", "username": "賬號", "password": "密碼", "addtionalString": "連接參數", "extParamsTip": "URI額外的連接參數，可以根據個性化場景書寫", "timezone": "時區", "timezoneTip": "指定時區，否則默認使用數據庫時區", "doc": "docs/mysql_zh_TW.md", "syncIndex": "同步索引", "syncIndexTip": "開啓該能力後，會自動將源的索引同步到目標，該行為可能會對目標數據庫造成影響，請謹慎開啓", "deploymentMode": "部署模式", "standalone": "單機部署", "master-slave": "主從架構", "master-slaveAddress": "服務器地址", "master-slaveAddressTip": "主從架構需要填寫的地址端口組，主庫的地址端口需填寫在第壹行，例如：*************:3306, *************:3306", "Address": "服務器地址", "serverPort": "端口", "prompt": "添加", "hashSplit": "哈希分片", "hashSplitTooltip": "開關打開時，可以根據哈希值進行分片，適用於大表全量階段分片同步", "maxSplit": "最大分片數", "batchReadThreadSize": "批量讀取線程數", "maximumQueueSize": "最大隊列大小", "maximumQueueSizeTip": "MySql 讀取增量數據隊列大小。如果下游同步較慢或表的單條數據過大，請調低此配置。"}}, "dataTypes": {"varchar[($byte)]": {"to": "TapString", "byte": 65535, "defaultByte": 1, "byteRatio": 3}, "longvarchar": {"to": "TapString", "byte": "2g", "pkEnablement": false}, "varbinary[($byte)]": {"to": "TapBinary", "byte": "2g", "defaultByte": 1}, "longvarbinary": {"to": "TapBinary", "byte": "2g", "pkEnablement": false}, "bit": {"bit": 1, "priority": 1, "to": "TapBoolean"}, "tinyint": {"to": "TapNumber", "bit": 8, "precision": 3, "value": [-128, 127]}, "smallint": {"to": "TapNumber", "bit": 16, "value": [-32768, 32767], "precision": 5}, "integer": {"to": "TapNumber", "bit": 32, "precision": 10, "value": [-2147483648, 2147483647]}, "bigint": {"to": "TapNumber", "bit": 64, "precision": 19, "value": [-9223372036854775808, 9223372036854775807]}, "numeric[($precision,$scale)]": {"to": "TapNumber", "defaultPrecision": 15, "defaultScale": 0, "precision": [1, 1000], "scale": [0, 1000], "fixed": true}, "double": {"to": "TapNumber", "precision": [1, 17], "preferPrecision": 11, "preferScale": 4, "scale": [0, 17]}, "date": {"to": "TapDate", "range": ["1000-01-01", "9999-12-31"], "pattern": "yyyy-MM-dd"}, "time": {"to": "TapTime", "fraction": [0, 6], "defaultFraction": 6, "range": ["-838:59:59", "838:59:59"], "pattern": "HH:mm:ss"}, "timestamp": {"to": "TapDateTime", "range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "defaultFraction": 6, "withTimeZone": true}}}