## **连接配置帮助**

### **1. 文件数据源前提说明**
- 由于文件数据源的特殊性，连接配置主要有文件协议特有的配置与文件路径。连接无法加载数据模型，模型需要在任务节点配置了文件类型对应需要的参数，方可加载，且目前一个连接配置仅对应一个模型。
- 文件数据源的增量读是通过文件通配扫描，只有新增文件或原文件的修改才能感知，扫描周期默认为1分钟。删除文件以及删除文件内容的数据同步，是不受支持的，且每次都是将涉及文件再次全量新增，通过更新条件字段达到修改的目的。

### **2. 支持文件协议**
以下文件协议路径分隔符均使用 "/"
#### **LOCAL**
local表示本地（引擎）所在的操作系统的文件
#### **FTP**
FTP（文件传输协议）可以设置文件服务器编码。
#### **SFTP**
SFTP（安全加密文件传输协议）可以设置文件服务器编码。Linux系统默认开启
#### **SMB**
SMB（文件共享协议）Windows系统支持的网络文件共享协议，兼容1.x,2.x,3.x。
- 特别留意：文件共享访问时，先选择共享目录，随后才是路径的填写。（共享目录/文件路径）
#### **S3FS**
S3FS（遵循S3协议文件系统）

### **3. 任务节点通用参数**
#### **模型名（Model）**
任务节点选择的文件构建的逻辑模型名称
#### **包含与排除通配（White&Black）**
通配只针对*模糊匹配，不支持正则表达式。包含置空表示所有文件，排除置空表示不排除。扫描文件逻辑为从包含匹配的文件中过滤掉排除匹配的文件。递归开关表示是否遍历子目录。

### **4. excel文件配置与用法**
支持xls、xlsx格式的excel文件解析，可以适配有合并单元格的表单，有公式输入的表单，但暂时不支持特大文件。
#### **文档密码**
Excel文件如果设置有密码，可以通过该设置解密
#### **页码范围**
如果为空，默认加载所有Sheet页。格式举例：1,3~5,8表示第1页，第3，4，5页和第8页。
#### **数据列范围**
格式举例：B~BA表示第B列到第BA列
#### **Excel表头与数据行**
Excel文件可以配置某行作为表头，也可以自己指定表头（逗号分隔）。当表头行为0表示CSV文件中没有标题行，如果同时表头为空的情况，会自动按Column1，Column2...命名。

### **5. Excel文件数据类型支持**
- STRING
- TEXT
- DOUBLE
- BOOLEAN
- DATE