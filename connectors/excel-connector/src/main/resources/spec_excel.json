{"properties": {"name": "EXCEL", "icon": "icons/excel.png", "doc": "${doc}", "id": "excel", "tags": ["File"]}, "configOptions": {"connection": {"type": "object", "properties": {"protocol": {"type": "string", "title": "${protocol}", "x-decorator": "FormItem", "x-component": "Select", "x-index": 10, "default": "local", "enum": [{"label": "LOCAL", "value": "local"}, {"label": "FTP", "value": "ftp"}, {"label": "SFTP", "value": "sftp"}, {"label": "SMB", "value": "smb"}, {"label": "S3FS", "value": "s3fs"}, {"label": "OSS", "value": "oss"}], "x-reactions": [{"target": "encoding", "fulfill": {"state": {"visible": "{{$self.value==='ftp' || $self.value==='sftp'}}"}}}, {"target": "*(ftpHost,ftpPort,ftpUsername,ftpPassword,ftpPassiveMode,ftpConnectTimeout,ftpDataTimeout)", "fulfill": {"state": {"visible": "{{$self.value==='ftp'}}"}}}, {"target": "*(sftpHost,sftpPort,sftpUsername,sftpPassword)", "fulfill": {"state": {"visible": "{{$self.value==='sftp'}}"}}}, {"target": "*(smbHost,smbUsername,smbPassword,smbShareDir)", "fulfill": {"state": {"visible": "{{$self.value==='smb'}}"}}}, {"target": "*(region)", "fulfill": {"state": {"visible": "{{$self.value==='s3fs'}}"}}}, {"target": "*(access<PERSON>ey,secretKey,endpoint,bucket)", "fulfill": {"state": {"visible": "{{$self.value==='oss' || $self.value==='s3fs'}}"}}}]}, "encoding": {"type": "string", "title": "${encoding}", "x-decorator": "FormItem", "x-component": "Select", "x-index": 20, "default": "UTF-8", "enum": [{"label": "UTF-8", "value": "UTF-8"}, {"label": "UTF-16", "value": "UTF-16"}, {"label": "GBK", "value": "GBK"}, {"label": "ASCII", "value": "ASCII"}]}, "ftpHost": {"required": true, "type": "string", "title": "${ftpHost}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "ftpHost", "x-index": 30}, "ftpPort": {"required": true, "type": "string", "title": "${ftpPort}", "default": 21, "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "ftpPort", "x-index": 40}, "ftpUsername": {"type": "string", "title": "${ftpUsername}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "ftpUsername", "x-index": 50}, "ftpPassword": {"type": "string", "title": "${ftpPassword}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "ftpPassword", "x-index": 60}, "ftpPassiveMode": {"type": "boolean", "title": "${ftpPassiveMode}", "default": true, "x-decorator": "FormItem", "x-component": "Switch", "x-index": 70}, "ftpConnectTimeout": {"required": true, "type": "string", "title": "${ftpConnectTimeout}", "default": 60, "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "ftpConnectTimeout", "x-index": 72}, "ftpDataTimeout": {"required": true, "type": "string", "title": "${ftpDataTimeout}", "default": 0, "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "ftpDataTimeout", "x-index": 75}, "sftpHost": {"required": true, "type": "string", "title": "${sftpHost}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "sftpHost", "x-index": 80}, "sftpPort": {"required": true, "type": "string", "title": "${sftpPort}", "default": 22, "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "sftpPort", "x-index": 90}, "sftpUsername": {"required": true, "type": "string", "title": "${sftpUsername}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "sftpUsername", "x-index": 100}, "sftpPassword": {"required": true, "type": "string", "title": "${sftpPassword}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "sftpPassword", "x-index": 110}, "smbHost": {"required": true, "type": "string", "title": "${smbHost}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "smbHost", "x-index": 120}, "smbUsername": {"type": "string", "title": "${smbUsername}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "smbUsername", "x-index": 130}, "smbPassword": {"type": "string", "title": "${smbPassword}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "smbPassword", "x-index": 140}, "smbShareDir": {"type": "string", "title": "${smbShareDir}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "smbShareDir", "x-index": 145}, "accessKey": {"required": true, "type": "string", "title": "${accessKey}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "accessKey", "x-index": 150}, "secretKey": {"required": true, "type": "string", "title": "${secretKey}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "secret<PERSON>ey", "x-index": 160}, "endpoint": {"required": true, "type": "string", "title": "${endpoint}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "endpoint", "x-index": 170}, "region": {"type": "string", "title": "${region}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "region", "x-index": 180}, "bucket": {"required": true, "type": "string", "title": "${bucket}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "bucket", "x-index": 190}, "filePathString": {"type": "string", "title": "${filePathString}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "filePathString", "x-index": 200}, "loadSchema": {"type": "boolean", "default": false, "x-display": "hidden"}}}, "node": {"type": "object", "properties": {"modelName": {"required": true, "type": "string", "title": "${modelName}", "x-decorator": "FormItem", "x-component": "Input", "x-decorator-props": {"tooltip": "${modelNameTooltip}"}, "apiServerKey": "modelName", "x-index": 1}, "includeRegString": {"type": "string", "title": "${includeRegString}", "default": "*.xls,*.xlsx", "x-decorator": "FormItem", "x-component": "Input", "x-decorator-props": {"tooltip": "${includeRegStringTooltip}"}, "apiServerKey": "includeRegString", "x-index": 2}, "excludeRegString": {"type": "string", "title": "${excludeRegString}", "x-decorator": "FormItem", "x-component": "Input", "x-decorator-props": {"tooltip": "${excludeRegStringTooltip}"}, "apiServerKey": "excludeRegString", "x-index": 3}, "recursive": {"type": "boolean", "title": "${recursive}", "default": true, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${recursiveTooltip}"}, "x-index": 4}, "excelPassword": {"type": "string", "title": "${excelPassword}", "x-decorator": "FormItem", "x-component": "Password", "x-decorator-props": {"tooltip": "${excelPasswordTooltip}"}, "apiServerKey": "excelPassword", "x-index": 5}, "sheetLocation": {"type": "string", "title": "${sheetLocation}", "x-decorator": "FormItem", "x-component": "Input", "x-decorator-props": {"tooltip": "${sheetLocationTooltip}"}, "apiServerKey": "sheetLocation", "x-index": 6}, "colLocation": {"required": true, "type": "string", "title": "${colLocation}", "x-decorator": "FormItem", "x-component": "Input", "x-decorator-props": {"tooltip": "${colLocationTooltip}"}, "apiServerKey": "colLocation", "x-index": 7}, "header": {"type": "string", "title": "${header}", "x-decorator": "FormItem", "x-component": "Input", "x-decorator-props": {"tooltip": "${headerTooltip}"}, "apiServerKey": "header", "x-index": 8}, "headerLine": {"type": "string", "title": "${headerLine}", "default": 1, "x-decorator": "FormItem", "x-component": "InputNumber", "x-decorator-props": {"tooltip": "${headerLineTooltip}"}, "x-index": 9}, "dataStartLine": {"required": true, "type": "string", "title": "${dataStartLine}", "default": 2, "x-decorator": "FormItem", "x-component": "InputNumber", "x-decorator-props": {"tooltip": "${dataStartLineTooltip}"}, "apiServerKey": "dataStartLine", "x-index": 10}, "justString": {"type": "boolean", "title": "${justString}", "default": true, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${justStringTooltip}"}, "x-index": 12, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}}}}, "messages": {"default": "en_US", "en_US": {"doc": "docs/excel_en_US.md", "protocol": "File Protocol", "encoding": "Charset", "ftpHost": "Host", "ftpPort": "Port", "ftpUsername": "Username", "ftpPassword": "Password", "ftpPassiveMode": "Passive Mode", "ftpConnectTimeout": "Connect Timeout(second)", "ftpDataTimeout": "Data Timeout(second)", "sftpHost": "Host", "sftpPort": "Port", "sftpUsername": "Username", "sftpPassword": "Password", "smbHost": "Host", "smbUsername": "Username", "smbPassword": "Password", "smbShareDir": "Shared Dir", "accessKey": "Access-Key", "secretKey": "Secret-Key", "endpoint": "Endpoint", "region": "Region", "bucket": "Bucket", "filePathString": "File Path", "includeRegString": "Include Reg", "excludeRegString": "Exclude Reg", "recursive": "Recursive", "excelPassword": "Excel Password", "sheetLocation": "Sheet Location", "colLocation": "Column Location", "delimiter": "Delimiter", "header": "File Header", "dataStartLine": "Data Line", "modelName": "Model Name", "headerLine": "Header Line", "justString": "All To String", "filePathStringTooltip": "The root path of all files that need to be read", "includeRegStringTooltip": "Includes wildcards, supports * and ?, takes precedence over excluding wildcards (non regular expressions) ", "excludeRegStringTooltip": "Exclude wildcards, support * and ?, exclude (non regular expressions) if wildcards are included", "recursiveTooltip": "When the directory recursion switch is turned on, files that meet the wildcard requirements will be traversed through the subdirectories", "excelPasswordTooltip": "If there is no password, you can enter it freely", "sheetLocationTooltip": "If left blank, all sheet pages will be loaded by default. If you need to specify page loading, for example, 2,4-6,8 means loading pages 2,4,5,6,8 (comma tilde should be in lowercase English) ", "colLocationTooltip": "Specify the range of data columns, such as A~BC representing columns A to BC (tilde in lowercase English) ", "headerTooltip": "Header names can be customized, separated by commas: if left blank, headers will be generated based on the file content; if not empty, header rows will be ignored, and it should be noted that if the number of headers is not enough, it will result in fewer data collection columns for Excel", "dataStartLineTooltip": "The true starting line for data collection", "modelNameTooltip": "The model name has no actual meaning, and is used to distinguish Excel models for products", "headerLineTooltip": "Header row, default to the first row, ignored when there is a custom header", "justStringTooltip": "Excel defaults to fully converting to strings. If each column of data is well organized, especially in numerical and time formats, and does not contain any strings, it can be turned off (reload the model to see the effect)"}, "zh_CN": {"doc": "docs/excel_zh_CN.md", "protocol": "文件协议", "encoding": "字符编码", "ftpHost": "地址", "ftpPort": "端口", "ftpUsername": "用户名", "ftpPassword": "口令", "ftpPassiveMode": "被动连接模式", "ftpConnectTimeout": "连接超时（秒）", "ftpDataTimeout": "传输超时（秒）", "sftpHost": "地址", "sftpPort": "端口", "sftpUsername": "用户名", "sftpPassword": "口令", "smbHost": "地址", "smbUsername": "用户名", "smbPassword": "口令", "smbShareDir": "共享目录", "accessKey": "Access-Key", "secretKey": "Secret-Key", "endpoint": "终端", "region": "域", "bucket": "桶", "filePathString": "文件路径", "includeRegString": "包含通配", "excludeRegString": "排除通配", "recursive": "目录递归", "excelPassword": "Excel文件密码", "sheetLocation": "Sheet页范围", "colLocation": "数据列范围", "header": "表头", "dataStartLine": "正文起始行", "modelName": "模型名", "headerLine": "表头行", "justString": "全转换字符串", "filePathStringTooltip": "需要读取的所有文件所在的根路径", "includeRegStringTooltip": "包含通配，支持*和?，优先于排除通配（非正则表达式）", "excludeRegStringTooltip": "排除通配，支持*和?，在包含通配的前提下排除（非正则表达式）", "recursiveTooltip": "目录递归开关打开，会向子目录遍历满足通配的文件", "excelPasswordTooltip": "若无密码，可任意输入", "sheetLocationTooltip": "若置空，默认会加载所有sheet页。如需要指定页加载，如2,4~6,8表示加载第2,4,5,6,8页（逗号波浪号注意英文小写）", "colLocationTooltip": "指定数据列的范围，如A~BC表示A列到BC列（波浪号注意英文小写）", "headerTooltip": "可以自定义表头名称，用逗号分隔：置空情况会按文件内容生成表头；非空情况将忽略表头行，且注意若表头数量不够会导致Excel的数据采集列变少", "dataStartLineTooltip": "真正的数据采集起始行", "modelNameTooltip": "模型名无实际含义，产品区分Excel模型用", "headerLineTooltip": "表头行，默认为首行，当有自定义表头时会忽略该值", "justStringTooltip": "Excel默认全转换为字符串，如果每列数据很规整，尤其为数字和时间格式，且不参杂一些字符串时，可以关闭（重新加载模型即可查看效果）"}, "zh_TW": {"doc": "docs/excel_zh_TW.md", "protocol": "檔案協定", "encoding": "字元編碼", "ftpHost": "地址", "ftpPort": "端口", "ftpUsername": "用戶名", "ftpPassword": "口令", "ftpPassiveMode": "被動連接模式", "ftpConnectTimeout": "連接超時（秒）", "ftpDataTimeout": "傳輸超時（秒）", "sftpHost": "地址", "sftpPort": "端口", "sftpUsername": "用戶名", "sftpPassword": "口令", "smbHost": "地址", "smbUsername": "用戶名", "smbPassword": "口令", "smbShareDir": "共享目錄", "accessKey": "Access-Key", "secretKey": "Secret-Key", "endpoint": "終端", "region": "域", "bucket": "桶", "filePathString": "文件路徑", "includeRegString": "包含通配", "excludeRegString": "排除通配", "recursive": "目錄遞歸", "excelPassword": "Excel文件密碼", "sheetLocation": "Sheet頁範圍", "colLocation": "數據列範圍", "header": "表頭", "dataStartLine": "正文起始行", "modelName": "模型名", "headerLine": "表頭行", "justString": "全轉換字符串", "filePathStringTooltip": "需要讀取的所有檔案所在的根路徑", "includeRegStringTooltip": "包含通配，支持*和？，優先於排除通配（非規則運算式）", "excludeRegStringTooltip": "排除通配，支持*和？，在包含通配的前提下排除（非規則運算式）", "recursiveTooltip": "目錄遞迴開關打開，會向子目錄遍歷滿足通配的檔案", "excelPasswordTooltip": "若無密碼，可任意輸入", "sheetLocationTooltip": "若置空，默認會加載所有sheet頁。如需要指定頁加載，如2,4~6,8表示加載第2,4,5,6,8頁（逗號波浪號注意英文小寫）", "colLocationTooltip": "指定數據列的範圍，如A~BC表示A列到BC列（波浪號注意英文小寫）", "headerTooltip": "可以自定義表頭名稱，用逗號分隔：置空情况會按檔案內容生成表頭；非空情况將忽略錶頭行，且注意若表頭數量不够會導致Excel的資料獲取列變少", "dataStartLineTooltip": "真正的資料獲取起始行", "modelNameTooltip": "模型名無實際含義，產品區分Excel模型用", "headerLineTooltip": "表頭行，默認為首行，當有自定義表頭時會忽略該值", "justStringTooltip": "Excel默認全轉換為字串，如果每列數據很規整，尤其為數位和時間格式，且不參雜一些字串時，可以關閉（重新加載模型即可查看效果）"}}, "dataTypes": {"DOUBLE": {"precision": [1, 1000], "scale": [0, 1000], "fixed": true, "preferPrecision": 20, "preferScale": 8, "priority": 1, "to": "TapNumber"}, "DATE": {"range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "defaultFraction": 3, "withTimeZone": false, "priority": 2, "to": "TapDateTime"}, "BOOLEAN": {"to": "TapBoolean"}, "STRING": {"byte": 200, "priority": 1, "defaultByte": 200, "preferByte": 200, "to": "TapString"}, "TEXT": {"to": "TapString"}}}